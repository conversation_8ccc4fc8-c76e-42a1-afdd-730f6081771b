/*
 * @File: axios请求封装
 * @Author: liulian
 * @Date: 2019-11-20 19:11:49
 * @version: V0.0.0.1
 */ 
import axios from 'axios';
import { message } from 'antd';

// 创建axios实例
const service = axios.create({
  timeout: 30000, // 请求超时时间
  withCredentials: true, // 跨域请求时发送cookies
});

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    const token = sessionStorage.getItem('meetWebToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    const res = response.data;
    
    // 如果是文件下载，直接返回
    if (response.config.responseType === 'blob') {
      return response;
    }
    
    // 根据业务需要处理响应
    if (res.code !== undefined && res.code !== 0 && res.code !== 200) {
      message.error(res.message || '请求失败');
      return Promise.reject(new Error(res.message || '请求失败'));
    }
    
    return res.data || res;
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error);
    
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          message.error('未授权，请重新登录');
          // 可以在这里处理登录跳转
          break;
        case 403:
          message.error('拒绝访问');
          break;
        case 404:
          message.error('请求地址出错');
          break;
        case 500:
          message.error('服务器内部错误');
          break;
        default:
          message.error(data?.message || '请求失败');
      }
    } else if (error.request) {
      message.error('网络错误，请检查网络连接');
    } else {
      message.error('请求配置错误');
    }
    
    return Promise.reject(error);
  }
);

/**
 * 创建API服务
 * @param {string} baseURL 基础URL
 * @param {function} getToken 获取token的函数
 * @param {object} apis API配置对象
 * @returns {object} API服务对象
 */
function getServices(baseURL, getToken, apis) {
  const serviceObj = {};
  
  Object.keys(apis).forEach(key => {
    const apiConfig = apis[key];
    const { url, type = 'get', getResp = false } = apiConfig;
    
    serviceObj[key] = (params = {}, config = {}) => {
      const requestConfig = {
        url: baseURL + url,
        method: type.toLowerCase(),
        ...config
      };
      
      // 添加token
      const token = getToken();
      if (token) {
        requestConfig.headers = {
          ...requestConfig.headers,
          'Authorization': `Bearer ${token}`
        };
      }
      
      // 根据请求类型设置参数
      if (type.toLowerCase() === 'get') {
        requestConfig.params = params;
      } else if (type.toLowerCase() === 'postjson') {
        requestConfig.method = 'post';
        requestConfig.data = params;
        requestConfig.headers = {
          ...requestConfig.headers,
          'Content-Type': 'application/json'
        };
      } else {
        requestConfig.data = params;
      }
      
      return service(requestConfig).then(response => {
        if (getResp) {
          return response;
        }
        return response.data || response;
      });
    };
  });
  
  return serviceObj;
}

export default getServices;
