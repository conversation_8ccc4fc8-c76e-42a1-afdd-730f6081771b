.bottom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  padding: 20px 20px calc(env(safe-area-inset-bottom) + 20px);
  display: flex;
  flex-direction: column;
  gap: 16px;
  transition: opacity 0.3s ease, transform 0.3s ease;

  // 主要控制按钮区域
  .control-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;

    .control-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      background: none;
      border: none;
      color: #ffffff;
      cursor: pointer;
      padding: 8px;
      border-radius: 12px;
      transition: all 0.2s ease;
      min-width: 60px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
        background: rgba(255, 255, 255, 0.2);
      }

      &.disabled {
        .button-icon {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.5);
          
          &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 2px;
            height: 32px;
            background: #ff4d4f;
            transform: translate(-50%, -50%) rotate(45deg);
            border-radius: 1px;
          }
        }

        .button-label {
          color: rgba(255, 255, 255, 0.5);
        }
      }

      .button-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #ffffff;
        transition: all 0.2s ease;
        position: relative;
        border: 1px solid rgba(255, 255, 255, 0.2);

        .control-button:hover & {
          background: rgba(255, 255, 255, 0.25);
          transform: scale(1.1);
        }
      }

      .button-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        text-align: center;
        line-height: 1.2;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      }
    }
  }

  // 挂断按钮区域
  .hang-up-section {
    display: flex;
    justify-content: center;

    .hang-up-button {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      background: #ff4d4f;
      border: none;
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: 
        0 4px 12px rgba(255, 77, 79, 0.4),
        0 2px 4px rgba(0, 0, 0, 0.2);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #ff6b6b 0%, #ff4d4f 100%);
        z-index: -1;
      }

      &:hover {
        transform: scale(1.1);
        box-shadow: 
          0 6px 16px rgba(255, 77, 79, 0.5),
          0 4px 8px rgba(0, 0, 0, 0.3);
      }

      &:active {
        transform: scale(0.95);
        box-shadow: 
          0 2px 8px rgba(255, 77, 79, 0.3),
          0 1px 2px rgba(0, 0, 0, 0.2);
      }

      .hang-up-icon {
        transform: rotate(135deg);
        transition: transform 0.2s ease;
      }

      &:hover .hang-up-icon {
        transform: rotate(135deg) scale(1.1);
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    padding: 16px 16px calc(env(safe-area-inset-bottom) + 16px);
    gap: 12px;

    .control-buttons {
      gap: 16px;

      .control-button {
        min-width: 50px;

        .button-icon {
          width: 44px;
          height: 44px;
          font-size: 18px;
        }

        .button-label {
          font-size: 11px;
        }
      }
    }

    .hang-up-section .hang-up-button {
      width: 56px;
      height: 56px;
      font-size: 20px;
    }
  }

  // 横屏适配
  @media (orientation: landscape) and (max-height: 500px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px calc(env(safe-area-inset-bottom) + 12px);

    .control-buttons {
      gap: 20px;

      .control-button {
        .button-label {
          display: none;
        }
      }
    }

    .hang-up-section .hang-up-button {
      width: 48px;
      height: 48px;
      font-size: 18px;
    }
  }

  // 高对比度模式适配
  @media (prefers-contrast: high) {
    background: rgba(0, 0, 0, 0.95);

    .control-buttons .control-button {
      .button-icon {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.5);
      }

      &.disabled .button-icon {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    .hang-up-section .hang-up-button {
      border: 2px solid rgba(255, 255, 255, 0.3);
    }
  }

  // 减少动画模式适配
  @media (prefers-reduced-motion: reduce) {
    transition: none;

    .control-buttons .control-button {
      transition: none;

      &:hover {
        transform: none;
      }

      .button-icon {
        transition: none;

        .control-button:hover & {
          transform: none;
        }
      }
    }

    .hang-up-section .hang-up-button {
      transition: none;

      &:hover {
        transform: none;
      }

      .hang-up-icon {
        transition: none;

        &:hover {
          transform: rotate(135deg);
        }
      }
    }
  }
}
