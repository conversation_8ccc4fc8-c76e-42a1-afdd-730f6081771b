/*
 * @File: 会议详情的reducer
 * @Author: liulian
 * @Date: 2021-07-01 15:30:22
 * @version: V0.0.0.1
 * @LastEditTime: 2024-04-08 20:26:13
 */
const INTOMEETDETAIL = 'INTOMEETDETAIL';
const PLAYEXPANDFLAG = 'PLAYEXPANDFLAG';
const ISFIRSTUPDATEMIX = 'ISFIRSTUPDATEMIX';
const ISRECORDING = 'ISRECORDING';
const MEMAUDIENCE = 'MEMAUDIENCE';
const MEETASYNCONWALL = 'MEETASYNCONWALL';
const CURSELMEM = 'CURSELMEM';
const LOOPVIDEOINFO = 'LOOPVIDEOINFO'
const CUROPDETAIL = 'CUROPDETAIL'
const CANSELECTMEDIAINFO = 'CANSELECTMEDIAINFO'
const CURSELECTAUDIO = 'CURSELECTAUDIO'
const ISCHANGETOJK = 'ISCHANGETOJK'
const DECODEINFOLIST= 'DECODEINFOLIST'
const CURMEETTALKTEL = 'CURMEETTALKTEL'
const CURMEETCHAIRMANTEL = 'CURMEETCHAIRMANTEL'
const OPARINFO = 'OPARINFO'
const ISSUBTITLE = 'ISSUBTITLE'
const LIVEURL = 'LIVEURL'
const WAITINGROOMLIST = 'WAITINGROOMLIST'
const WAITINGSWITCH = 'WAITINGSWITCH'
const ISWAITING = 'ISWAITING'
const ISSHOWALLMUTE = 'ISSHOWALLMUTE'
const SELFDISCHARGE = 'SELFDISCHARGE'
const VIDEOLISTNUMBER = 'VIDEOLISTNUMBER'
const ISSHOWSPEAKNAME = 'ISSHOWSPEAKNAME'
const ISCHAT = 'ISCHAT'
const CHATINFO = 'CHATINFO'
const RECEIVERS = 'RECEIVERS'
const MEMTYPELIST = 'MEMTYPELIST'
const ISSHARING = 'ISSHARING'

const initState = {
    intoMeetDetail:{},  //进入会议之后的会议详情信息：包括从接口中查询到的一些相关信息，应该是最全的信息
    memTypeList:{},  //会议成员类型列表
    playExpandFlag:true,  //是否扩展分屏播放标志位
    isFirstUpdateMix:true,  //是否是初次设置混屏
    isRecording:false,  //是否正在录制
    isShareing:false,  //是否正在分享
    memAudience: 'speak',  //成员入会时状态
    meetAsyncOnWall: false,  //会议是否同步上墙
    curSelMem:{},   //当前选中的会议成员人员
    loopVideoInfo:[],   //当前可轮询的视频信息数组
    curOpDetail:{}, //当前操作员的详细信息
    canSelectMediaInfo:{}, //可以选择的音视频源信息
    curSelectAudio:'jk',  //当前选中的音频类型 
    isChangeToJk:false,  //当前音频源是否正在切换为捷控
    decodeInfoList:[],  //缓存视频初始化过程中的Decodeinfo信息，等视频初始化成功之后去播放
    curMeetTalkTel:'',   //当前会场中讲话人员的号码
    curMeetChairmanTel:'',  //当前会场中主持人号码
    opArInfo:[],//设置语音转文字信息
    isSubtitle:false,//是否开启字幕
    liveUrl:'', //分享地址
    waitingRoomList:[],//等候室列表
    waitingswitch:false,//等候室开关
    isWaiting:false,//等候室开关
    isShowAllMute: true,  //是否全体禁言
    selfDischarge:false,//允许成员自我解除禁言
    videoListNumber:[],//播放列表
    isShowSpeakName:{
        isShow:false,
        name:'淮南市应急管理局；安徽省应急管理厅'
    },
    isChat:false,
    chatInfo:[],
    receivers:''

};

export function meetDetailReducer(state = initState, action) {
    switch (action.type) {
        case INTOMEETDETAIL: return { ...state, intoMeetDetail: action.data };
        case PLAYEXPANDFLAG: return { ...state, playExpandFlag: action.data };
        case ISFIRSTUPDATEMIX: return { ...state, isFirstUpdateMix: action.data };
        case ISRECORDING: return { ...state, isRecording: action.data };
        case MEMAUDIENCE: return { ...state, memAudience: action.data };
        case MEETASYNCONWALL: return { ...state, meetAsyncOnWall: action.data };
        case CURSELMEM: return { ...state, curSelMem: action.data };
        case LOOPVIDEOINFO: return { ...state, loopVideoInfo: action.data };
        case CUROPDETAIL: return { ...state, curOpDetail: action.data };
        case CANSELECTMEDIAINFO: return { ...state, canSelectMediaInfo: action.data };
        case CURSELECTAUDIO: return { ...state, curSelectAudio: action.data };
        case ISCHANGETOJK: return { ...state, isChangeToJk: action.data };
        case DECODEINFOLIST: return { ...state, decodeInfoList: action.data };
        case CURMEETTALKTEL: return { ...state, curMeetTalkTel: action.data };
        case CURMEETCHAIRMANTEL: return { ...state, curMeetChairmanTel: action.data };
        case OPARINFO: return { ...state, opArInfo: action.data };
        case ISSUBTITLE: return { ...state, isSubtitle: action.data };
        case LIVEURL: return { ...state, liveUrl: action.data };
        case WAITINGROOMLIST: return { ...state, waitingRoomList: action.data };
        case WAITINGSWITCH: return { ...state, waitingswitch: action.data };
        case ISWAITING: return { ...state, isWaiting: action.data };
        case ISSHOWALLMUTE: return { ...state, isShowAllMute: action.data };
        case SELFDISCHARGE: return { ...state, selfDischarge: action.data };
        case VIDEOLISTNUMBER: return { ...state, videoListNumber: action.data };
        case ISSHOWSPEAKNAME: return { ...state, isShowSpeakName: action.data };
        case ISCHAT: return { ...state, isChat: action.data };
        case CHATINFO: return { ...state, chatInfo: action.data };
        case RECEIVERS: return { ...state, receivers: action.data };
        case MEMTYPELIST: return { ...state, memTypeList: action.data };
        case ISSHARING: return { ...state, isShareing: action.data };
        default: return state;
    }
}

/**
 * 设置进入会议之后的会议详情信息
 */
export function setIntoMeetDetail(data) {
    return { type: INTOMEETDETAIL, data: data }
}

/**
 * 设置是否扩展分屏播放标志位
 */
export function setPlayExpandFlag(data) {
    return { type: PLAYEXPANDFLAG, data: data }
}

/**
 * 设置是否是初次设置混屏
 */
export function setIsFirstUpdateMix(data) {
    return { type: ISFIRSTUPDATEMIX, data: data }
}

/**
 * 设置是否正在录制
 */
export function setIsRecording(data) {
    return { type: ISRECORDING, data: data }
}

/**
 * 设置成员入会时状态
 */
export function setMemAudience(data) {
    return { type: MEMAUDIENCE, data: data }
}

/**
 * 设置会议是否同步上墙
 */
export function setMeetAsyncOnWall(data) {
    return { type: MEETASYNCONWALL, data: data }
}

/**
 * 设置当前选中的会议成员人员
 */
export function setCurSelMem(data) {
    return { type: CURSELMEM, data: data }
}

/**
 * 设置当前可轮询的视频信息数组
 */
export function setLoopVideoInfo(data) {
    return { type: LOOPVIDEOINFO, data: data }
}

/**
 * 设置当前操作员的详细信息
 */
export function setCurOpDetail(data) {
    return { type: CUROPDETAIL, data: data }
}

/**
 * 设置可以选择的音视频源信息
 */
export function setCanSelectMediaInfo(data) {
    return { type: CANSELECTMEDIAINFO, data: data }
}

/**
 * 设置当前选中的音频类型
 */
export function setCurSelectAudio(data) {
    return { type: CURSELECTAUDIO, data: data }
}

/**
 * 设置当前音频源是否正在切换为捷控
 */
export function setIsChangeToJk(data) {
    return { type: ISCHANGETOJK, data: data }
}

/**
 * 设置缓存视频初始化过程中的Decodeinfo信息
 */
export function setDecodeInfoList(data) {
    return { type: DECODEINFOLIST, data: data }
}

/**
 * 设置当前会场中讲话人员的号码
 */
export function setCurMeetTalkTel(data) {
    return { type: CURMEETTALKTEL, data: data }
}

/**
 * 设置当前会场中主持人号码
 */
export function setCurMeetChairmanTel(data) {
    return { type: CURMEETCHAIRMANTEL, data: data }
}

/**
 * 设置语音转文字信息
 */
export function setOparInfo(data) {
    return { type: OPARINFO, data: data }
}

/**
 * 设置是否开启字幕
 */
export function setIsSubtitle(data) {
    return { type: ISSUBTITLE, data: data }
}

/**
 * 设置分享地址
 */
export function setLiveUrl(data) {
    return { type: LIVEURL, data: data }
}

/**
 * 设置等候室列表
 */
export function setWaitingRoomList(data) {
    return { type: WAITINGROOMLIST, data: data }
}

/**
 * 设置等候室开关
 */
export function setWaitingswitch(data) {
    return { type: WAITINGSWITCH, data: data }
}

/**
 * 设置等候室开关
 */
export function setwaiting(data) {
    return { type: ISWAITING, data: data }
}

/**
 * 设置是否全体禁言
 */
export function setIsShowAllMute(data) {
    return { type: ISSHOWALLMUTE, data: data }
}

/**
 * 设置允许成员自我解除禁言
 */
export function setSelfDischarge(data) {
    return { type: SELFDISCHARGE, data: data }
}

/**
 * 设置播放列表
 */
export function setVideoListNumber(data) {
    return { type: VIDEOLISTNUMBER, data: data }
}

/**
 * 设置是否显示发言人姓名
 */
export function setIsShowSpeakName(data) {
    return { type: ISSHOWSPEAKNAME, data: data }
}

/**
 * 设置是否开启聊天
 */
export function setIsChat(data) {
    return { type: ISCHAT, data: data }
}

/**
 * 设置聊天信息
 */
export function setChatInfo(data) {
    return { type: CHATINFO, data: data }
}

/**
 * 设置接收者
 */
export function setReceivers(data) {
    return { type: RECEIVERS, data: data }
}

/**
 * 设置会议成员类型列表
 */
export function setMemTypeList(data) {
    return { type: MEMTYPELIST, data: data }
}

/**
 * 设置是否正在分享
 */
export function setIsShareing(data) {
    return { type: ISSHARING, data: data }
}
