import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import reportWebVitals from './reportWebVitals';

import { ConfigProvider } from 'antd';
import zh_CN from 'antd/es/locale/zh_CN';
import 'antd/dist/reset.css';

import WebRouter from "./router";
import {HashRouter} from "react-router-dom";

import { Provider } from 'react-redux'
import store from "./redux/store";
import {DndProvider} from "react-dnd";
import {HTML5Backend} from "react-dnd-html5-backend";

const root = ReactDOM.createRoot(document.getElementById('root'));

root.render(
	// <React.StrictMode>
		<Provider store={store}>
			<ConfigProvider locale={zh_CN}>
				<DndProvider backend={HTML5Backend}>
					<HashRouter>
						<WebRouter />
					</HashRouter>
				</DndProvider>
			</ConfigProvider>
		</Provider>
	// </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
