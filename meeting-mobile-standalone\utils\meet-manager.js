/*
 * @File: 协同会商状态管理
 * @Author: liulian
 * @Date: 2021-06-17 15:11:47
 * @version: V0.0.0.1
 * @LastEditTime: 2025-01-21 13:47:05
 */
import { message } from "antd";
import moment from "moment";
import { curMainTel } from "../config/constants";
import { setIsShowCreateMeet, setControlMeetDetail, setCurMeetDetail } from "../reducers/create-meet-reducer";
import { setIsVisitor } from '../reducers/loading-reducer';
import { getUrlParmse, judgePlayVideo, playVideo } from './method'
import { setIsMultiScreen } from "../reducers/screen-manage-reducer";
import { setCurMeetChairmanTel, setIntoMeetDetail, setIsRecording, setIsShareing, setWaitingRoomList } from "../reducers/meet-detail-reducer";
import store from "../store";

/**
 * 会议管理器类
 * 负责管理会议相关的状态和操作
 */
class MeetManager {
    constructor() {
        this.meetsObj = null;
        this.isInitialized = false;
        this.currentMeetId = null;
        this.meetingState = {
            isRecording: false,
            isSharing: false,
            memberCount: 0
        };
    }

    /**
     * 初始化会议管理器
     */
    init() {
        try {
            // 检查是否有scooper对象
            if (window.scooper && window.scooper.meetManager) {
                this.meetsObj = window.scooper.meetManager.meetsObj;
                this.isInitialized = true;
                console.log('会议管理器初始化成功');
            } else {
                console.warn('未找到scooper.meetManager对象，使用模拟模式');
                this.initMockMode();
            }
        } catch (error) {
            console.error('会议管理器初始化失败:', error);
            this.initMockMode();
        }
    }

    /**
     * 初始化模拟模式
     */
    initMockMode() {
        this.meetsObj = {
            createMeet: (params) => {
                console.log('模拟创建会议:', params);
                return Promise.resolve({ meetId: 'mock-' + Date.now() });
            },
            joinMeet: (meetId, tel) => {
                console.log('模拟加入会议:', { meetId, tel });
                return Promise.resolve();
            },
            leaveMeet: (meetId, tel) => {
                console.log('模拟离开会议:', { meetId, tel });
                return Promise.resolve();
            },
            kickMember: (meetId, tel) => {
                console.log('模拟踢出成员:', { meetId, tel });
                return Promise.resolve();
            },
            changeMemberLevel: (meetId, tel, level) => {
                console.log('模拟改变成员级别:', { meetId, tel, level });
                return Promise.resolve();
            },
            startRecord: (meetId) => {
                console.log('模拟开始录制:', meetId);
                return Promise.resolve();
            },
            stopRecord: (meetId) => {
                console.log('模拟停止录制:', meetId);
                return Promise.resolve();
            },
            startShare: (meetId) => {
                console.log('模拟开始分享:', meetId);
                return Promise.resolve();
            },
            stopShare: (meetId) => {
                console.log('模拟停止分享:', meetId);
                return Promise.resolve();
            }
        };
        this.isInitialized = true;
        console.log('会议管理器以模拟模式运行');
    }

    /**
     * 创建会议
     */
    async createMeet(params) {
        try {
            if (this.meetsObj && this.meetsObj.createMeet) {
                const result = await this.meetsObj.createMeet(params);
                this.currentMeetId = result.meetId;
                return result;
            } else {
                console.log('模拟创建会议:', params);
                const mockResult = { meetId: 'mock-' + Date.now() };
                this.currentMeetId = mockResult.meetId;
                return mockResult;
            }
        } catch (error) {
            console.error('创建会议失败:', error);
            message.error('创建会议失败');
            throw error;
        }
    }

    /**
     * 加入会议
     */
    async joinMeet(meetId, tel) {
        try {
            if (this.meetsObj && this.meetsObj.joinMeet) {
                await this.meetsObj.joinMeet(meetId, tel);
            } else {
                console.log('模拟加入会议:', { meetId, tel });
            }
            this.currentMeetId = meetId;
            message.success('成功加入会议');
        } catch (error) {
            console.error('加入会议失败:', error);
            message.error('加入会议失败');
            throw error;
        }
    }

    /**
     * 离开会议
     */
    async leaveMeet(meetId, tel) {
        try {
            if (this.meetsObj && this.meetsObj.leaveMeet) {
                await this.meetsObj.leaveMeet(meetId, tel);
            } else {
                console.log('模拟离开会议:', { meetId, tel });
            }
            this.currentMeetId = null;
            message.success('已离开会议');
        } catch (error) {
            console.error('离开会议失败:', error);
            message.error('离开会议失败');
            throw error;
        }
    }

    /**
     * 踢出成员
     */
    async kickMember(meetId, tel) {
        try {
            if (this.meetsObj && this.meetsObj.kickMember) {
                await this.meetsObj.kickMember(meetId, tel);
            } else {
                console.log('模拟踢出成员:', { meetId, tel });
            }
            message.success('成员已被移出会议');
        } catch (error) {
            console.error('踢出成员失败:', error);
            message.error('踢出成员失败');
            throw error;
        }
    }

    /**
     * 改变成员级别
     */
    async changeMemberLevel(meetId, tel, level) {
        try {
            if (this.meetsObj && this.meetsObj.changeMemberLevel) {
                await this.meetsObj.changeMemberLevel(meetId, tel, level);
            } else {
                console.log('模拟改变成员级别:', { meetId, tel, level });
            }
            message.success('成员权限已更新');
        } catch (error) {
            console.error('改变成员级别失败:', error);
            message.error('更新成员权限失败');
            throw error;
        }
    }

    /**
     * 开始录制
     */
    async startRecord(meetId) {
        try {
            if (this.meetsObj && this.meetsObj.startRecord) {
                await this.meetsObj.startRecord(meetId);
            } else {
                console.log('模拟开始录制:', meetId);
            }
            this.meetingState.isRecording = true;
            store.dispatch(setIsRecording(true));
            message.success('开始录制');
        } catch (error) {
            console.error('开始录制失败:', error);
            message.error('开始录制失败');
            throw error;
        }
    }

    /**
     * 停止录制
     */
    async stopRecord(meetId) {
        try {
            if (this.meetsObj && this.meetsObj.stopRecord) {
                await this.meetsObj.stopRecord(meetId);
            } else {
                console.log('模拟停止录制:', meetId);
            }
            this.meetingState.isRecording = false;
            store.dispatch(setIsRecording(false));
            message.success('停止录制');
        } catch (error) {
            console.error('停止录制失败:', error);
            message.error('停止录制失败');
            throw error;
        }
    }

    /**
     * 开始分享
     */
    async startShare(meetId) {
        try {
            if (this.meetsObj && this.meetsObj.startShare) {
                await this.meetsObj.startShare(meetId);
            } else {
                console.log('模拟开始分享:', meetId);
            }
            this.meetingState.isSharing = true;
            store.dispatch(setIsShareing(true));
            message.success('开始分享');
        } catch (error) {
            console.error('开始分享失败:', error);
            message.error('开始分享失败');
            throw error;
        }
    }

    /**
     * 停止分享
     */
    async stopShare(meetId) {
        try {
            if (this.meetsObj && this.meetsObj.stopShare) {
                await this.meetsObj.stopShare(meetId);
            } else {
                console.log('模拟停止分享:', meetId);
            }
            this.meetingState.isSharing = false;
            store.dispatch(setIsShareing(false));
            message.success('停止分享');
        } catch (error) {
            console.error('停止分享失败:', error);
            message.error('停止分享失败');
            throw error;
        }
    }

    /**
     * 获取当前会议ID
     */
    getCurrentMeetId() {
        return this.currentMeetId;
    }

    /**
     * 获取会议状态
     */
    getMeetingState() {
        return { ...this.meetingState };
    }

    /**
     * 销毁会议管理器
     */
    destroy() {
        try {
            this.meetsObj = null;
            this.isInitialized = false;
            this.currentMeetId = null;
            this.meetingState = {
                isRecording: false,
                isSharing: false,
                memberCount: 0
            };
            console.log('会议管理器已销毁');
        } catch (error) {
            console.error('销毁会议管理器失败:', error);
        }
    }
}

// 创建单例实例
const meetManager = new MeetManager();

// 自动初始化
if (typeof window !== 'undefined') {
    // 延迟初始化，等待scooper对象加载
    setTimeout(() => {
        meetManager.init();
    }, 1000);
}

export default meetManager;
