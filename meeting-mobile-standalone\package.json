{"name": "meeting-mobile-standalone", "version": "1.0.0", "description": "独立的移动端会议组件", "main": "index.js", "scripts": {"build": "webpack --mode production", "dev": "webpack --mode development", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["meeting", "mobile", "video", "conference"], "author": "", "license": "ISC", "dependencies": {"react": "^17.0.0", "react-dom": "^17.0.0", "react-redux": "^7.2.0", "redux": "^4.1.0", "antd": "^4.16.0", "react-swipeable-views": "^0.14.0", "moment": "^2.29.0", "pubsub-js": "^1.9.0", "qs": "^6.10.0", "copy-to-clipboard": "^3.3.0", "axios": "^0.24.0"}, "peerDependencies": {"sc-screen-share": "*", "sc-soft-handler": "*", "scooper-video": "*"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "@babel/preset-react": "^7.14.0", "babel-loader": "^8.2.0", "css-loader": "^6.2.0", "less": "^4.1.0", "less-loader": "^10.0.0", "style-loader": "^3.2.0", "webpack": "^5.50.0", "webpack-cli": "^4.8.0"}}