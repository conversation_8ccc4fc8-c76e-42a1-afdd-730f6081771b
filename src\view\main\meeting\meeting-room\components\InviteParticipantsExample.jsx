/**
 * @Create: 邀请参会人组件使用示例
 * @Date: 2025/01/28
 * @desc: 展示如何使用邀请参会人组件
 */

import React, { useState } from 'react';
import { Button } from 'antd';
import InviteParticipants from './InviteParticipants';
import ParticipantsList from './ParticipantsList';

function InviteParticipantsExample() {
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showParticipantsList, setShowParticipantsList] = useState(false);
  const [participants, setParticipants] = useState([
    {
      id: 1,
      name: '张三',
      tel: '13800138001',
      isHost: true,
      isAudioOn: true,
      isVideoOn: true
    },
    {
      id: 2,
      name: '李四',
      tel: '13800138002',
      isHost: false,
      isAudioOn: true,
      isVideoOn: false
    }
  ]);

  // 模拟会议数据
  const meetingData = {
    meetId: 'meeting_123456',
    meetAccess: 'access_token_123',
    meetingName: '项目讨论会议',
    startTime: '2025-01-28 14:00:00'
  };

  // 处理邀请成功
  const handleInviteSuccess = (invitedUsers) => {
    console.log('邀请成功的用户:', invitedUsers);
    
    // 将邀请的用户添加到参与者列表中
    const newParticipants = invitedUsers.map((user, index) => ({
      id: user.id || participants.length + index + 1,
      name: user.name,
      tel: user.tel,
      isHost: false,
      isAudioOn: false,
      isVideoOn: false,
      department: user.department
    }));

    setParticipants(prev => [...prev, ...newParticipants]);
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>邀请参会人组件示例</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <Button 
          type="primary" 
          onClick={() => setShowInviteModal(true)}
          style={{ marginRight: '10px' }}
        >
          打开邀请弹窗
        </Button>
        
        <Button 
          onClick={() => setShowParticipantsList(true)}
        >
          查看参与者列表
        </Button>
      </div>

      <div>
        <h3>当前参与者 ({participants.length}人):</h3>
        <ul>
          {participants.map(participant => (
            <li key={participant.id}>
              {participant.name} ({participant.tel}) 
              {participant.isHost && ' - 主持人'}
            </li>
          ))}
        </ul>
      </div>

      {/* 邀请参会人弹窗 */}
      <InviteParticipants
        visible={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        meetingData={meetingData}
        onInviteSuccess={handleInviteSuccess}
      />

      {/* 参与者列表弹窗 */}
      <ParticipantsList
        visible={showParticipantsList}
        participants={participants}
        onClose={() => setShowParticipantsList(false)}
        isHost={true}
        isMuteAll={false}
        onInvite={handleInviteSuccess}
        onMuteAll={() => console.log('全体静音')}
        meetingDuration="01:23:45"
        meetingData={meetingData}
      />
    </div>
  );
}

export default InviteParticipantsExample;
