/*
 * @File: 视频管理
 * @Author: liulian
 * @Date: 2020-07-30 16:38:57
 * @version: V0.0.0.1
 * @LastEditTime: 2024-09-18 14:45:58
 */
import { message, Modal } from 'antd'
import store from '../store/index'
import { changeLoading } from '../reducers/loading-reducer'
import { Component } from 'react'
import { setPlayExpandFlag } from '../reducers/meet-detail-reducer';
import { setCurSelectScreen, setMeetScreenInfo } from '../reducers/screen-manage-reducer'
import { setIntoMeetDetail, setIsFirstUpdateMix, setLoopVideoInfo } from '../reducers/meet-detail-reducer'

/**
 * 会议视频管理组件
 * 负责初始化视频控制器、注册事件监听器，处理视频播放相关操作
 */
class MeetVideoManage extends Component {
    /**
     * 视频控制器实例 - 由 VideoWebRtc 提供
     * @type {Object}
     * @private
     */
    videoController = null;
    
    /**
     * 定时器实例，用于轮询检测
     * @type {Number}
     * @private
     */
    callInterval = null;

    constructor(props) {
        super(props);
    }

    /**
     * 组件挂载后，初始化视频控制器
     */
    componentDidMount() {
        this.initializeTime()
    }
    
    /**
     * 组件卸载前，清理资源和事件监听
     */
    componentWillUnmount() {
        // 清理定时器
        clearInterval(window.initTime);
        
        // 如果视频控制器已初始化，移除事件监听
        // if (this.videoController) {
        //     this.videoController.removeAllListeners();
        // }
    }
    
    /**
     * 设置初始化定时器
     * 每500ms检查一次会议详情是否加载完成
     */
    initializeTime = () => {
        let _this = this;
        window.initTime = setInterval(()=>{_this.initializa()}, 500);
    }

    /**
     * 初始化视频控制器
     * 当会议详情加载完成后执行
     */
    initializa = () => {
        let { intoMeetDetail } = store.getState().meetDetail;
        if (intoMeetDetail && Object.values(intoMeetDetail).length !== 0) {
            clearInterval(window.initTime);
            const _this = this;
            
            // 检查是否有OCX插件
            if (!_this.hasOcx()) {
                store.dispatch(changeLoading(false));
                return;
            }
            
            // 初始化视频配置
            let videoOpts = {
                // 初始化时的界面显示的分屏数
                windows: intoMeetDetail.videoScreen || 1,
                windowNums: 16,
                freeWindow: true,
            }
            
            try {
                // 检查是否有可用的视频控制器
                if (window.scooper && window.scooper.meetvideoController) {
                    this.videoController = window.scooper.meetvideoController;
                    this.initVideoController(videoOpts);
                } else if (window.VideoWebRtc) {
                    // 使用WebRTC视频控制器
                    this.initWebRtcController(videoOpts);
                } else {
                    console.warn('没有找到可用的视频控制器');
                    store.dispatch(changeLoading(false));
                }
            } catch (error) {
                console.error('初始化视频控制器失败:', error);
                store.dispatch(changeLoading(false));
            }
        }
    }
    
    /**
     * 初始化传统视频控制器
     */
    initVideoController = (videoOpts) => {
        try {
            this.videoController.init(videoOpts);
            this.registerVideoEvents();
            store.dispatch(changeLoading(false));
            console.log('视频控制器初始化成功');
        } catch (error) {
            console.error('初始化传统视频控制器失败:', error);
            store.dispatch(changeLoading(false));
        }
    }
    
    /**
     * 初始化WebRTC视频控制器
     */
    initWebRtcController = (videoOpts) => {
        try {
            // 创建WebRTC视频控制器实例
            this.videoController = new window.VideoWebRtc({
                container: '#video-main-web-rtc',
                ...videoOpts
            });
            
            this.registerWebRtcEvents();
            store.dispatch(changeLoading(false));
            store.dispatch({ type: 'CREAREVIDEOUI', data: true });
            console.log('WebRTC视频控制器初始化成功');
        } catch (error) {
            console.error('初始化WebRTC视频控制器失败:', error);
            store.dispatch(changeLoading(false));
        }
    }
    
    /**
     * 注册传统视频控制器事件
     */
    registerVideoEvents = () => {
        if (!this.videoController) return;
        
        // 注册视频播放事件
        this.videoController.on('videoPlay', (data) => {
            console.log('视频播放事件:', data);
        });
        
        // 注册视频停止事件
        this.videoController.on('videoStop', (data) => {
            console.log('视频停止事件:', data);
        });
        
        // 注册错误事件
        this.videoController.on('error', (error) => {
            console.error('视频控制器错误:', error);
            message.error('视频播放出现错误');
        });
    }
    
    /**
     * 注册WebRTC视频控制器事件
     */
    registerWebRtcEvents = () => {
        if (!this.videoController) return;
        
        // 注册连接事件
        this.videoController.on('connected', () => {
            console.log('WebRTC连接成功');
        });
        
        // 注册断开连接事件
        this.videoController.on('disconnected', () => {
            console.log('WebRTC连接断开');
        });
        
        // 注册流事件
        this.videoController.on('stream', (stream) => {
            console.log('收到视频流:', stream);
        });
        
        // 注册错误事件
        this.videoController.on('error', (error) => {
            console.error('WebRTC错误:', error);
            message.error('视频连接出现错误');
        });
    }
    
    /**
     * 检查是否有OCX插件
     * @returns {boolean} 是否有OCX插件
     */
    hasOcx = () => {
        try {
            // 检查是否有相关的全局对象
            return !!(window.scooper || window.VideoWebRtc || window.newShandleUtil);
        } catch (error) {
            console.error('检查OCX插件失败:', error);
            return false;
        }
    }

    render() {
        // 这个组件不渲染任何UI，只负责视频管理
        return null;
    }
}

export default MeetVideoManage;
