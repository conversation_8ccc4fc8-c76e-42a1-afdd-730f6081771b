/*
 * @File: 视频管理组件
 * @Author: yang<PERSON><PERSON>
 * @Date: 2020-07-30 16:38:57
 * @version: V0.0.0.1
 * @LastEditTime: 2024-12-18 14:21:02
 * @Description: 负责会议视频的初始化、播放控制、事件监听和分屏管理
 */
import { message } from 'antd'
import store from '../store/index'
import { changeLoading,createVideoUI} from '../reducer/loading-reducer'
import { Component } from 'react'
import { setPlayExpandFlag } from '../reducer/meet-detail-reducer';
import { setCurSelectScreen, setMeetScreenInfo, setScreenList } from '../reducer/screen-manage-reducer'
import { setIntoMeetDetail, setIsFirstUpdateMix, setLoopVideoInfo } from '../reducer/meet-detail-reducer'
import  '../reducer/loading-reducer';
import {debounce} from 'lodash'
import { deepArrayEqual, judgePlayVideo, openMits, updateLayout,getMemName,updateLoopVideoInfo,getName } from './method'
import {getToken} from "../config/constants"
import PubSub from 'pubsub-js';

/**
 * 根据当前环境设置最大分屏数
 * 移动端最多支持4个分屏，PC端最多支持16个分屏
 */
const videoNumber = window.location.hash.indexOf('#/main/videoPartingMobile') > -1?4:16;

/**
 * 会议视频管理组件
 * 负责初始化视频控制器、注册事件监听器，处理视频播放相关操作
 */
class MeetVideoManage extends Component {
    /**
     * 视频控制器实例 - 由 VideoWebRtc 提供
     * @type {Object}
     * @private
     */
    videoController = null;
    
    /**
     * 定时器实例，用于轮询检测
     * @type {Number}
     * @private
     */
    callInterval = null;

    constructor(props) {
        super(props);
    }

    /**
     * 组件挂载后，初始化视频控制器
     */
    componentDidMount() {
        this.initializeTime()
    }
    
    /**
     * 组件卸载前，清理资源和事件监听
     */
    componentWillUnmount() {
        // 清理定时器
        clearInterval(window.initTime);
        
        // 如果视频控制器已初始化，移除事件监听
        // if (this.videoController) {
        //     this.videoController.removeAllListeners();
        // }
    }
    
    /**
     * 设置初始化定时器
     * 每500ms检查一次会议详情是否加载完成
     */
    initializeTime = () => {
        let _this = this;
        window.initTime = setInterval(()=>{_this.initializa()}, 500);
    }
    /**
     * 初始化视频控制器
     * 当会议详情加载完成后执行
     */
    initializa = () => {
        let { intoMeetDetail } = store.getState().meetDetail;
        if (intoMeetDetail && Object.values(intoMeetDetail).length > 0) {
            clearInterval(window.initTime);
            const _this = this;
            let videoOpts = {
                //初始化时的界面显示的分屏树
                windows: intoMeetDetail.videoScreen||(this.isMobileDevice()?4:1),
                windowsNum:this.isMobileDevice()?4:16,
                windowsArr:this.isMobileDevice()?[4]:[1, 2, 3, 4, 6, 9, 16],
                draggable: true,
                // windowsBeginIndex: 0,
                showVideoInfo:0,
                conf:{token:getToken()},
                showVideoName: true, // 显示视频名称
                isScooperMeet: true, // 显示scooper-meet的界面
                // objectFit: 'true'
                // isWsPlay:true
            }
            // var videoArea = _this.isIE() ? '.video-area' : '.web-rtc-camera-content';
            var videoArea = document.querySelector('.webRtcVideo');
            _this.videoController = new window.VideoWebRtc(videoArea, videoOpts);

            window.scooper.meetvideoController = _this.videoController;
            window.updateFlag = 0
            store.dispatch(changeLoading(false));
            // _this.videoController.openSetVdieoSer();
            // 初始化完成
            _this.videoController.addListener('initsucc', (e)=> {
                // let { myMeetList } = store.getState().createMeet;
                window.init = 'success';
                window.decodeFlag = 0;
                setTimeout(()=>{
                    let { intoMeetDetail } = store.getState().meetDetail;
                    let {isMultiScreen} = store.getState().screenManage;
                    window.scooper.meetvideoController.setDraggble(!isMultiScreen)
                    store.dispatch(createVideoUI(true));
                    judgePlayVideo([intoMeetDetail],'first');
                },300)
            })
            // 播放成功
            _this.videoController.addListener('playsuccessByRealIndex', (e)=> {      //播放成功  -- 更新分屏信息给python
                console.log("播放成功", e);
                PubSub.publish('addMoreAntd', e.id)
                let screenList = store.getState().screenManage.screenList;
                let intoMeetDetail = store.getState().meetDetail.intoMeetDetail;
                screenList && screenList.length > 0 && screenList.map((item,index) => {
                    if (index == e.index && e.opts && e.opts.isUpdateScreen !='false' ) {
                        let curParam = JSON.parse(sessionStorage.getItem('curParam'));
                        let curMeetId = curParam.meetId;
                        item.videoName = getMemName(e.id,intoMeetDetail.members);
                        item.meetId = curMeetId;
                        item.devId = e.id;
                        item.hasVideo = true;
                    }
                })
                store.dispatch(setScreenList([...screenList]));
                
                // 更新分屏信息给后台
                _this.updateMeetScreen();
                // 修改麦克风图标未变成禁言状态
                if(intoMeetDetail?.meetMem){
                    intoMeetDetail.meetMem.map((item) => {
                        if (item.tel == e.id) {
                            if (['speak', "audience"].includes(item.level)) {
                                _this.videoController.setSpeakType({tel:e.id,level:item.level})
                            }
                        }
                    });
                }
            })
            _this.videoController.addListener('afterclose', function (e) {
                if (e.id) {
                    console.log("关闭视频")
                    let { screenList } = store.getState().screenManage;
                    screenList && screenList.length > 0 && screenList.map((item) => {
                        if (item.devId == e.id) {
                            item.videoName = '请选择视频源';
                            item.meetId = '';
                            item.devId = '';
                            item.hasVideo = false;
                            item.selected = ''
                        }
                    })
                    store.dispatch(setScreenList([...screenList]))
                    // 视频关闭后 更新轮询视频的数据信息
                    _this.updateLoopVideoInfo(e.id)
                    // 更新分屏信息给后台
                    if(window.updateFlag != 1){
                        _this.updateMeetScreen();
                    }
                    PubSub.publish('addMoreAntd', e.id)
                }
            })
            // 分屏
            _this.videoController.addListener('screenchange', function (e) {   //分屏切换成功  -- 更新分屏信息给python
                console.log("分屏切换成功", e.windowNums);
                let curNum = e.windowNums; //4
                let curScreen = 'screen-' + curNum;
                let { intoMeetDetail } = store.getState().meetDetail;
                intoMeetDetail.videoScreen = curNum;
                console.log("分屏切换成功:setIntoMeetDetail", intoMeetDetail)
                store.dispatch(setIntoMeetDetail({ ...intoMeetDetail }));
                store.dispatch(setCurSelectScreen(curScreen));
                let {screenList } = store.getState().screenManage;
                // 更新分屏信息给后台
                _this.updateMeetScreen(curNum);
                setTimeout(()=>{
                    if (curNum>videoNumber) {
                        const { isShareing } = store.getState().meetDetail;
                        if (!isShareing) {
                            store.dispatch(setPlayExpandFlag(false));
                            curNum = videoNumber;
                            _this.videoController.setWindowsNum(curNum);
                        }
                        return
                    }
                })
            })
            // 拖拽
            _this.videoController.addListener('dragEnd', function (e) {         //拖动成功之后的通知  --更新分屏信息给python
                console.log("拖拽成功通知：",e);
            })
            _this.videoController.addListener('changeVideocodeType', function (data) {
                const {changeCodeType,opts,playseq,videoId} = data
                opts.videoCodeType = changeCodeType;
                (async() => {
                    const name = await getName(videoId)
                    updateLoopVideoInfo("add",{tel:videoId,member:name});
                })()
                setTimeout( async() => {
                    _this.videoController.play(playseq, videoId,videoId,opts);
                },1200)
            })
            // 断流
            _this.videoController.addListener('necessaryTips', function (data) {
                const {playseq,opts,index} = data
                const numWin =!isNaN(playseq)?playseq+1:index+1;
                message.error('窗口:'+ numWin +', 终端:'+opts.name+'无流');
            })
        }  
    }

   isMobileDevice = () => {
        return (typeof window.orientation !== "undefined") || (navigator.userAgent.indexOf('IEMobile') !== -1);
    };
    /**
     * 更新分屏信息给python && 上墙情况  
     */
    updateMeetScreen = debounce(async (screenParam) => {
        let { intoMeetDetail,isFirstUpdateMix } = store.getState().meetDetail;
        let { curSelectScreenVal } = store.getState().screenManage;
        let screen = screenParam || curSelectScreenVal.split('-')[1];
          //混屏服务开始  --更新分屏信息
        let curParam = JSON.parse(sessionStorage.getItem('curParam'));
        let curMeetId = curParam?.meetId;
        let { meetScreenInfo, screenList } = store.getState().screenManage;
        let memberInfo = [];
        screenList.map((item, index) => {
            if (item.hasVideo && item.meetId == curMeetId && item.devId) {
                let info = {
                    screenId: Number(index) + 1,
                    tel: item.devId
                }
                memberInfo.push(info);
            }
        })
        if (meetScreenInfo && (meetScreenInfo.meetScreenType != screen || !deepArrayEqual(meetScreenInfo.member?meetScreenInfo.member:[], memberInfo))) {
            console.log(isFirstUpdateMix);
            let param = {
                meetId: curMeetId,
                meetScreenType: Number(screen),
                member:(isFirstUpdateMix || memberInfo.length == 0)?meetScreenInfo.member:memberInfo
            }
            store.dispatch(setMeetScreenInfo(param));
            store.dispatch(setIsFirstUpdateMix(false))
        }
        
        if (intoMeetDetail.meetSyncOnWall) {  // mits开启 ---更新mits
            let curParam = JSON.parse(sessionStorage.getItem('curParam'));
            let curMeetId = curParam.meetId;
            openMits(curMeetId,screen)
        }
    },800)
    /**
     * 更新轮询视频的数据信息
     */
    updateLoopVideoInfo = (id) => {
        let { loopVideoInfo } = store.getState().meetDetail;
        const findInfoIndex = loopVideoInfo.findIndex(info => info.id == id);
        if (findInfoIndex >= 0) {  //找到了
            loopVideoInfo.splice(findInfoIndex, 1);
            store.dispatch(setLoopVideoInfo([...loopVideoInfo]))
        }
    }
    render() {
        return (
            null
        )
    }
}


// window.scooper = window.scooper || {}
// const meetVideoManage = window.scooper.meetVideoManage = new MeetVideoManage();
export default MeetVideoManage;