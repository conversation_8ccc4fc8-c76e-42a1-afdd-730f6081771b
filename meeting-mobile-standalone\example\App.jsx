/*
 * @File: Meeting Mobile 使用示例
 * @Author: liulian
 * @Date: 2025-01-01 00:00:00
 * @version: V1.0.0
 * @Description: 展示如何在项目中使用meeting-mobile组件
 */

import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Route, Switch, useHistory } from 'react-router-dom';
import MeetingMobile, { initMeetingMobile } from '../index';

// 初始化配置
initMeetingMobile({
  apiBaseUrl: 'https://your-api-server.com',
  debug: true,
  globalObjects: {
    // 如果你有自定义的全局对象，可以在这里配置
    // scooper: yourScooperObject,
    // newShandleUtil: yourShandleUtilObject,
  }
});

/**
 * 会议页面组件
 */
const MeetingPage = () => {
  const history = useHistory();
  const [meetingConfig, setMeetingConfig] = useState({});

  useEffect(() => {
    // 从URL参数获取会议配置
    const urlParams = new URLSearchParams(window.location.search);
    const meetId = urlParams.get('meetId');
    const token = urlParams.get('token');
    const isAudio = urlParams.get('isAudio');
    const isVideo = urlParams.get('isVideo');
    const memJoinType = urlParams.get('memJoinType');

    if (meetId) {
      setMeetingConfig({
        meetId,
        token,
        isAudio: isAudio === 'true',
        isVideo: isVideo === 'true',
        memJoinType
      });

      // 设置会议相关的sessionStorage
      if (token) {
        sessionStorage.setItem('meetWebToken', token);
        sessionStorage.setItem('curParam', JSON.stringify({ meetId }));
      }
    }
  }, []);

  const handleLeave = () => {
    console.log('用户离开会议');
    
    // 清理会议相关数据
    sessionStorage.removeItem('meetWebToken');
    sessionStorage.removeItem('curParam');
    sessionStorage.removeItem('meetWebMaintel');
    
    // 跳转到离开页面或首页
    history.push('/meeting-closed');
  };

  const handleError = (error) => {
    console.error('会议组件错误:', error);
    // 处理错误，比如显示错误页面
  };

  if (!meetingConfig.meetId) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        fontSize: '18px'
      }}>
        正在加载会议...
      </div>
    );
  }

  return (
    <MeetingMobile 
      onLeave={handleLeave}
      onError={handleError}
      history={history}
      config={meetingConfig}
    />
  );
};

/**
 * 会议结束页面
 */
const MeetingClosedPage = () => {
  const history = useHistory();

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      padding: '20px',
      textAlign: 'center'
    }}>
      <h2>会议已结束</h2>
      <p>感谢您的参与</p>
      <button 
        onClick={() => history.push('/')}
        style={{
          padding: '10px 20px',
          fontSize: '16px',
          backgroundColor: '#1890ff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          marginTop: '20px'
        }}
      >
        返回首页
      </button>
    </div>
  );
};

/**
 * 首页组件
 */
const HomePage = () => {
  const history = useHistory();
  const [meetingUrl, setMeetingUrl] = useState('');

  const handleJoinMeeting = () => {
    if (meetingUrl) {
      // 解析会议URL或直接跳转
      window.location.href = meetingUrl;
    } else {
      // 示例：跳转到测试会议
      const testMeetingUrl = '/meeting?meetId=test123&token=testtoken&isAudio=true&isVideo=true&memJoinType=speaker';
      history.push(testMeetingUrl);
    }
  };

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      padding: '20px'
    }}>
      <h1>视频会议系统</h1>
      
      <div style={{ marginTop: '40px', width: '100%', maxWidth: '400px' }}>
        <input
          type="text"
          placeholder="请输入会议链接或会议ID"
          value={meetingUrl}
          onChange={(e) => setMeetingUrl(e.target.value)}
          style={{
            width: '100%',
            padding: '12px',
            fontSize: '16px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            marginBottom: '20px'
          }}
        />
        
        <button 
          onClick={handleJoinMeeting}
          style={{
            width: '100%',
            padding: '12px',
            fontSize: '16px',
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          加入会议
        </button>
      </div>

      <div style={{ marginTop: '40px', textAlign: 'center' }}>
        <h3>快速测试</h3>
        <button 
          onClick={() => history.push('/meeting?meetId=test123&token=testtoken&isAudio=true&isVideo=true&memJoinType=speaker')}
          style={{
            padding: '8px 16px',
            fontSize: '14px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            margin: '5px'
          }}
        >
          测试会议（发言人）
        </button>
        
        <button 
          onClick={() => history.push('/meeting?meetId=test123&token=testtoken&isAudio=false&isVideo=true&memJoinType=audience')}
          style={{
            padding: '8px 16px',
            fontSize: '14px',
            backgroundColor: '#faad14',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            margin: '5px'
          }}
        >
          测试会议（听众）
        </button>
      </div>
    </div>
  );
};

/**
 * 主应用组件
 */
const App = () => {
  useEffect(() => {
    // 设置移动端视口高度
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVH();
    window.addEventListener('resize', setVH);

    return () => {
      window.removeEventListener('resize', setVH);
    };
  }, []);

  return (
    <Router>
      <div className="App">
        <Switch>
          <Route exact path="/" component={HomePage} />
          <Route path="/meeting" component={MeetingPage} />
          <Route path="/meeting-closed" component={MeetingClosedPage} />
        </Switch>
      </div>
    </Router>
  );
};

export default App;
