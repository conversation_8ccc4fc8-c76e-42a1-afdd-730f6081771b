.meeting-detail {
  width: 100%;
  height: 100vh;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  .nav-bar {
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    
    :global(.adm-nav-bar-title) {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
    }
  }

  .error-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999999;
    font-size: 16px;
  }

  .content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    
    .info-card,
    .time-card,
    .host-card,
    .participants-card,
    .details-card {
      margin-bottom: 16px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      :global(.adm-card-body) {
        padding: 16px;
      }
    }

    .meeting-header {
      display: flex;
      align-items: center;
      gap: 16px;

      .meeting-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .meeting-title {
        flex: 1;

        h2 {
          margin: 0 0 8px 0;
          font-size: 20px;
          font-weight: 600;
          color: #333333;
          line-height: 1.3;
        }

        .meeting-type-tag {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 4px;
          background-color: #f0f0f0;
          border:none;
          color: #333333;
        }
      }
    }

    .time-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .time-label {
        font-size: 14px;
        color: #666666;
        font-weight: 500;
      }

      .time-value {
        font-size: 14px;
        color: #333333;
        font-weight: 600;
      }
    }

    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .host-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .host-details {
        flex: 1;

        .host-name {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
          margin-bottom: 4px;
        }

        .host-tel {
          font-size: 14px;
          color: #666666;
        }
      }
    }

    .participants-list {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .participant-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 0;

        .participant-name {
          font-size: 14px;
          color: #333333;
          font-weight: 500;
        }
      }
    }

    .detail-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .detail-label {
        font-size: 14px;
        color: #666666;
        font-weight: 500;
      }

      .detail-value {
        font-size: 14px;
        color: #333333;
        font-weight: 600;
        max-width: 60%;
        text-align: right;
        word-break: break-all;
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .content {
      padding: 12px;
      
      .info-card,
      .time-card,
      .host-card,
      .participants-card,
      .details-card {
        margin-bottom: 12px;
        
        :global(.adm-card-body) {
          padding: 12px;
        }
      }

      .meeting-header {
        gap: 12px;

        .meeting-icon {
          width: 40px;
          height: 40px;
        }

        .meeting-title h2 {
          font-size: 18px;
        }
      }

      .card-title {
        font-size: 15px;
        margin-bottom: 12px;
      }
    }
  }

  // 暗色模式适配
  @media (prefers-color-scheme: dark) {
    background: #1a1a1a;

    .nav-bar {
      background: #2a2a2a;
      border-bottom-color: #3a3a3a;
      
      :global(.adm-nav-bar-title) {
        color: #ffffff;
      }
    }

    .info-card,
    .time-card,
    .host-card,
    .participants-card,
    .details-card {
      :global(.adm-card) {
        background: #2a2a2a;
      }
    }

    .meeting-header {
      .meeting-icon {
        background: #3a3a3a;
      }

      .meeting-title h2 {
        color: #ffffff;
      }
    }

    .time-item {
      border-bottom-color: #3a3a3a;

      .time-label {
        color: #cccccc;
      }

      .time-value {
        color: #ffffff;
      }
    }

    .card-title {
      color: #ffffff;
      border-bottom-color: #3a3a3a;
    }

    .host-info .host-details {
      .host-name {
        color: #ffffff;
      }

      .host-tel {
        color: #cccccc;
      }
    }

    .participants-list .participant-item .participant-name {
      color: #ffffff;
    }

    .detail-item {
      border-bottom-color: #3a3a3a;

      .detail-label {
        color: #cccccc;
      }

      .detail-value {
        color: #ffffff;
      }
    }
  }
}
