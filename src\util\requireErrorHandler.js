/**
 * RequireJS 错误处理工具
 * 解决 "Cannot set properties of undefined (setting 'contextName')" 错误
 */

class RequireErrorHandler {
  constructor() {
    this.isInitialized = false;
    this.errorHandlers = [];
  }

  /**
   * 初始化错误处理
   */
  init() {
    if (this.isInitialized) {
      return;
    }

    console.log('🔧 初始化 RequireJS 错误处理...');

    // 处理 RequireJS 特定错误
    this.setupRequireErrorHandler();
    
    // 处理全局错误
    this.setupGlobalErrorHandler();
    
    // 处理未捕获的 Promise 错误
    this.setupPromiseErrorHandler();

    this.isInitialized = true;
    console.log('✅ RequireJS 错误处理初始化完成');
  }

  /**
   * 设置 RequireJS 错误处理
   */
  setupRequireErrorHandler() {
    if (typeof window.require !== 'undefined') {
      const originalOnError = window.require.onError;
      
      window.require.onError = (err) => {
        console.warn('🚨 RequireJS 错误已捕获:', err);
        
        // 特殊处理 contextName 错误
        if (err.message && err.message.includes('contextName')) {
          console.warn('🔧 处理 contextName 错误，继续运行应用');
          return true; // 阻止错误传播
        }
        
        // 调用原始错误处理器
        if (originalOnError) {
          return originalOnError.call(this, err);
        }
        
        return true; // 默认阻止错误传播
      };
    }
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandler() {
    const handleGlobalError = (event) => {
      const error = event.error;
      
      if (!error || !error.message) {
        return false;
      }

      // 检查是否是 RequireJS 相关错误
      const isRequireError = 
        error.message.includes('contextName') ||
        error.message.includes('require.js') ||
        error.message.includes('checkLoaded') ||
        error.stack?.includes('require.js');

      if (isRequireError) {
        console.warn('🔧 全局 RequireJS 错误已捕获:', error.message);
        event.preventDefault();
        return true;
      }

      return false;
    };

    window.addEventListener('error', handleGlobalError);
    this.errorHandlers.push(() => {
      window.removeEventListener('error', handleGlobalError);
    });
  }

  /**
   * 设置 Promise 错误处理
   */
  setupPromiseErrorHandler() {
    const handlePromiseError = (event) => {
      const error = event.reason;
      
      if (error && error.message && 
          (error.message.includes('contextName') || 
           error.message.includes('require.js'))) {
        console.warn('🔧 Promise RequireJS 错误已捕获:', error.message);
        event.preventDefault();
        return true;
      }
      
      return false;
    };

    window.addEventListener('unhandledrejection', handlePromiseError);
    this.errorHandlers.push(() => {
      window.removeEventListener('unhandledrejection', handlePromiseError);
    });
  }

  /**
   * 检查 RequireJS 状态
   */
  checkRequireStatus() {
    const status = {
      requireExists: typeof window.require !== 'undefined',
      defineExists: typeof window.define !== 'undefined',
      requirejsExists: typeof window.requirejs !== 'undefined',
      hasContexts: false,
      hasConfig: false
    };

    if (window.require) {
      status.hasContexts = !!(window.require.s && window.require.s.contexts);
      status.hasConfig = !!(window.require.config);
    }

    console.log('🔍 RequireJS 状态检查:', status);
    return status;
  }

  /**
   * 安全地调用 RequireJS
   */
  safeRequire(deps, callback, errback) {
    try {
      if (typeof window.require === 'function') {
        return window.require(deps, callback, errback);
      } else {
        console.warn('⚠️ RequireJS 不可用，跳过模块加载');
        if (callback) {
          callback();
        }
      }
    } catch (error) {
      console.warn('🚨 RequireJS 调用失败:', error);
      if (errback) {
        errback(error);
      } else if (callback) {
        callback();
      }
    }
  }

  /**
   * 清理错误处理器
   */
  cleanup() {
    console.log('🧹 清理 RequireJS 错误处理器...');
    
    this.errorHandlers.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn('清理错误处理器失败:', error);
      }
    });
    
    this.errorHandlers = [];
    this.isInitialized = false;
  }

  /**
   * 重置 RequireJS（紧急情况下使用）
   */
  resetRequire() {
    console.warn('🔄 重置 RequireJS...');
    
    try {
      // 清理现有的 RequireJS 状态
      if (window.require && window.require.s) {
        window.require.s.contexts = {};
      }
      
      // 重新初始化默认上下文
      if (typeof window.requirejs === 'function') {
        window.requirejs.config({
          waitSeconds: 0,
          enforceDefine: false
        });
      }
      
      console.log('✅ RequireJS 重置完成');
    } catch (error) {
      console.error('❌ RequireJS 重置失败:', error);
    }
  }
}

// 创建单例实例
const requireErrorHandler = new RequireErrorHandler();

// 导出实例和便捷方法
export default requireErrorHandler;

export const initRequireErrorHandler = () => requireErrorHandler.init();
export const cleanupRequireErrorHandler = () => requireErrorHandler.cleanup();
export const checkRequireStatus = () => requireErrorHandler.checkRequireStatus();
export const safeRequire = (deps, callback, errback) => 
  requireErrorHandler.safeRequire(deps, callback, errback);
export const resetRequire = () => requireErrorHandler.resetRequire();
