/**
 * @Create: 周颖仁
 * @Date: 2023/11/1
 * @desc: 发起会议
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/1
 */

import React, { useState, useContext } from "react";
import { useNavigate } from "react-router-dom";
import style from './style/sass/index.module.scss';
import { Button, Input } from "antd-mobile";
import { dispatch_web_api } from "../../../../util/api";
import dayjs from 'dayjs';
import PubSub from 'pubsub-js';
import {
  AudioOutlined,
  AudioMutedOutlined,
  VideoCameraOutlined,
  EyeInvisibleOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { MainContext } from "../../context";

function StartMeeting({ onBack }) {
  const navigate = useNavigate();
  const { setShowType } = useContext(MainContext);

  const [isMicOn, setIsMicOn] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);

  // 获取用户信息
  const ownerInformation = JSON.parse(localStorage.getItem('scooperOwnerInformation') || '{}');

  const userName = ownerInformation.name || '默认用户名'; // 默认用户名

  // 会议名称相关状态
  const [meetingName, setMeetingName] = useState(`${userName}发起的会议`);

  // 返回会议主页
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      setShowType('normal');
    }
  };

  // 切换麦克风状态
  const toggleMic = () => {
    setIsMicOn(!isMicOn);
  };

  // 切换摄像头状态
  const toggleCamera = () => {
    setIsCameraOn(!isCameraOn);
  };



  // 处理会议名称输入
  const handleNameChange = (value) => {
    setMeetingName(value);
  };

  // 进入会议
  const handleStartMeeting = () => {
    console.log('发起会议:', {
      meetingName,
      userName,
      micOn: isMicOn, //  
      cameraOn: isCameraOn //
    });
    const setCurMeetAccess = () => {
      let year = dayjs().format('YYYY');
      let optsNum = '';
      for (var i = 0; i < 6; i++) {
          optsNum += Math.floor(Math.random() * 10);
      }
     return year + optsNum
  }

  const meetingId = setCurMeetAccess()
    const paramsData = {
      businessId: '',
      name: meetingName,
      meetAccess: meetingId, // 随机8位纯数字
      meetAttr: "MEET_INSTANT",
      passwdSpeaker: '',
      passwdAudience: '',
      defineAttr: '',
      videoInfo: 'video_call',
      isMute: 0,
      mediaType: 'video',
  }
  // 订阅会议创建消息
  PubSub.subscribe('MEET_LST', (msg, data) => {
    console.log('会议创建消息:', data);
    const {meetName,mediaType} = data.meet
      dispatch_web_api.getMeetByMeetAccess({meetAccess: meetingId}).then(res => {
        const tel = ownerInformation.mainTel || '';
        const meetingData = {
          meetId: res.data.meetId,
          meetName: meetName,
          meetAccess: meetingId,
          meetMediaType: mediaType,
        };
        if(res.code === 0) {
          if(data.meet.meetId === res.data.meetId) {
              sessionStorage.setItem('curParam', JSON.stringify(meetingData)); // 保存会议数据
              dispatch_web_api.joinVideoMember({id: res.data.meetId, tel: tel, level: isMicOn ? 'speak' : 'listen', businessId: '', autoAnswer: '', meetAccess: meetingId}).then(res => {
                if(res.code === 0) {
                  // 删除监听
                  PubSub.unsubscribe('MEET_LST');
                  // 创建会议数据
                  navigate('/main/meeting/room', {
                    state: {
                      meetingData: meetingData,
                      isHost: true
                    }
                  });
                }
              })
          }
        }
      })
  });

  dispatch_web_api.createMeet(paramsData).then(res => {
    console.log(res)
    if(res.code === 0) {
    }else{
      PubSub.unsubscribe('MEET_LST');
    }
  })
  };

  return (
    <div className={style['start-meeting']}>
      {/* 装饰元素 */}
      <div className={style['decoration-left']}></div>
      <div className={style['decoration-right']}></div>

      {/* 顶部关闭按钮 */}
      <div className={style.header}>
        <div className={style['close-button']} onClick={handleBack}>
          <CloseOutlined style={{color: '#fff'}}/>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className={style.content}>
        {/* 会议标题区域 */}
        <div className={style['title-section']}>
          <div className={style['title-input-container']}>
            <Input
              value={meetingName}
              onChange={handleNameChange}
              placeholder="请输入会议名称"
              className={style['title-input']}
              style={{
                '--color': '#ffffff',
                '--placeholder-color': 'rgba(255, 255, 255, 0.7)',
                color: '#ffffff'
              }}
            />
          </div>
          <div className={style['title-underline']}></div>
        </div>

        {/* 控制按钮区域 */}
        <div className={style['control-section']}>
          <div className={style['control-buttons']}>
            <div
              className={`${style['control-button']} ${!isMicOn ? style.disabled : ''}`}
              onClick={toggleMic}
            >
              {isMicOn ? <AudioOutlined style={{color: '#fff'}}/> : <AudioMutedOutlined style={{color: '#fff'}}/>}
            </div>

            <div
              className={`${style['control-button']} ${!isCameraOn ? style.disabled : ''}`}
              onClick={toggleCamera}
            >
              {isCameraOn ? <VideoCameraOutlined style={{color: '#fff'}}/> : <EyeInvisibleOutlined style={{color: '#fff'}}/>}
            </div>
          </div>
        </div>

        {/* 进入会议按钮 */}
        <div className={style['start-button-section']}>
          <Button
            block
            color="primary"
            size="large"
            className={style['start-button']}
            onClick={handleStartMeeting}
          >
            创建会议
          </Button>
        </div>
      </div>
    </div>
  );
}

export default StartMeeting;
