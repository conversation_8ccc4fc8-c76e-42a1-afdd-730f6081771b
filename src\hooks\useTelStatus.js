/**
 * @Create: 号码状态 Hook
 * @Date: 2025/01/28
 * @desc: 提供号码状态相关的 Hook
 */

import { useContext, useMemo } from 'react';
import { MainContext } from '../view/main/context';
import { 
    getTelStatus, 
    getTelStatusText, 
    getTelStatusColor,
    isTelOnline,
    canCallTel,
    isTelInCall,
    getOnlineTels,
    getCallableTels,
    getBusyTels,
    getTelStatusStats
} from '../util/telStatusUtils';

/**
 * 使用号码状态的 Hook
 * @returns {Object} 号码状态相关的方法和数据
 */
export const useTelStatus = () => {
    const { telStatusList, setTelStatusList, initTelStatus } = useContext(MainContext);

    // 获取单个号码状态
    const getTelStatusInfo = useMemo(() => (tel) => {
        return getTelStatus(tel, telStatusList);
    }, [telStatusList]);

    // 获取号码状态文本
    const getStatusText = useMemo(() => (tel) => {
        return getTelStatusText(tel, telStatusList);
    }, [telStatusList]);

    // 获取号码状态颜色
    const getStatusColor = useMemo(() => (tel) => {
        return getTelStatusColor(tel, telStatusList);
    }, [telStatusList]);

    // 检查号码是否在线
    const isOnline = useMemo(() => (tel) => {
        return isTelOnline(tel, telStatusList);
    }, [telStatusList]);

    // 检查号码是否可以拨打
    const canCall = useMemo(() => (tel) => {
        return canCallTel(tel, telStatusList);
    }, [telStatusList]);

    // 检查号码是否正在通话中
    const isInCall = useMemo(() => (tel) => {
        return isTelInCall(tel, telStatusList);
    }, [telStatusList]);

    // 获取在线号码列表
    const onlineTels = useMemo(() => {
        return getOnlineTels(telStatusList);
    }, [telStatusList]);

    // 获取可拨打号码列表
    const callableTels = useMemo(() => {
        return getCallableTels(telStatusList);
    }, [telStatusList]);

    // 获取通话中号码列表
    const busyTels = useMemo(() => {
        return getBusyTels(telStatusList);
    }, [telStatusList]);

    // 获取统计信息
    const statusStats = useMemo(() => {
        return getTelStatusStats(telStatusList);
    }, [telStatusList]);

    // 更新单个号码状态
    const updateTelStatus = (tel, statusData) => {
        if (!tel || !statusData) return;
        
        setTelStatusList(prev => ({
            ...prev,
            [tel]: {
                ...prev[tel],
                ...statusData,
                lastUpdateTime: new Date().toISOString()
            }
        }));
        
        console.log(`📱 [号码状态] 手动更新 ${tel}:`, statusData);
    };

    // 批量更新号码状态
    const batchUpdateTelStatus = (updates) => {
        if (!updates || typeof updates !== 'object') return;
        
        setTelStatusList(prev => {
            const updated = { ...prev };
            const currentTime = new Date().toISOString();
            
            Object.keys(updates).forEach(tel => {
                if (updates[tel]) {
                    updated[tel] = {
                        ...updated[tel],
                        ...updates[tel],
                        lastUpdateTime: currentTime
                    };
                }
            });
            
            return updated;
        });
        
        console.log(`📱 [号码状态] 批量更新 ${Object.keys(updates).length} 个号码`);
    };

    // 移除号码状态
    const removeTelStatus = (tel) => {
        if (!tel) return;
        
        setTelStatusList(prev => {
            const updated = { ...prev };
            delete updated[tel];
            return updated;
        });
        
        console.log(`📱 [号码状态] 移除 ${tel}`);
    };

    // 清空所有号码状态
    const clearAllTelStatus = () => {
        setTelStatusList({});
        console.log('📱 [号码状态] 清空所有状态');
    };

    // 重新初始化号码状态
    const refreshTelStatus = () => {
        if (initTelStatus) {
            initTelStatus();
            console.log('📱 [号码状态] 重新初始化');
        }
    };

    return {
        // 数据
        telStatusList,
        onlineTels,
        callableTels,
        busyTels,
        statusStats,
        
        // 查询方法
        getTelStatusInfo,
        getStatusText,
        getStatusColor,
        isOnline,
        canCall,
        isInCall,
        
        // 更新方法
        updateTelStatus,
        batchUpdateTelStatus,
        removeTelStatus,
        clearAllTelStatus,
        refreshTelStatus
    };
};

/**
 * 使用单个号码状态的 Hook
 * @param {string} tel - 号码
 * @returns {Object} 单个号码的状态信息
 */
export const useSingleTelStatus = (tel) => {
    const { telStatusList } = useContext(MainContext);
    
    const statusInfo = useMemo(() => {
        return getTelStatus(tel, telStatusList);
    }, [tel, telStatusList]);
    
    return statusInfo;
};

/**
 * 使用多个号码状态的 Hook
 * @param {Array} tels - 号码数组
 * @returns {Object} 多个号码的状态信息
 */
export const useMultipleTelStatus = (tels = []) => {
    const { telStatusList } = useContext(MainContext);
    
    const statusInfos = useMemo(() => {
        const result = {};
        tels.forEach(tel => {
            if (tel) {
                result[tel] = getTelStatus(tel, telStatusList);
            }
        });
        return result;
    }, [tels, telStatusList]);
    
    const onlineCount = useMemo(() => {
        return Object.values(statusInfos).filter(info => info.isOnline).length;
    }, [statusInfos]);
    
    const callableCount = useMemo(() => {
        return Object.values(statusInfos).filter(info => info.canCall).length;
    }, [statusInfos]);
    
    const busyCount = useMemo(() => {
        return Object.values(statusInfos).filter(info => 
            ['callst_doubletalk', 'callst_meet'].includes(info.status)
        ).length;
    }, [statusInfos]);
    
    return {
        statusInfos,
        total: tels.length,
        onlineCount,
        callableCount,
        busyCount,
        offlineCount: tels.length - onlineCount
    };
};

export default useTelStatus;
