/*
 * @File: Redux store配置
 * @Author: liulian
 * @Date: 2020-10-29 10:08:20
 * @version: V0.0.0.1
 */
import { createStore, combineReducers, applyMiddleware } from 'redux';
import { createMeetReducer } from '../reducers/create-meet-reducer';
import { screenManageReducer } from '../reducers/screen-manage-reducer';
import { meetDetailReducer } from '../reducers/meet-detail-reducer';
import { loadingReducer } from '../reducers/loading-reducer';

// 合并所有reducer
const rootReducer = combineReducers({
    createMeet: createMeetReducer,
    screenManage: screenManageReducer,
    meetDetail: meetDetailReducer,
    loading: loadingReducer
});

// 创建中间件（如果需要的话）
const middleware = [];

// 开发环境下添加Redux DevTools支持
const composeEnhancers = 
    (typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__) || 
    ((funcs) => funcs.reduce((a, b) => (...args) => a(b(...args))));

// 创建store
const store = createStore(
    rootReducer,
    composeEnhancers(applyMiddleware(...middleware))
);

export default store;
