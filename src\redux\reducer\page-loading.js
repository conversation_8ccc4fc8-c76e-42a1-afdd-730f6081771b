/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 全局页面加载reducer
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

import { createSlice } from "@reduxjs/toolkit";

export const pageLoadingSlice = createSlice({
    name: 'pageLoading',
    initialState: {
        delay: 100,
        tip: '页面加载中...',
        showLoading: false,
    },
    reducers: {
        changePageLoading: (state, action) => {
            return{
                ...state,
                ...action.payload
            }
        }
    }
})

export const { changePageLoading } = pageLoadingSlice.actions;

export default pageLoadingSlice.reducer;
