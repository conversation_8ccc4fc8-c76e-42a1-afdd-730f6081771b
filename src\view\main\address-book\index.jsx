/**
 * @Create: 周颖仁
 * @Date: 2023/11/1
 * @desc: 通讯录
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/1
 */

import React, {useContext, useEffect, useRef, useState} from "react";
import style from './style/sass/index.module.scss';
import {scooper_core_rest_api} from "../../../util/api";
import {Breadcrumb, Button, Input, List} from "antd";
import {RightOutlined, ApartmentOutlined, UserOutlined} from "@ant-design/icons";
import {cloneDeep} from "lodash";
import {useDebounce} from "ahooks";
import {MainContext} from "../context";

function AddressBook() {

	const myBreadcrumb = useRef();
	const mySearch = useRef();

	const { showType, setShowType, showTypeRef, setSelectedMemId, setShowMemInfo } = useContext(MainContext);

	const [currentDeptDataList, setCurrentDeptDataList] = useState([]);
	const [searchPersonList, setSearchPersonList] = useState([]);
	const [breadcrumbList, setBreadcrumbList] = useState([]);
	const [searchKey, setSearchKey] = useState(undefined);
	const [loading, setLoading] = useState(false);

	const debounceSearch = useDebounce(searchKey, { wait: 200 });

	/**
	 * @desc 查询首层部门树并提供给面包屑
	 */
	useEffect(() => {
		let params = {
			id: 0,
			expandMember: true,
		}
		scooper_core_rest_api.listDeptByParent(params).then(data => {
			let breadcrumbList = (data || []).map(item => {
				return({
					name: item.name,
					deptId: item.id,
					id: item.id
				})
			})
			setBreadcrumbList(breadcrumbList);
		}).catch(error => {
			console.log(`listDeptByParent: ${error}`);
		})
	}, []);

	/**
	 * @desc 根据部门id查询部门及成员
	 */
	useEffect(() => {
		if(!breadcrumbList.length) return;
		setCurrentDeptDataList([]);
		let currentDept = breadcrumbList[breadcrumbList.length - 1];
		setLoading(true);
		let params = {
			id: currentDept.deptId,
			expandMember: true
		}
		scooper_core_rest_api.listDeptByParent(params).then(data => {
			setCurrentDeptDataList(data || []);
			setLoading(false);
		}).catch(error => {
			console.log(`listDeptByParent: ${error}`);
			setLoading(false);
		})
	}, [breadcrumbList]);

	/**
	 * @desc 面包屑跟新后移动面包屑位置以确保最后一个面包屑处于最末尾
	 */
	useEffect(() => {
		myBreadcrumb.current.scrollLeft = myBreadcrumb.current.scrollWidth;
	}, [breadcrumbList]);

	/**
	 * @desc 根据关键字查询人员信息
	 */
	useEffect(() => {
		setSearchPersonList([]);
		if(!debounceSearch) return;
		let param = {
			keyword: debounceSearch
		}
		setLoading(true);
		scooper_core_rest_api.listOrgMember(param).then(data => {
			setSearchPersonList(data || []);
			setLoading(false);
		}).catch(error => {
			console.log(`listOrgMember: ${error}`);
			setLoading(false);
		})
	}, [debounceSearch]);

	/**
	 * @desc 面包屑点击事件
	 * @param breadcrumb 当前点击的面包屑数据
	 */
	function breadcrumbClick(breadcrumb) {
		let index = breadcrumbList.findIndex(item => item.deptId === breadcrumb.deptId);
		if(index !== breadcrumbList.length - 1) {
			let newBreadcrumbList = cloneDeep(breadcrumbList);
			newBreadcrumbList = newBreadcrumbList.slice(0, index + 1);
			setBreadcrumbList(newBreadcrumbList);
		}
	}

	/**
	 * @desc 列表点击事件
	 * @param info 部门或人员数据
	 */
	function listClick(info) {
		if(info.dataType === 'orgDept') {
			let newBreadcrumbList = cloneDeep(breadcrumbList);
			newBreadcrumbList.push({
				name: info.name,
				deptId: info.id
			})
			setBreadcrumbList(newBreadcrumbList);
		}else {
			setShowMemInfo(true);
			setSelectedMemId(info.id);
		}
	}

	/**
	 * @desc 搜索功能聚焦时
	 */
	function searchFocus() {
		if(showTypeRef.current === 'normal') {
			setShowType('search');
			showTypeRef.current = 'search';
		}
	}

	/**
	 * @desc 搜索框失去焦点
	 */
	function searchBlur() {
		if(showTypeRef.current === 'search') {
			mySearch.current.focus();
		}
	}

	/**
	 * @desc 取消搜索
	 */
	function cancelSearch() {
		setShowType('normal');
		showTypeRef.current = 'normal';
		setSearchKey(undefined);
		mySearch.current.blur();
	}

	return(
		<div className={style.addressBook}>
			<div className={style.searchContent}>
				<Input
					value={searchKey}
					ref={mySearch}
					prefix={<UserOutlined />}
					placeholder='请输入查询的姓名'
					size='large'
					allowClear
					onChange={e => setSearchKey(e.target.value)}
					onFocus={(e) => searchFocus(e)}
					onBlur={() => searchBlur()}
				/>
				{
					showType === 'search' && <Button size='large' type='link' onClick={() => cancelSearch()}>取消</Button>
				}
			</div>
			<div className={style.personListContent}>
				{
					showType === 'normal' &&
					<>
						<div className={style.breadcrumbContent} ref={myBreadcrumb}>
							<Breadcrumb className={style.myBreadcrumb} separator={'>'} >
								{
									breadcrumbList.map((item, index) => {
										return(
											<Breadcrumb.Item className={style.unitBreadcrumbItem} key={item.deptId} onClick={() => breadcrumbClick(item)}>
												<span className={`${index !== breadcrumbList.length - 1 ? style.currentBreadcrumb: ''}`}>{item.name}</span>
											</Breadcrumb.Item>
										)
									})
								}
							</Breadcrumb>
						</div>
						<div className={style.personList}>
							<List
								broder={true}
								dataSource={currentDeptDataList}
								loading={loading}
								renderItem={item => {
									return(
										<List.Item
											className={style.unitDeptInfo}
											key={`${item.dataType === 'orgDept'? 'dept': 'mem'}-${item.id}`}
											onClick={() => listClick(item)}
											extra={item.dataType  === 'orgDept'? <RightOutlined />: null}
											avatar={item.dataType === 'orgDept'? <ApartmentOutlined />: null}
										>
											<List.Item.Meta
												avatar={item.dataType === 'orgDept'? <ApartmentOutlined />: <UserOutlined />}
												title={item.name}
											/>
										</List.Item>
									)
								}}
							/>

						</div>
					</>
				}
				{
					showType === 'search' &&
					<div className={style.searchPersonList}>
						<List
							broder={true}
							dataSource={searchPersonList}
							loading={loading}
							renderItem={item => {
								return(
									<List.Item
										className={style.unitPersonInfo}
										key={`mem-${item.id}`}
										onClick={() => listClick(item)}
									>
										<List.Item.Meta
											avatar={<UserOutlined />}
											title={item.memName}
										/>
									</List.Item>
								)
							}}
						/>
					</div>

				}
			</div>
		</div>
	)
}

export default AddressBook;