.start-meeting {
  width: 100%;
  height: 100vh;
  background: linear-gradient(180deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  
  // CSS变量定义
  --text-color: #ffffff;
  --input-text-color: #ffffff;
  --input-caret-color: #ffffff;
  --input-placeholder-color: rgba(255, 255, 255, 0.6);
  --input-selection-color: rgba(255, 255, 255, 0.4);
  --button-active-color: rgba(255, 255, 255, 0.15);
  --button-disabled-color: rgba(255, 255, 255, 0.05);

  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    padding: calc(env(safe-area-inset-top) + 16px) 20px 16px;
    display: flex;
    justify-content: flex-start;

    .close-button {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      .anticon {
        font-size: 20px;
        color: var(--text-color);
      }

      &:active {
        transform: scale(0.9);
        opacity: 0.7;
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: calc(64px + env(safe-area-inset-top) + 40px) 20px calc(env(safe-area-inset-bottom) + 40px);
    position: relative;

    .title-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: -60px;
      position: relative;

      // 添加标题区域的背景装饰
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120%;
        height: 200px;
        background: radial-gradient(ellipse at center,
          rgba(255, 255, 255, 0.08) 0%,
          rgba(255, 255, 255, 0.03) 40%,
          transparent 70%
        );
        border-radius: 50%;
        pointer-events: none;
        z-index: 0;
      }

      .title-input-container {
        width: 100%;
        max-width: 320px;
        margin-bottom: 24px;
        position: relative;
        z-index: 1;

        // 聚焦时的外发光效果
        &:focus-within {
          &::before {
            content: '';
            position: absolute;
            top: -4px;
            left: -4px;
            right: -4px;
            bottom: -4px;
            background: linear-gradient(45deg,
              rgba(255, 255, 255, 0.5),
              rgba(255, 255, 255, 0.2),
              rgba(255, 255, 255, 0.5)
            );
            border-radius: 16px;
            z-index: -1;
            opacity: 1;
            transition: opacity 0.3s ease;
            animation: glow 2s ease-in-out infinite alternate;
          }
        }

        @keyframes glow {
          from {
            box-shadow: 0 0 25px rgba(255, 255, 255, 0.3);
          }
          to {
            box-shadow: 0 0 35px rgba(255, 255, 255, 0.5);
          }
        }

        .title-input {
          .adm-input {
            background: rgba(255, 255, 255, 0.12);
            border: 2px solid rgba(255, 255, 255, 0.35);
            border-radius: 14px;
            color: var(--input-text-color);
            font-size: 22px;
            padding: 18px 24px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 56px;
            caret-color: var(--input-caret-color);
            font-weight: 600;
            letter-spacing: 1.2px;
            line-height: 1.3;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            box-shadow:
              0 4px 20px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);

            // 强制设置光标和文字颜色
            &,
            &:focus,
            &:active,
            &:focus-visible,
            &:focus-within {
              caret-color: #ffffff !important;
              color: #ffffff !important;
            }

            // 确保输入框内的文本为白色
            input {
              color: #ffffff !important;
              caret-color: #ffffff !important;
            }

            &::placeholder {
              color: rgba(255, 255, 255, 0.7) !important;
              font-size: 19px;
              font-weight: 500;
              letter-spacing: 0.8px;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            &:focus {
              border-color: rgba(255, 255, 255, 0.8);
              background: rgba(255, 255, 255, 0.18);
              outline: none;
              box-shadow:
                0 0 0 4px rgba(255, 255, 255, 0.2),
                0 12px 30px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
              transform: translateY(-3px) scale(1.02);
            }

            // 文本选择样式
            &::selection {
              background: rgba(255, 255, 255, 0.4);
              color: #ffffff !important;
            }

            &::-moz-selection {
              background: rgba(255, 255, 255, 0.4);
              color: #ffffff !important;
            }

            // 针对不同浏览器的占位符优化
            &::-webkit-input-placeholder {
              color: rgba(255, 255, 255, 0.7) !important;
              opacity: 1;
            }

            &::-moz-placeholder {
              color: rgba(255, 255, 255, 0.7) !important;
              opacity: 1;
            }

            &:-ms-input-placeholder {
              color: rgba(255, 255, 255, 0.7) !important;
            }

            // 额外的占位符样式确保兼容性
            &::placeholder {
              color: rgba(255, 255, 255, 0.7) !important;
            }

            // 移动端触摸优化
            &:focus {
              -webkit-tap-highlight-color: transparent;
              -webkit-user-select: text;
              user-select: text;
            }
          }
        }
      }
      .title-input{
        color: #fff !important;

        // 使用 :global() 来匹配 antd-mobile 的类名
        :global(.adm-input-element) {
          color: #fff !important;
          caret-color: #fff !important;
          text-align: center;

        }

        // 也可以直接匹配 input 元素
        input {
          color: #fff !important;
          caret-color: #fff !important;
          text-align: center;
          
        }

        // 确保所有可能的输入元素都是白色
        :global(.adm-input) {
          color: #fff !important;

          input {
            color: #fff !important;
            caret-color: #fff !important;
          }
        }
      }

      .title-underline {
        width: 80px;
        height: 2px;
        background: linear-gradient(90deg,
          transparent,
          rgba(255, 255, 255, 0.8) 20%,
          rgba(255, 255, 255, 0.9) 50%,
          rgba(255, 255, 255, 0.8) 80%,
          transparent
        );
        border-radius: 2px;
        position: relative;
        z-index: 1;

        // 添加发光效果
        &::after {
          content: '';
          position: absolute;
          top: -1px;
          left: -5px;
          right: -5px;
          bottom: -1px;
          background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
          );
          border-radius: 4px;
          filter: blur(2px);
          z-index: -1;
        }
      }
    }

    .control-section {
      margin-bottom: 40px;

      .control-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 40px;

        .control-button {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: var(--button-active-color);
          border: 1px solid rgba(255, 255, 255, 0.2);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease;
          backdrop-filter: blur(10px);
          position: relative;

          &:active {
            transform: scale(0.95);
            background: rgba(255, 255, 255, 0.2);
          }

          &.disabled {
            background: var(--button-disabled-color);
            border-color: rgba(255, 255, 255, 0.1);

            .anticon {
              color: rgba(255, 255, 255, 0.3);
            }

            // 禁用状态的斜线
            &::after {
              content: '';
              position: absolute;
              width: 2px;
              height: 40px;
              background: rgba(255, 255, 255, 0.5);
              transform: rotate(45deg);
              border-radius: 1px;
            }
          }

          .anticon {
            font-size: 24px;
            color: var(--text-color);
            transition: color 0.2s ease;
          }
        }
      }
    }

    .start-button-section {
      width: 100%;
      padding: 0 20px;

      .start-button {
        height: 50px;
        border-radius: 25px;
        background: #007AFF;
        border: none;
        font-size: 17px;
        font-weight: 600;
        transition: all 0.2s ease;
        box-shadow: 0 2px 10px rgba(0, 122, 255, 0.3);

        &:active {
          transform: scale(0.98);
          background: #0056CC;
        }

        .adm-button-text {
          color: #ffffff;
          font-weight: 600;
        }
      }
    }
  }

  // 移动端适配的装饰元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
  }

  // 简化的装饰元素
  .decoration-left {
    position: absolute;
    bottom: 25%;
    left: 8%;
    width: 40px;
    height: 40px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.06) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
  }

  .decoration-right {
    position: absolute;
    top: 35%;
    right: 10%;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.04) 0%, transparent 70%);
    border-radius: 50%;
    pointer-events: none;
  }

  // 移动端安全区域适配
  @supports (padding: max(0px)) {
    .content {
      padding-top: calc(64px + max(env(safe-area-inset-top), 20px) + 40px);
      padding-bottom: calc(max(env(safe-area-inset-bottom), 20px) + 40px);
    }
    
    .header {
      padding-top: calc(max(env(safe-area-inset-top), 20px) + 16px);
    }
  }
}
