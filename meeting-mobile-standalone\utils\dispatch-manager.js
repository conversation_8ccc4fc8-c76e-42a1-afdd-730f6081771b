/*
 * @File: 调度状态管理
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2021-05-18 16:38:59
 * @version: V0.0.0.1
 * @LastEditTime: 2025-01-21 14:55:49
 */
import React from 'react';
import { devmode, getToken, curMainTel } from '../config/constants'
import { message, Modal } from 'antd'
import store from '../store';
import { apis } from './api';
import { getUrlParmse, handleAudioFont, startAllop, shadleReLogin } from './method'
import { setMemIdMapCache, setMemTelMapCache, setTelStausList, setIsLeaveMeeting } from '../reducers/loading-reducer';
import { setDecodeInfoList, setLoopVideoInfo, setIntoMeetDetail } from '../reducers/meet-detail-reducer';

const { confirm } = Modal

/**
 * 调度管理器类
 * 负责管理调度相关的状态和事件
 */
class DispatchManager {
    constructor() {
        this.dispatcher = null;
        this.listenObj = null;
        this.unlistenObj = null;
        this.accountDetail = null;
        this.isInitialized = false;
    }

    /**
     * 初始化调度管理器
     */
    init() {
        try {
            // 检查是否有scooper对象
            if (window.scooper && window.scooper.dispatch) {
                this.dispatcher = window.scooper.dispatch;
                this.listenObj = this.dispatcher.listen;
                this.unlistenObj = this.dispatcher.unlisten;
                this.isInitialized = true;
                console.log('调度管理器初始化成功');
            } else {
                console.warn('未找到scooper.dispatch对象，使用模拟模式');
                this.initMockMode();
            }
        } catch (error) {
            console.error('调度管理器初始化失败:', error);
            this.initMockMode();
        }
    }

    /**
     * 初始化模拟模式
     */
    initMockMode() {
        this.dispatcher = {
            listen: () => {},
            unlisten: () => {},
            call: () => Promise.resolve(),
            hangup: () => Promise.resolve(),
            answer: () => Promise.resolve()
        };
        this.isInitialized = true;
        console.log('调度管理器以模拟模式运行');
    }

    /**
     * 设置账户详情
     */
    setAccountDetail(detail) {
        this.accountDetail = detail;
    }

    /**
     * 获取账户详情
     */
    getAccountDetail() {
        return this.accountDetail;
    }

    /**
     * 语音识别开关
     */
    opAsrSwitch(tels, status) {
        try {
            if (this.dispatcher && this.dispatcher.opAsrSwitch) {
                return this.dispatcher.opAsrSwitch(tels, status);
            } else {
                console.log('模拟语音识别开关:', { tels, status });
                return Promise.resolve();
            }
        } catch (error) {
            console.error('语音识别开关操作失败:', error);
            return Promise.reject(error);
        }
    }

    /**
     * 拨打电话
     */
    call(tel) {
        try {
            if (this.dispatcher && this.dispatcher.call) {
                return this.dispatcher.call(tel);
            } else {
                console.log('模拟拨打电话:', tel);
                return Promise.resolve();
            }
        } catch (error) {
            console.error('拨打电话失败:', error);
            return Promise.reject(error);
        }
    }

    /**
     * 挂断电话
     */
    hangup(tel) {
        try {
            if (this.dispatcher && this.dispatcher.hangup) {
                return this.dispatcher.hangup(tel);
            } else {
                console.log('模拟挂断电话:', tel);
                return Promise.resolve();
            }
        } catch (error) {
            console.error('挂断电话失败:', error);
            return Promise.reject(error);
        }
    }

    /**
     * 接听电话
     */
    answer(tel) {
        try {
            if (this.dispatcher && this.dispatcher.answer) {
                return this.dispatcher.answer(tel);
            } else {
                console.log('模拟接听电话:', tel);
                return Promise.resolve();
            }
        } catch (error) {
            console.error('接听电话失败:', error);
            return Promise.reject(error);
        }
    }

    /**
     * 监听状态变化
     */
    listen(event, callback) {
        try {
            if (this.listenObj) {
                return this.listenObj(event, callback);
            } else {
                console.log('模拟监听事件:', event);
                return () => {}; // 返回取消监听的函数
            }
        } catch (error) {
            console.error('监听事件失败:', error);
            return () => {};
        }
    }

    /**
     * 取消监听
     */
    unlisten(event, callback) {
        try {
            if (this.unlistenObj) {
                return this.unlistenObj(event, callback);
            } else {
                console.log('模拟取消监听:', event);
            }
        } catch (error) {
            console.error('取消监听失败:', error);
        }
    }

    /**
     * 销毁调度管理器
     */
    destroy() {
        try {
            // 清理所有监听器
            if (this.unlistenObj) {
                // 这里可以添加具体的清理逻辑
            }
            
            this.dispatcher = null;
            this.listenObj = null;
            this.unlistenObj = null;
            this.accountDetail = null;
            this.isInitialized = false;
            
            console.log('调度管理器已销毁');
        } catch (error) {
            console.error('销毁调度管理器失败:', error);
        }
    }
}

// 创建单例实例
const dispatchManager = new DispatchManager();

// 自动初始化
if (typeof window !== 'undefined') {
    // 延迟初始化，等待scooper对象加载
    setTimeout(() => {
        dispatchManager.init();
    }, 1000);
}

export default dispatchManager;
