/**
 * @Create: 历史会议详情页面
 * @Date: 2025/01/28
 * @desc: 历史会议详情
 */

import React from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { NavBar, Card, Avatar, Space, Tag } from "antd-mobile";
import { LeftOutlined, VideoCameraOutlined, AudioOutlined } from '@ant-design/icons';
import style from './style/sass/index.module.scss';

function MeetingDetail() {
  const navigate = useNavigate();
  const location = useLocation();
  const meetingData = location.state?.meetingData;

  // 如果没有会议数据，返回错误页面
  if (!meetingData) {
    return (
      <div className={style['meeting-detail']}>
        <NavBar onBack={() => navigate(-1)} backIcon={<LeftOutlined />}>
          历史会议详情
        </NavBar>
        <div className={style['error-container']}>
          <p>会议数据不存在</p>
        </div>
      </div>
    );
  }

  // 格式化时间
  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return '';
    return dateTimeStr.replace('T', ' ').substring(0, 19);
  };

  // 计算会议时长
  const calculateDuration = (startTime, endTime) => {
    if (!startTime || !endTime) return '未知';
    
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end - start;
    
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    
    if (hours > 0) {
      return `${hours}小时${minutes}分${seconds}秒`;
    } else if (minutes > 0) {
      return `${minutes}分${seconds}秒`;
    } else {
      return `${seconds}秒`;
    }
  };

  // 获取参与人数
  const getParticipantCount = (members) => {
    if (Array.isArray(members)) {
      return members.length;
    }
    return 1;
  };

  return (
    <div className={style['meeting-detail']}>
      {/* 导航栏 */}
      <NavBar 
        onBack={() => navigate(-1)} 
        backIcon={<LeftOutlined style={{ color: '#333' }} />}
        className={style['nav-bar']}
      >
        历史会议详情
      </NavBar>

      {/* 主要内容 */}
      <div className={style['content']}>
        {/* 会议基本信息 */}
        <Card className={style['info-card']}>
          <div className={style['meeting-header']}>
            <div className={style['meeting-icon']}>
              {meetingData.meetMediaType === 'video' ? (
                <VideoCameraOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              ) : (
                <AudioOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
              )}
            </div>
            <div className={style['meeting-title']}>
              <h2>{meetingData.meetName || '未命名会议'}</h2>
              <Tag 
                color={meetingData.meetMediaType === 'video' ? 'blue' : 'green'}
                className={style['meeting-type-tag']}
              >
                {meetingData.meetMediaType === 'video' ? '视频会议' : '音频会议'}
              </Tag>
            </div>
          </div>
        </Card>

        {/* 会议时间信息 */}
        <Card className={style['time-card']}>
          <div className={style['time-item']}>
            <span className={style['time-label']}>开始时间</span>
            <span className={style['time-value']}>
              {formatDateTime(meetingData.timeBegin)}
            </span>
          </div>
          <div className={style['time-item']}>
            <span className={style['time-label']}>结束时间</span>
            <span className={style['time-value']}>
              {formatDateTime(meetingData.timeEnd)}
            </span>
          </div>
          <div className={style['time-item']}>
            <span className={style['time-label']}>会议时长</span>
            <span className={style['time-value']}>
              {calculateDuration(meetingData.timeBegin, meetingData.timeEnd)}
            </span>
          </div>
        </Card>

        {/* 发起人信息 */}
        <Card className={style['host-card']}>
          <div className={style['card-title']}>发起人</div>
          <div className={style['host-info']}>
            <Avatar size={40} style={{ backgroundColor: '#1890ff' }}>
              {meetingData.ctrlName ? meetingData.ctrlName.charAt(0) : 'U'}
            </Avatar>
            <div className={style['host-details']}>
              <div className={style['host-name']}>
                {meetingData.ctrlName || '未知用户'}
              </div>
              <div className={style['host-tel']}>
                {meetingData.ctrlTel || ''}
              </div>
            </div>
          </div>
        </Card>

        {/* 参会人信息 */}
        <Card className={style['participants-card']}>
          <div className={style['card-title']}>
            参会人 ({getParticipantCount(meetingData.members)}人)
          </div>
          <div className={style['participants-list']}>
            {meetingData.members && Array.isArray(meetingData.members) ? (
              meetingData.members.map((member, index) => (
                <div key={index} className={style['participant-item']}>
                  <Avatar size={32} style={{ backgroundColor: '#52c41a' }}>
                    {member.name ? member.name.charAt(0) : index + 1}
                  </Avatar>
                  <span className={style['participant-name']}>
                    {member.name || `参会人${index + 1}`}
                  </span>
                </div>
              ))
            ) : (
              <div className={style['participant-item']}>
                <Avatar size={32} style={{ backgroundColor: '#1890ff' }}>
                  {meetingData.ctrlName ? meetingData.ctrlName.charAt(0) : 'U'}
                </Avatar>
                <span className={style['participant-name']}>
                  {meetingData.ctrlName || '发起人'}
                </span>
              </div>
            )}
          </div>
        </Card>

        {/* 会议详细信息 */}
        <Card className={style['details-card']}>
          <div className={style['card-title']}>会议详情</div>
          <div className={style['detail-item']}>
            <span className={style['detail-label']}>会议ID</span>
            <span className={style['detail-value']}>{meetingData.meetId}</span>
          </div>
          <div className={style['detail-item']}>
            <span className={style['detail-label']}>会议号</span>
            <span className={style['detail-value']}>{meetingData.meetAccess}</span>
          </div>
          <div className={style['detail-item']}>
            <span className={style['detail-label']}>会议类型</span>
            <span className={style['detail-value']}>
              {meetingData.meetType === 'DEFAULT' ? '普通会议' : meetingData.meetType}
            </span>
          </div>
          <div className={style['detail-item']}>
            <span className={style['detail-label']}>会议属性</span>
            <span className={style['detail-value']}>
              {meetingData.meetAttr === 'MEET_INSTANT' ? '即时会议' : meetingData.meetAttr}
            </span>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default MeetingDetail;
