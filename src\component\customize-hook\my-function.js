/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 自定义函数
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

/**
 * 根据入参名截取url中携带数据
 * @param name url
 * @return {null|string}
 */
function getQueryString(name) {
	let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
	let r = window.location.hash.split('?')[1]?.match(reg);
	if(r != null) {
		return unescape(r[2]);
	}
	return null
}

export { getQueryString }