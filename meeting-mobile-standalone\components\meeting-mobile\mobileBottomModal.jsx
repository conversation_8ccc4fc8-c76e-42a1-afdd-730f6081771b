/*
 * @Author: ya<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-05-09 17:53:03
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-11-19 09:57:14
 * @FilePath: meeting-mobile-standalone\components\meeting-mobile\mobileBottomModal.jsx
 * @Description: 移动端底部弹窗组件
 */
import { Modal,List,Avatar  } from 'antd';
import { Drawer, Button, Radio } from 'antd';
import React, { useEffect, useState } from 'react';
import {AudioTwoTone,PlaySquareOutlined,PhoneOutlined } from '@ant-design/icons'
import { getTelCallType } from '../../utils/method';
import '../../styles/mettingPeople.less'

const BottomModal = ({ visible, onClose, data }) => {
  const isJoinButton = (item) =>{
    return !['unresponse', 'quit', 'reject'].includes(item.status);
  }
  const getStatus = {
    "meeting":'会议中',
    "inMeet":'会议中',  
    'quit':'离开',
    "calling":'呼叫中',
    'unresponse':'未接听',
    'reject':'未接听',
    'outMeet':'已离会'
  }
  const mainTel = sessionStorage.getItem('meetWebMaintel')
  return (
    <Drawer
        title="参会人员"
        placement={'right'}
        closable={false}
        onClose={onClose}
        open={visible}
        className='moblieRight'
    >
        <div className='MeetBox'>
          <div className='Meeting'>
            <ul>
                {data?.map((item,index)=>{
                  if (isJoinButton(item)) {
                    return (
                      <li className={`MeetingMembersList`} key={'MeetingMembersList'+index}>
                        <div className="iconRadio">
                            {mainTel===item.tel?'我':item.memName}
                            {item.level==="speak_start"&&item.status !="quit"&&<AudioTwoTone/>}
                            {item.level==="audience"&&item.status !="quit"&&<AudioTwoTone  twoToneColor="rgb(232, 59, 70)"/>}
                        </div>
                        <div>
                            <p className="MeetingMemName">{item.memName}</p>
                            <p className="MeetingMemName">{item.deptName}</p>
                        </div>
                        {getTelCallType(item.tel || item.memTel) == 'video'?<PlaySquareOutlined />:<PhoneOutlined />}
                        <p className='right'>{getStatus[item.status]}</p>
                      </li>
                    )
                  }
                })}
            </ul>
            <ul>
              {data?.map((item,index)=>{
                  if (!isJoinButton(item)) {
                    return (
                      <li className={`MeetingMembersList liveMetted`} key={'MeetingMembersList'+index}>
                        <div className="iconRadio">
                            {mainTel===item.tel?'我':item.memName}
                            {item.level==="speak_start"&&item.status !="quit"&&<AudioTwoTone/>}
                            {item.level==="audience"&&item.status !="quit"&&<AudioTwoTone  twoToneColor="rgb(232, 59, 70)"/>}
                        </div>
                        <div>
                            <p className="MeetingMemName">{item.memName}</p>
                            <p className="MeetingMemName">{item.deptName}</p>
                        </div>
                        {getTelCallType(item.tel || item.memTel) == 'video'?<PlaySquareOutlined />:<PhoneOutlined />}
                        <p className='right'>{getStatus[item.status]}</p>
                    </li>
                    )      
                  } 
              })}
            </ul>
          </div>
        </div>
    </Drawer>
  );
};

export default BottomModal;
