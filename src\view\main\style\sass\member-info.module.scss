.member-info {
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	.nav-bar-content {
		position: relative;
		top: 0;
		width: 100%;
		height: 45px;
		border-bottom: 1px solid #F0F0F0;
	}
	.mem-action-content {
		position: relative;
		top: 24px;
		width: 100%;
		height: 260px;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-bottom: 1px solid #F0F0F0;
		.avatar {
			width: 64px;
			height: 64px;
			position: absolute;
		}
		.mem-name {
			margin-top: 24px;
			font-size: 18px;
			color: #333333;
		}
		.action-content {
			display: flex;
			justify-content: center;
			width: 100%;
			margin-top: 36px;
			.unit-action {
				width: 33.3%;
				display: flex;
				flex-direction: column;
				align-items: center;
				.icon-tel {
					width: 45px;
					height: 45px;
					background: url("../images/icon_tel.png") no-repeat;
					background-size: 100% 100%;
					cursor: pointer;
				}
				.icon-video {
					width: 45px;
					height: 45px;
					background: url("../images/icon_video.png") no-repeat;
					background-size: 100% 100%;
					cursor: pointer;
				}
				.action-name {
					margin-top: 12px;
					font-size: 16px;
				}
			}
		}
	}
	.mem-info-content {
		position: relative;
		top: 12px;
		height: calc(100% - 45px - 24px - 260px - 12px);
		overflow-y: auto;
	}
}