/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 系统操作按钮reducer
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

import { createSlice } from "@reduxjs/toolkit";

export const systemOperationSlice = createSlice({
	name: 'systemOperation',
	initialState: {
		type: 'SYSTEM_OPERATION',
		position: {
			left: 1800,
			top: 80,
		},
		dragging: false,
	},
	reducers: {
		changePosition: (state, action) => {return{...state, position: action.payload}},
		changeDragging: (state, action) => { return{...state, dragging: action.payload} }
	}
})

export const { changePosition, changeDragging } = systemOperationSlice.actions;

export default systemOperationSlice.reducer;
