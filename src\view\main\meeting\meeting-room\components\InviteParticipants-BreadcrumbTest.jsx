/**
 * @Create: 面包屑导航测试组件
 * @Date: 2025/01/28
 * @desc: 测试面包屑导航功能
 */

import React, { useState } from 'react';
import { Button, Space, Card } from 'antd';
import InviteParticipants from './InviteParticipants';

function InviteParticipantsBreadcrumbTest() {
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [invitedUsers, setInvitedUsers] = useState([]);

  // 模拟会议数据
  const meetingData = {
    meetId: 'test_meeting_breadcrumb',
    meetAccess: 'test_access_token_breadcrumb',
    meetingName: '面包屑导航测试会议'
  };

  // 处理邀请成功
  const handleInviteSuccess = (users) => {
    console.log('邀请成功的用户:', users);
    setInvitedUsers(prev => [...prev, ...users]);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>面包屑导航功能测试</h2>
      
      <Card title="测试说明" style={{ marginBottom: '20px' }}>
        <div>
          <h4>测试要点：</h4>
          <ol>
            <li><strong>默认状态</strong>：打开弹窗应显示"通讯录"作为根目录</li>
            <li><strong>部门浏览</strong>：点击部门进入下级，面包屑应显示路径</li>
            <li><strong>面包屑导航</strong>：点击面包屑中的任意层级可以跳转</li>
            <li><strong>根目录显示</strong>：在根目录只显示顶级部门，不显示成员</li>
            <li><strong>搜索功能</strong>：搜索时显示全局搜索结果</li>
          </ol>
          
          <h4>预期行为：</h4>
          <ul>
            <li>初始面包屑：<code>通讯录</code></li>
            <li>进入部门后：<code>通讯录 > 部门名称</code></li>
            <li>多级导航：<code>通讯录 > 一级部门 > 二级部门</code></li>
            <li>点击面包屑可以快速跳转到对应层级</li>
          </ul>
        </div>
      </Card>

      <Card title="控制面板" style={{ marginBottom: '20px' }}>
        <Space>
          <Button 
            type="primary" 
            onClick={() => setShowInviteModal(true)}
          >
            打开邀请弹窗（测试面包屑）
          </Button>
          
          <Button 
            onClick={() => setInvitedUsers([])}
          >
            清空邀请记录
          </Button>
        </Space>
      </Card>

      <Card title="邀请记录" style={{ marginBottom: '20px' }}>
        <div>
          <p><strong>已邀请用户数量：</strong>{invitedUsers.length}</p>
          {invitedUsers.length > 0 && (
            <div>
              <h4>邀请用户列表：</h4>
              <ul>
                {invitedUsers.map((user, index) => (
                  <li key={index}>
                    {user.name} ({user.tel}) - {user.department}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </Card>

      <Card title="测试步骤">
        <div>
          <h4>建议测试流程：</h4>
          <ol>
            <li>点击"打开邀请弹窗"按钮</li>
            <li>观察初始状态：面包屑应显示"通讯录"</li>
            <li>查看部门列表：应该只显示顶级部门</li>
            <li>点击任意部门进入：面包屑应更新为"通讯录 > 部门名"</li>
            <li>继续点击子部门：面包屑应继续延长</li>
            <li>点击面包屑中的"通讯录"：应返回根目录</li>
            <li>点击面包屑中的中间层级：应跳转到对应层级</li>
            <li>测试搜索功能：输入关键字搜索用户</li>
            <li>选择用户并邀请：验证邀请功能正常</li>
          </ol>
          
          <h4>注意事项：</h4>
          <ul>
            <li>确保已正确配置 scooper_core_rest_api</li>
            <li>检查网络请求是否正常</li>
            <li>观察控制台日志输出</li>
            <li>验证面包屑点击交互</li>
          </ul>
        </div>
      </Card>

      {/* 邀请参会人弹窗 */}
      <InviteParticipants
        visible={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        meetingData={meetingData}
        onInviteSuccess={handleInviteSuccess}
      />
    </div>
  );
}

export default InviteParticipantsBreadcrumbTest;
