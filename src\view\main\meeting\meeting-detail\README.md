# 历史会议详情页面

## 功能说明

### 页面概述
历史会议详情页面用于显示已结束会议的详细信息，包括会议基本信息、时间信息、参与人员等。

### 主要功能
1. **会议基本信息展示**
   - 会议名称
   - 会议类型（视频/音频）
   - 会议状态标识

2. **时间信息展示**
   - 开始时间
   - 结束时间
   - 会议时长（自动计算）

3. **人员信息展示**
   - 发起人信息（头像、姓名、电话）
   - 参会人列表（头像、姓名）
   - 参会人数统计

4. **会议详细信息**
   - 会议ID
   - 会议号
   - 会议类型
   - 会议属性

## 页面路由

### 路由路径
```
/main/meeting/detail
```

### 路由参数
通过 `location.state` 传递会议数据：
```javascript
navigate('/main/meeting/detail', {
  state: {
    meetingData: {
      meetId: "会议ID",
      meetName: "会议名称",
      meetAccess: "会议号",
      meetMediaType: "video|audio",
      meetType: "会议类型",
      meetAttr: "会议属性",
      timeBegin: "开始时间",
      timeEnd: "结束时间",
      ctrlName: "发起人姓名",
      ctrlTel: "发起人电话",
      members: [
        {
          name: "参会人姓名"
        }
      ]
    }
  }
});
```

## 使用方法

### 从历史会议列表跳转
1. 在历史会议列表页面点击任意会议项
2. 系统自动跳转到会议详情页面
3. 页面显示该会议的详细信息

### 返回操作
- 点击导航栏的返回按钮
- 系统返回到上一个页面（通常是会议列表页面）

## 技术实现

### 核心技术
- **React Hooks**: 使用 `useLocation` 和 `useNavigate`
- **React Router**: 页面路由和参数传递
- **Ant Design Mobile**: UI组件库
- **SCSS Modules**: 样式管理

### 数据处理
- **时间格式化**: 将ISO时间格式转换为可读格式
- **时长计算**: 根据开始和结束时间计算会议时长
- **参与人数统计**: 统计参会人员数量
- **错误处理**: 处理缺少会议数据的情况

### 响应式设计
- **移动端优化**: 适配不同屏幕尺寸
- **暗色模式支持**: 自动适配系统主题
- **安全区域适配**: 支持刘海屏等特殊屏幕

## 样式特点

### 卡片式布局
- 使用 Card 组件分组显示信息
- 清晰的信息层次结构
- 良好的视觉分隔

### 图标和标识
- 会议类型图标（视频/音频）
- 用户头像显示
- 状态标签展示

### 交互体验
- 流畅的页面过渡
- 清晰的导航指示
- 友好的错误提示

## 文件结构

```
meeting-detail/
├── index.jsx                 # 主组件文件
├── style/
│   └── sass/
│       └── index.module.scss # 样式文件
└── README.md                 # 说明文档
```

## 扩展功能

### 可能的扩展
1. **会议录制回放**: 如果有录制文件，可以添加播放功能
2. **会议报告导出**: 导出会议详情为PDF或其他格式
3. **会议评价**: 添加会议质量评价功能
4. **分享功能**: 分享会议详情给其他人
5. **重新发起**: 基于历史会议信息重新发起新会议

### 数据扩展
- 会议质量统计（网络质量、音视频质量等）
- 参会人员行为统计（发言时长、屏幕共享等）
- 会议文件和聊天记录
- 会议纪要和决议事项
