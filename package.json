{"name": "wll-h5", "version": "1.0.2", "private": true, "homepage": ".", "dependencies": {"@babel/core": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@reduxjs/toolkit": "^1.9.5", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "ahooks": "^3.7.7", "antd": "^5.10.3", "antd-mobile": "^5.33.0", "antd-mobile-icons": "^0.3.0", "archiver": "^5.3.1", "axios": "^1.4.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "cometd": "^8.0.9", "crypto-js": "^4.1.1", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dayjs": "^1.11.7", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "http-proxy-middleware": "^2.0.6", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "^2.4.5", "mkdirp": "^3.0.1", "ol": "^7.4.0", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "pubsub-js": "^1.9.5", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-redux": "^8.0.5", "react-refresh": "^0.11.0", "react-router": "^6.11.1", "react-router-dom": "^6.11.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass": "^1.62.1", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "web-vitals": "^2.1.0", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "scripts": {"start": "node scripts/start.js", "build": "node scripts/build.js && node generateZipFile.js", "test": "node scripts/test.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "parserOptions": {"babelOptions": {"presets": [["babel-preset-react-app", false], "babel-preset-react-app/prod"]}}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}