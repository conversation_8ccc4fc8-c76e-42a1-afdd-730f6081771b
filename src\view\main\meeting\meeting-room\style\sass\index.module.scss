.meeting-room {
  width: 100%;
  height: 100vh;
  height: calc(var(--vh, 1vh) * 100);
  background: #000000;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  
  // 确保在移动端全屏显示
  -webkit-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;

  // 会议信息栏
  .meeting-header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    // 不可点击
    pointer-events: none;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.8) 0%,
      rgba(0, 0, 0, 0.4) 50%,
      transparent 100%
    );
    padding: calc(env(safe-area-inset-top) + 16px) 20px 20px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .meeting-info {
      flex: 1;
      // 不可点击
      pointer-events: none;
      .meeting-title {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 4px 0;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
      }

      .meeting-status {
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;

        &::before {
          content: '';
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background: #52c41a;
          animation: pulse 2s infinite;
        }
      }
    }

    .meeting-time {
      color: rgba(255, 255, 255, 0.9);
      font-size: 16px;
      font-weight: 500;
      font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;

      .duration-label {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 400;
        font-family: inherit;
      }

      .duration-value {
        font-size: 18px;
        color: #52c41a;
        font-weight: 600;
        font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        background: rgba(82, 196, 26, 0.1);
        padding: 4px 12px;
        border-radius: 20px;
        border: 1px solid rgba(82, 196, 26, 0.3);
        text-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
        letter-spacing: 1px;
        min-width: 80px;
        text-align: center;

        // 数字跳动动画
        transition: all 0.3s ease;

        &:hover {
          background: rgba(82, 196, 26, 0.2);
          border-color: rgba(82, 196, 26, 0.5);
          transform: scale(1.05);
        }
      }
    }
  }

  // 连接状态指示器
  .connection-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 200;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 24px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    color: #ffffff;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    span {
      font-size: 16px;
      font-weight: 500;
    }
  }

  // 动画定义
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .meeting-header {
      padding: calc(env(safe-area-inset-top) + 12px) 16px 16px;

      .meeting-info .meeting-title {
        font-size: 16px;
      }

      .meeting-time {
        font-size: 14px;
        gap: 6px;
        margin-top: 6px;

        .duration-label {
          font-size: 12px;
        }

        .duration-value {
          font-size: 16px;
          padding: 3px 10px;
          min-width: 70px;
        }
      }
    }

    .connection-indicator {
      padding: 20px 24px;
      
      .loading-spinner {
        width: 28px;
        height: 28px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  // 横屏适配
  @media (orientation: landscape) and (max-height: 500px) {
    .meeting-header {
      padding: calc(env(safe-area-inset-top) + 8px) 16px 12px;

      .meeting-info .meeting-title {
        font-size: 14px;
      }

      .meeting-time {
        font-size: 12px;
        gap: 4px;
        margin-top: 4px;

        .duration-label {
          font-size: 10px;
        }

        .duration-value {
          font-size: 14px;
          padding: 2px 8px;
          min-width: 60px;
        }
      }
    }
  }

  // 暗色模式适配
  @media (prefers-color-scheme: dark) {
    // 已经是暗色主题，无需额外适配
  }

  // 高对比度模式适配
  @media (prefers-contrast: high) {
    .meeting-header {
      background: rgba(0, 0, 0, 0.9);
    }

    .connection-indicator {
      background: rgba(0, 0, 0, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
  }

  // 减少动画模式适配
  @media (prefers-reduced-motion: reduce) {
    .meeting-header {
      transition: none;
    }

    .loading-spinner {
      animation: none;
      border-top-color: #ffffff;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; transform: none; }
    }
  }
}
