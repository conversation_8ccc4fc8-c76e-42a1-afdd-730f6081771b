// 邀请参会人组件样式
.invite-modal {
  .ant-modal-header {
    border-bottom: 1px solid #f0f0f0;
    padding: 16px 24px;
  }

  .ant-modal-body {
    padding: 0;
    max-height: 600px;
    overflow: hidden;
  }

  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 0;
  }
}

.invite-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  // 搜索区域
  .search-section {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;

    .search-input {
      .ant-input {
        border-radius: 20px;
        background: #f5f5f5;
        border: none;
        padding: 8px 16px;

        &:focus {
          background: #ffffff;
          border: 1px solid #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      .ant-input-prefix {
        color: #999999;
      }
    }
  }

  // 面包屑导航
  .breadcrumb {
    padding: 12px 16px;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    overflow-x: auto;
    white-space: nowrap;

    .breadcrumb-path {
      font-size: 12px;
      color: #666666;
      display: inline-block;
    }

    .breadcrumb-item {
      font-size: 12px;
      transition: color 0.3s ease;

      &:hover {
        color: #40a9ff !important;
      }
    }
  }

  // 内容区域
  .content-area {
    flex: 1;
    overflow-y: auto;
    max-height: 400px;
  }
}

// 部门列表
.department-list {
  .section-title {
    padding: 12px 16px;
    font-size: 14px;
    color: #666666;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .department-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #f5f5f5;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }

    .department-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .department-details {
        display: flex;
        align-items: center;
        gap: 8px;

        .department-name {
          font-size: 16px;
          color: #333333;
          font-weight: 500;
        }

        .department-count {
          font-size: 14px;
          color: #999999;
        }
      }
    }

    .arrow-icon {
      font-size: 16px;
      color: #cccccc;
    }
  }
}

// 用户列表
.user-list {
  .department-header {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 10;

    .back-btn {
      padding: 0;
      margin-right: 8px;
      color: #1890ff;

      &:hover {
        color: #40a9ff;
      }
    }

    .department-title {
      font-size: 16px;
      color: #333333;
      font-weight: 500;
    }
  }

  .section-title {
    padding: 12px 16px;
    font-size: 14px;
    color: #666666;
    background: #fafafa;
    border-bottom: 1px solid #f0f0f0;
  }

  .user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f5f5;
    }

    &:not(:last-child) {
      border-bottom: 1px solid #f0f0f0;
    }

    &.selected {
      background: #e6f7ff;
      border-color: #91d5ff;

      .user-name {
        color: #1890ff;
        font-weight: 500;
      }
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .user-details {
        .user-name {
          font-size: 16px;
          color: #333333;
          margin-bottom: 2px;
        }

        .user-tel {
          font-size: 12px;
          color: #999999;
        }

        .user-dept {
          font-size: 11px;
          color: #cccccc;
          margin-top: 2px;
        }
      }
    }

    .check-icon {
      color: #1890ff;
      font-size: 16px;
    }
  }

  .empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #999999;

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

// 底部操作区
.modal-footer {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 24px;

  .selected-info {
    font-size: 12px;
    color: #666666;
    line-height: 1.4;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-btn {
    width: 100%;
    height: 40px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 500;

    &.ant-btn-primary {
      background: #1890ff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }

      &:disabled {
        background: #f5f5f5;
        border-color: #d9d9d9;
        color: #cccccc;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .invite-modal {
    width: 90% !important;
    max-width: 400px;

    .ant-modal-body {
      max-height: 500px;
    }
  }

  .content-area {
    max-height: 350px;
  }

  .department-item,
  .user-item {
    padding: 16px;

    .department-info,
    .user-info {
      gap: 16px;
    }
  }

  .modal-footer {
    padding: 16px;

    .selected-info {
      font-size: 11px;
      max-height: 32px;
    }

    .ant-btn {
      height: 44px;
      font-size: 16px;
    }
  }
}

// 暗色模式适配
@media (prefers-color-scheme: dark) {
  .invite-modal {
    .ant-modal-content {
      background: #1f1f1f;
      color: #ffffff;
    }

    .ant-modal-header {
      background: #1f1f1f;
      border-bottom-color: #333333;

      .ant-modal-title {
        color: #ffffff;
      }
    }
  }

  .search-section {
    border-bottom-color: #333333;

    .search-input .ant-input {
      background: #333333;
      color: #ffffff;

      &::placeholder {
        color: #999999;
      }

      &:focus {
        background: #404040;
        border-color: #1890ff;
      }
    }
  }

  .breadcrumb {
    background: #262626;
    border-bottom-color: #333333;
  }

  .department-list,
  .user-list {
    .section-title {
      background: #262626;
      color: #cccccc;
      border-bottom-color: #333333;
    }

    .department-item,
    .user-item {
      border-bottom-color: #333333;

      &:hover {
        background: #333333;
      }

      .department-name,
      .user-name {
        color: #ffffff;
      }

      &.selected {
        background: #1f3a8a;
        border-color: #3b82f6;
      }
    }
  }

  .modal-footer {
    background: #1f1f1f;
    border-top-color: #333333;

    .selected-info {
      color: #cccccc;
    }
  }
}
