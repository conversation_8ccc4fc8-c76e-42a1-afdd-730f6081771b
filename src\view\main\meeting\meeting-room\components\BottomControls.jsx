/**
 * @Create: 底部控制栏组件
 * @Date: 2025/01/28
 * @desc: 会议底部控制按钮
 */

import React from 'react';
import {
  AudioOutlined,
  AudioMutedOutlined,
  VideoCameraOutlined,
  EyeInvisibleOutlined,
  PhoneOutlined,
  TeamOutlined,
  MoreOutlined,
  SoundOutlined,
  SoundFilled
} from '@ant-design/icons';
import style from '../style/sass/bottom-controls.module.scss';

function BottomControls({
  isAudio,
  isVideo,
  isSpeaker,
  onToggleAudio,
  onToggleVideo,
  onToggleSpeaker,
  onShowParticipants,
  onLeaveMeeting
}) {

  // 控制按钮配置
  const controlButtons = [
    {
      key: 'audio',
      icon: isAudio ? <AudioOutlined /> : <AudioMutedOutlined />,
      label: isAudio ? '静音' : '取消静音',
      active: isAudio,
      onClick: onToggleAudio,
      className: !isAudio ? style.disabled : ''
    },
    {
      key: 'video',
      icon: isVideo ? <VideoCameraOutlined /> : <EyeInvisibleOutlined />,
      label: isVideo ? '关闭摄像头' : '开启摄像头',
      active: isVideo,
      onClick: onToggleVideo,
      className: !isVideo ? style.disabled : ''
    },
    {
      key: 'participants',
      icon: <TeamOutlined />,
      label: '参与者',
      active: true,
      onClick: onShowParticipants,
      className: ''
    },
  ];

  return (
    <div className={style['bottom-controls']}>
      {/* 主要控制按钮 */}
      <div className={style['control-buttons']}>
        {controlButtons.map((button) => (
          <button
            key={button.key}
            className={`${style['control-button']} ${button.className}`}
            onClick={button.onClick}
            aria-label={button.label}
          >
            <div className={style['button-icon']}>
              {button.icon}
            </div>
            <span className={style['button-label']}>
              {button.label}
            </span>
          </button>
        ))}
      </div>

      {/* 挂断按钮 */}
      <div className={style['hang-up-section']}>
        <button
          className={style['hang-up-button']}
          onClick={onLeaveMeeting}
          aria-label="离开会议"
        >
          <PhoneOutlined className={style['hang-up-icon']} />
        </button>
      </div>
    </div>
  );
}

export default BottomControls;
