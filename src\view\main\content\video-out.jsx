/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 视频呼出
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext, useEffect, useState} from "react";
import style from '../style/sass/video.module.scss';
import {MainContext} from "../context";
import {message} from "antd";

function VideoOut () {

	const { nonOwnerInformation } = useContext(MainContext);

	const [isMute, setIsMute] = useState(false);
	const [isSpeaker, setIsSpeaker] = useState(true);
	const [isCloseCamera, setIsCloseCamera] = useState(true);
	const [isFrontCamera, setIsFrontCamera] = useState(true);


	/**
	 * @desc 获取本地视频
	 */
	useEffect(() => {
		let shandle = window.shandleUtil.getSHandle();
		window.shandleUtil.updateVoiceSpeaker(true);
		shandle.setVideoContainer({
			localVideo: document.getElementById('my-video-out-content')
		})
	}, []);

	/**
	 * @desc 挂断电话
	 */
	function hangupCall() {
		window.shandleUtil.hungUp();
	}

	/**
	 * @desc 切换是否静音
	 */
	function setDeviceMute() {
		window.shandleUtil.audioClose(!isMute);
		setIsMute(!isMute);
	}

	/**
	 * @desc 切换扬声器
	 */
	function changeSpeaker() {
		// if (!window.shandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
		// 	message.error("当前设备不支持切换");
		// 	return
		// }
		// window.shandleUtil.updateVoiceSpeaker(!isSpeaker)
		if (!window.shandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
			message.error("当前设备不支持切换");
			return
		}
		window.shandleUtil.updateVoiceSpeaker(!isSpeaker);
		setIsSpeaker(!isSpeaker);
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice().then(() => {
		// 	setIsSpeaker(!isSpeaker);
		// }).catch(error => {
		// 	message.error(error);
		// });
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice();
		// setIsSpeaker(!isSpeaker);
	}

	/**
	 * @desc 关闭摄像头
	 */
	function closeCamera() {
		window.shandleUtil.enableCamera(!isCloseCamera);
		setIsCloseCamera(!isCloseCamera);
	}

	/**
	 * @desc 转换前后摄像头
	 */
	function turnFrontCamera() {
		let shandle = window.shandleUtil.getSHandle();
		// shandle.deviceControl.updateFrontCamera(!isFrontCamera);
		console.log(shandle);
		shandle.deviceControl.updateFrontCamera(!isFrontCamera);
		window.shandleUtil.audioClose(isMute);
		setIsFrontCamera(!isFrontCamera);
	}

	return(
		<div className={style.videoCall}>
			<div className={style.myVideoContent} id='my-video-out-content'/>
			<div className={style.videoHeader}>
				<span className={style.memName}>{nonOwnerInformation.memName}</span>
				<span className={style.defaultTip}>正在呼叫默认号码...</span>
			</div>
			<div className={style.actionsContent}>
				<span className={`${style.iconMute} ${isMute? style.isMute: ''}`} onClick={() => setDeviceMute()}></span>
				<span className={`${style.iconSpeaker} ${!isSpeaker? style.isSpeaker: ''}`} onClick={() => changeSpeaker()}></span>
				<span className={`${style.iconCloseCamera} ${!isCloseCamera? style.isCloseCamera: ''}`} onClick={() => closeCamera()}></span>
				<span className={`${style.iconTurnCamera} ${!isFrontCamera? style.isFrontCamera: ''}`} onClick={() => turnFrontCamera()}></span>
			</div>
			<div className={style.hungup}>
				<span className={style.iconHungUp} onClick={() => hangupCall()}></span>
			</div>
		</div>
	)
}

export default VideoOut;