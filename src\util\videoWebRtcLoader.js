/**
 * VideoWebRtc 加载器工具
 * 解决 "window.VideoWebRtc is not a constructor" 错误
 */

class VideoWebRtcLoader {
  constructor() {
    this.isLoaded = false;
    this.loadPromise = null;
    this.maxRetries = 20; // 最大重试次数
    this.retryInterval = 500; // 重试间隔 (ms)
  }

  /**
   * 检查 VideoWebRtc 是否已加载
   */
  isVideoWebRtcLoaded() {
    return typeof window.VideoWebRtc === 'function';
  }

  /**
   * 等待 VideoWebRtc 加载完成
   */
  waitForVideoWebRtc() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = new Promise((resolve, reject) => {
      let retryCount = 0;

      const checkLoaded = () => {
        console.log(`🔍 检查 VideoWebRtc 加载状态 (${retryCount + 1}/${this.maxRetries})`);

        if (this.isVideoWebRtcLoaded()) {
          console.log('✅ VideoWebRtc 已加载完成');
          this.isLoaded = true;
          resolve(window.VideoWebRtc);
          return;
        }

        retryCount++;
        if (retryCount >= this.maxRetries) {
          const error = new Error(`VideoWebRtc 加载超时，已重试 ${this.maxRetries} 次`);
          console.error('❌', error.message);
          reject(error);
          return;
        }

        console.log(`⏳ VideoWebRtc 尚未加载，${this.retryInterval}ms 后重试...`);
        setTimeout(checkLoaded, this.retryInterval);
      };

      // 立即检查一次
      checkLoaded();
    });

    return this.loadPromise;
  }

  /**
   * 安全地创建 VideoWebRtc 实例
   */
  async createVideoController(container, options) {
    try {
      console.log('🔧 开始创建 VideoController...');

      // 等待 VideoWebRtc 加载
      const VideoWebRtc = await this.waitForVideoWebRtc();

      // 检查容器元素
      if (!container) {
        throw new Error('容器元素不存在');
      }

      // 创建实例
      const controller = new VideoWebRtc(container, options);
      console.log('✅ VideoController 创建成功');

      return controller;
    } catch (error) {
      console.error('❌ VideoController 创建失败:', error);
      throw error;
    }
  }

  /**
   * 检查依赖脚本加载状态
   */
  checkDependencies() {
    const dependencies = {
      VideoWebRtc: typeof window.VideoWebRtc,
      adapter: typeof window.adapter,
      scooper: typeof window.scooper,
      require: typeof window.require
    };

    console.log('🔍 依赖脚本加载状态:', dependencies);
    return dependencies;
  }

  /**
   * 强制重新加载 VideoWebRtc 脚本
   */
  reloadVideoWebRtc() {
    return new Promise((resolve, reject) => {
      console.log('🔄 重新加载 VideoWebRtc 脚本...');

      // 移除现有脚本
      const existingScript = document.querySelector('script[src*="scooper.video"]');
      if (existingScript) {
        existingScript.remove();
      }

      // 创建新脚本
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.src = '/scooper-video-player/scooper.video.min.js';
      
      script.onload = () => {
        console.log('✅ VideoWebRtc 脚本重新加载成功');
        this.isLoaded = false;
        this.loadPromise = null;
        resolve();
      };

      script.onerror = (error) => {
        console.error('❌ VideoWebRtc 脚本重新加载失败:', error);
        reject(error);
      };

      document.head.appendChild(script);
    });
  }

  /**
   * 获取加载状态信息
   */
  getStatus() {
    return {
      isLoaded: this.isLoaded,
      isVideoWebRtcAvailable: this.isVideoWebRtcLoaded(),
      hasLoadPromise: !!this.loadPromise,
      dependencies: this.checkDependencies()
    };
  }

  /**
   * 重置加载器状态
   */
  reset() {
    console.log('🔄 重置 VideoWebRtc 加载器状态');
    this.isLoaded = false;
    this.loadPromise = null;
  }
}

// 创建单例实例
const videoWebRtcLoader = new VideoWebRtcLoader();

// 导出实例和便捷方法
export default videoWebRtcLoader;

export const waitForVideoWebRtc = () => videoWebRtcLoader.waitForVideoWebRtc();
export const createVideoController = (container, options) => 
  videoWebRtcLoader.createVideoController(container, options);
export const isVideoWebRtcLoaded = () => videoWebRtcLoader.isVideoWebRtcLoaded();
export const checkDependencies = () => videoWebRtcLoader.checkDependencies();
export const reloadVideoWebRtc = () => videoWebRtcLoader.reloadVideoWebRtc();
export const getVideoWebRtcStatus = () => videoWebRtcLoader.getStatus();
export const resetVideoWebRtcLoader = () => videoWebRtcLoader.reset();

/**
 * 使用示例：
 * 
 * import { createVideoController, waitForVideoWebRtc } from './videoWebRtcLoader';
 * 
 * // 方法1：直接创建（推荐）
 * const controller = await createVideoController(container, options);
 * 
 * // 方法2：先等待再创建
 * await waitForVideoWebRtc();
 * const controller = new window.VideoWebRtc(container, options);
 */
