# 全体禁言防抖功能实现指南

## 功能概述

为了防止用户多次快速点击全体禁言按钮导致重复请求，我们实现了基于防抖（debounce）和状态控制的双重保护机制。

## 实现原理

### 1. 防抖机制

使用 lodash 的 `debounce` 函数，配置如下：

```javascript
const debouncedHandleMuteAll = useMemo(
  () => debounce(handleMuteAll, 1000, {
    leading: true,  // 立即执行第一次
    trailing: false // 不执行最后一次
  }),
  [handleMuteAll]
);
```

**参数说明：**
- `delay: 1000ms` - 防抖延迟时间
- `leading: true` - 第一次点击立即执行
- `trailing: false` - 不执行最后一次点击

### 2. 状态控制

使用 `isMuteAllProcessing` 状态标志：

```javascript
const [isMuteAllProcessing, setIsMuteAllProcessing] = useState(false);

// 在执行函数中检查状态
if (isMuteAllProcessing) {
  console.log('全体禁言操作正在处理中，跳过重复请求');
  return;
}
```

### 3. 异步处理

使用 Promise.all 批量处理所有参与者：

```javascript
const promises = meetingState.participants.map(item =>
  dispatch_web_api.changeMemberLevel({
    id: meetingData.meetId,
    tel: item.tel,
    level: newMuteState ? 'audience' : 'speak'
  })
);

await Promise.all(promises);
```

## 核心代码

### 主要实现

```javascript
// 全体禁言/取消全体禁言
const handleMuteAll = useCallback(async () => {
  // 状态检查
  if (isMuteAllProcessing) {
    console.log('全体禁言操作正在处理中，跳过重复请求');
    return;
  }

  setIsMuteAllProcessing(true);
  const newMuteState = !meetingState.isMuteAll;

  try {
    // 批量处理
    const promises = meetingState.participants.map(item =>
      dispatch_web_api.changeMemberLevel({
        id: meetingData.meetId,
        tel: item.tel,
        level: newMuteState ? 'audience' : 'speak'
      })
    );

    await Promise.all(promises);

    // 更新状态
    setMeetingState(prev => ({
      ...prev,
      isMuteAll: newMuteState,
      participants: prev.participants.map(p => ({
        ...p,
        level: newMuteState ? 'audience' : 'speak'
      }))
    }));

    message.success(newMuteState ? '已开启全体禁言' : '已取消全体禁言');
  } catch (error) {
    console.error('全体禁言操作失败:', error);
    message.error('操作失败，请重试');
  } finally {
    setIsMuteAllProcessing(false);
  }
}, [isMuteAllProcessing, meetingState.isMuteAll, meetingState.participants, meetingData.meetId]);

// 防抖版本
const debouncedHandleMuteAll = useMemo(
  () => debounce(handleMuteAll, 1000, {
    leading: true,
    trailing: false
  }),
  [handleMuteAll]
);
```

### UI 状态显示

```jsx
<div
  className={`${style['action-button']} ${isMuteAll ? style['active'] : ''} ${isMuteAllProcessing ? style['processing'] : ''}`}
  onClick={isMuteAllProcessing ? undefined : onMuteAll}
  style={{ 
    opacity: isMuteAllProcessing ? 0.6 : 1,
    cursor: isMuteAllProcessing ? 'not-allowed' : 'pointer'
  }}
>
  {isMuteAllProcessing ? (
    <div className={style['loading-icon']}>⏳</div>
  ) : isMuteAll ? (
    <SoundFilled className={style['action-icon']} />
  ) : (
    <SoundOutlined className={style['action-icon']} />
  )}
  <span className={style['action-text']}>
    {isMuteAllProcessing 
      ? '处理中...' 
      : isMuteAll 
        ? '取消全体禁言' 
        : '全体禁言'
    }
  </span>
</div>
```

## 样式实现

### 处理中状态样式

```scss
.action-button {
  &.processing {
    background: #f0f0f0;
    border-color: #d9d9d9;
    color: #999999;
    cursor: not-allowed;
    opacity: 0.6;

    .action-icon {
      color: #999999;
    }

    .action-text {
      color: #999999;
    }

    &:hover {
      background: #f0f0f0;
      border-color: #d9d9d9;
      transform: none;
    }
  }

  .loading-icon {
    font-size: 20px;
    margin-bottom: 4px;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
```

## 防护机制

### 1. 时间防抖
- **延迟**: 1秒内多次点击只执行一次
- **立即执行**: 第一次点击立即响应
- **忽略尾随**: 不执行最后一次点击

### 2. 状态防护
- **处理标志**: `isMuteAllProcessing` 阻止重复执行
- **UI 禁用**: 处理中禁用按钮点击
- **视觉反馈**: 显示加载状态和处理文本

### 3. 错误处理
- **异常捕获**: try-catch 包装异步操作
- **状态恢复**: finally 块确保状态重置
- **用户提示**: 成功/失败消息提示

## 测试验证

### 测试场景

1. **单次点击**: 正常执行全体禁言操作
2. **快速连击**: 1秒内多次点击只执行一次
3. **处理中点击**: 操作进行中的点击被忽略
4. **网络异常**: 请求失败时状态正确恢复

### 测试工具

使用 `MuteAllDebounceTest.jsx` 组件进行测试：

```jsx
import MuteAllDebounceTest from './components/MuteAllDebounceTest';

// 在开发环境中使用
<MuteAllDebounceTest />
```

### 验证要点

- ✅ 防抖延迟生效
- ✅ 状态控制正确
- ✅ UI 反馈及时
- ✅ 错误处理完善
- ✅ 性能表现良好

## 性能优化

### 1. 批量处理
使用 `Promise.all` 并行处理所有参与者，而不是串行处理

### 2. 状态更新
一次性更新所有相关状态，减少重渲染

### 3. 内存管理
使用 `useCallback` 和 `useMemo` 优化函数和对象创建

### 4. 防抖配置
合理的延迟时间平衡用户体验和性能

## 最佳实践

1. **防抖参数**: 根据业务场景调整延迟时间
2. **状态管理**: 使用明确的状态标志控制操作
3. **错误处理**: 完善的异常处理和用户提示
4. **UI 反馈**: 及时的视觉反馈提升用户体验
5. **测试覆盖**: 全面的测试确保功能稳定

## 扩展应用

这种防抖模式可以应用于其他类似场景：

- 表单提交防重复
- 搜索输入防抖
- 按钮点击防抖
- API 请求防重复

通过合理配置防抖参数和状态控制，可以有效提升应用的稳定性和用户体验。
