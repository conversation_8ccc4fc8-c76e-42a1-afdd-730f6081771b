/*
 * @File: 定义公共方法 
 * @Author: liulian
 * @Date: 2020-11-05 10:36:43
 * @version: V0.0.0.1
 * @LastEditTime: 2025-02-12 11:34:48
 */

import { message } from "antd";
import moment from "moment";
import { curMainTel, curViceTel, meetLengthOptions } from "../config/constants";
import store from "../store";
import { apis } from "./api";
import CryptoJS from 'crypto-js';

window.CryptoJS = CryptoJS;

/**
 * 获取URL参数
 */
export function getUrlParmse() {
    const url = window.location.href;
    const params = {};
    
    // 处理hash参数
    if (url.includes('#')) {
        const hashPart = url.split('#')[1];
        if (hashPart && hashPart.includes('?')) {
            const queryString = hashPart.split('?')[1];
            const pairs = queryString.split('&');
            pairs.forEach(pair => {
                const [key, value] = pair.split('=');
                if (key && value) {
                    params[decodeURIComponent(key)] = decodeURIComponent(value);
                }
            });
        }
    }
    
    // 处理普通URL参数
    if (url.includes('?')) {
        const queryString = url.split('?')[1].split('#')[0];
        const pairs = queryString.split('&');
        pairs.forEach(pair => {
            const [key, value] = pair.split('=');
            if (key && value) {
                params[decodeURIComponent(key)] = decodeURIComponent(value);
            }
        });
    }
    
    return params;
}

/**
 * 获取我的音频号码
 */
export function getMeAudioTel() {
    const { curOpDetail } = store.getState().meetDetail;
    return curOpDetail?.mainTel || curMainTel();
}

/**
 * 获取用户媒体类型
 */
export function getUserMediaType(tel) {
    // 简化实现，实际项目中可能需要更复杂的逻辑
    return 'video';
}

/**
 * 加载我的会议列表
 */
export function loadMyMeetList() {
    // 简化实现
    return [];
}

/**
 * 填充我的会议列表
 */
export function fillMyMeetList(meetList) {
    // 简化实现
    console.log('填充会议列表:', meetList);
}

/**
 * 初始化我的会议列表记录
 */
export function initMyMeeListRecord() {
    // 简化实现
    console.log('初始化会议列表记录');
}

/**
 * DES解密
 */
export function decryptByDES(encryptedData, key) {
    try {
        const keyHex = CryptoJS.enc.Utf8.parse(key);
        const decrypted = CryptoJS.DES.decrypt(encryptedData, keyHex, {
            mode: CryptoJS.mode.ECB,
            padding: CryptoJS.pad.Pkcs7
        });
        return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
        console.error('DES解密失败:', error);
        return '';
    }
}

/**
 * 查找主框架
 */
export function findMainFrame() {
    try {
        let frame = window;
        while (frame.parent && frame.parent !== frame) {
            frame = frame.parent;
        }
        return frame;
    } catch (error) {
        console.error('查找主框架失败:', error);
        return window;
    }
}

/**
 * 获取电话呼叫类型
 */
export function getTelCallType(tel) {
    if (!tel) return 'audio';
    
    // 简化的判断逻辑，实际项目中可能需要更复杂的判断
    const videoTelPattern = /^(1[3-9]\d{9}|[2-9]\d{7,10})$/;
    return videoTelPattern.test(tel) ? 'video' : 'audio';
}

/**
 * 获取电话状态
 */
export function getTelStatus(tel) {
    const { telStatusList } = store.getState().loading;
    return telStatusList[tel] || 'offline';
}

/**
 * 数组去重（根据参数）
 */
export const uniqueByParam = (arr, param) => {
    const res = new Map();
    return arr?.filter((a) => !res.has(a[param]) && res.set(a[param], 1));
}

/**
 * 深度比较数组
 */
export function deepArrayEqual(arr1, arr2) {
    if (!arr1 || !arr2) return false;
    if (arr1.length !== arr2.length) return false;
    
    return arr1.every((item, index) => {
        if (typeof item === 'object' && typeof arr2[index] === 'object') {
            return JSON.stringify(item) === JSON.stringify(arr2[index]);
        }
        return item === arr2[index];
    });
}

/**
 * 判断是否播放视频
 */
export function judgePlayVideo(tel, status) {
    return status === 'meeting' || status === 'inMeet';
}

/**
 * 获取数字
 */
export function getNum(num) {
    const thresholds = [0, 1, 2, 3, 4, 6, 9];
    const windowNums = [1, 2, 3, 4, 6, 9, 16];
    
    for (let i = thresholds.length - 1; i >= 0; i--) {
        if (num > thresholds[i]) {
            return windowNums[i];
        }
    }
    return 1;
}

/**
 * 获取成员名称
 */
export function getMemName(tel) {
    const { memTelMapCache } = store.getState().loading;
    return memTelMapCache[tel]?.memName || tel;
}

/**
 * 更新屏幕列表
 */
export function updateScreenList(screenList) {
    // 简化实现
    console.log('更新屏幕列表:', screenList);
}

/**
 * 获取成员类型
 */
export function getMemType(tel) {
    const { intoMeetDetail, memTypeList } = store.getState().meetDetail;
    const { memTelMapCache } = store.getState().loading;

    // 首先尝试从memTypeList获取memType
    if (memTypeList[tel]) {
        return memTypeList[tel].memType;
    }
    if (memTelMapCache[tel] && memTelMapCache[tel].memType) {
        return memTelMapCache[tel].memType;
    } else {
        // 如果没有，在intoMeetDetail.meetMem中查找
        if (intoMeetDetail) {
            let mem = intoMeetDetail?.member?.find(item => item.tel === tel);
            if (mem) {
                return mem.memType;
            }
        }
    }
    // 如果两处都没有找到，返回默认值'0'
    return '0';
}

/**
 * 开启语音识别
 */
export const startOpAsrSwitch = (tels, status, isMeet) => {
    if (window.scooper && window.scooper.meetDispatchManager) {
        window.scooper.meetDispatchManager.opAsrSwitch(tels, status);
    }
}

/**
 * 播放视频信息
 */
export function playVideoInfo(tel, index, name, type, status) {
    if (window.scooper && window.scooper.meetvideoController) {
        window.scooper.meetvideoController.play(tel, index, name, type, status);
    }
}

/**
 * 格式化会议成员
 */
export function formatMeetMem(memberArr, memTelMapCache) {
    if (!memberArr || !Array.isArray(memberArr)) return [];
    
    return memberArr.map((realMem) => {
        if (realMem.memName === realMem.tel) {
            realMem.memName = null;
        }
        realMem.memTel = realMem.tel;
        realMem.memName = realMem.memName || 
                         (memTelMapCache[realMem.tel] && memTelMapCache[realMem.tel].memName) || 
                         realMem.tel;
        realMem.deptName = (memTelMapCache[realMem.tel] && memTelMapCache[realMem.tel].deptName) || '';
        realMem.deptNames = (memTelMapCache[realMem.tel] && memTelMapCache[realMem.tel].deptNames) || '';
        realMem.dutyName = (memTelMapCache[realMem.tel] && memTelMapCache[realMem.tel].dutyName) || '';
        return realMem;
    });
}

/**
 * 计算结束时间
 */
export function getEndTime(startTime, meetLength) {
    let nubHours = meetLength / 2;
    let endTime = moment(startTime).add({ hours: nubHours });
    return moment(endTime).format('YYYY-MM-DD HH:mm:ss');
}

/**
 * 处理音频字体
 */
export function handleAudioFont() {
    // 简化实现
    console.log('处理音频字体');
}

/**
 * 软手柄重新登录
 */
export const shadleReLogin = () => {
    if (window.newShandleUtil) {
        window.newShandleUtil.reRegister();
    }
}
