/**
 * @Create: 周颖仁
 * @Date: 2023/8/30
 * @desc: 系统操作按钮组
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/8/30
 */

import React, {useEffect, useRef} from "react";
import {Button, Dropdown, Tooltip, Modal} from "antd";
import {RollbackOutlined, MenuOutlined, LogoutOutlined} from "@ant-design/icons";
import {useNavigate} from "react-router";
import {useDrag} from "react-dnd";
import {useDispatch, useSelector} from "react-redux";
import {changeDragging,} from "../../redux/reducer/system-operation";
import style from './style/index.module.scss';
import {dispatch_web_api} from "../../util/api";

const { confirm } = Modal;

function SystemOperation() {

	const navigate = useNavigate();

	const { type, position } = useSelector(state => state.systemOperation);

	const dispatch = useDispatch();

	const tempDragging = useRef(false);


	// 拖拽配置
	const [, drag, preview] = useDrag(() => ({
		type: type,
		item: {left: position.left, top: position.top},
		end: () => {
			tempDragging.current = false;
			dispatch(changeDragging(false));
		},
		isDragging: () => {
			if(!tempDragging.current) {
				tempDragging.current = true;
				dispatch(changeDragging(true));
			}
		},
		collect: (monitor) => ({
			isDragging: !!monitor.isDragging()
		})
	}), [position])


	/**
	 * @desc 防止拖拽时携带底部背景
	 */
	useEffect(() => {
		// preview(getEmptyImage(), { captureDraggingState: true });
		const dragPreviewImage = new Image();
		dragPreviewImage.src = require('./style/image/icon-menu.png');
		preview(dragPreviewImage);
	}, [preview]);

	// 下拉选项
	const dropdownMenu = [
		{
			key: '1',
			label: <Tooltip title='返回导航页'> <Button size='large' type='primary' icon={<RollbackOutlined />}  onClick={() => navigate('/home-page')}/></Tooltip>
		},
		{
			key: '2',
			label: <Tooltip title='退出登录'><Button size='large' type='primary' icon={<LogoutOutlined />} onClick={() => exitSystem()} /></Tooltip>
		}
	]

	/**
	 * @desc 退出登录
	 */
	function exitSystem() {
		confirm({
			title: '确定要退出系统吗?',
			content: '',
			cancelText: '取消',
			okText: '确定',
			async onOk() {
				await dispatch_web_api.logout();
				navigate('/login');
				localStorage.clear();
				sessionStorage.clear();
			},
			onCancel() { },
		});
	}

	return(
		<>
			<div className={`${style.systemOperation} ${tempDragging.current? style.isDragging: ''}`} ref={drag} style={{ left: position.left, top: position.top }} >
				<Dropdown
					className={style.systemOperationDropdown}
					menu={{ items: dropdownMenu }}
					placement='bottom'
					getPopupContainer={triggerNode =>  triggerNode}
					overlayClassName={style.systemOperationOverlay}
					trigger='click'
				>
					<Button size='large' type='primary' icon={<MenuOutlined />}></Button>
				</Dropdown>
			</div>
		</>
	)
}

export default SystemOperation;

