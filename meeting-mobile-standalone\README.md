# Meeting Mobile Standalone

独立的移动端会议组件，可以在其他项目中直接使用。

## 功能特性

- 📱 移动端优化的会议界面
- 🎥 视频通话支持
- 🎤 音频控制（麦克风开关、扬声器切换）
- 👥 参会人员管理
- 📺 多屏显示支持
- 🔄 实时状态同步
- 🎨 响应式设计

## 安装

### 方式一：直接复制文件夹

将 `meeting-mobile-standalone` 文件夹复制到你的项目中。

### 方式二：作为npm包安装（如果发布到npm）

```bash
npm install meeting-mobile-standalone
```

## 依赖要求

确保你的项目已安装以下依赖：

```json
{
  "react": "^17.0.0",
  "react-dom": "^17.0.0",
  "react-redux": "^7.2.0",
  "redux": "^4.1.0",
  "antd": "^4.16.0",
  "react-swipeable-views": "^0.14.0",
  "moment": "^2.29.0",
  "pubsub-js": "^1.9.0",
  "qs": "^6.10.0",
  "copy-to-clipboard": "^3.3.0",
  "axios": "^0.24.0",
  "crypto-js": "^4.1.0"
}
```

## 使用方法

### 基础使用

```jsx
import React from 'react';
import MeetingMobile, { initMeetingMobile } from './meeting-mobile-standalone';

// 初始化配置
initMeetingMobile({
  apiBaseUrl: 'https://your-api-server.com',
  debug: true
});

function App() {
  const handleLeave = () => {
    console.log('用户离开会议');
    // 处理离开会议的逻辑
  };

  return (
    <div className="App">
      <MeetingMobile 
        onLeave={handleLeave}
        // 其他props...
      />
    </div>
  );
}

export default App;
```

### 高级配置

```jsx
import React from 'react';
import MeetingMobile, { initMeetingMobile } from './meeting-mobile-standalone';

// 高级配置
initMeetingMobile({
  apiBaseUrl: 'https://your-api-server.com',
  debug: true,
  globalObjects: {
    // 如果你有自定义的全局对象
    scooper: yourScooperObject,
    newShandleUtil: yourShandleUtilObject,
    newCometd: yourCometdObject
  }
});

function App() {
  const handleLeave = () => {
    // 离开会议处理
  };

  // 如果使用React Router
  const history = useHistory();

  return (
    <MeetingMobile 
      onLeave={handleLeave}
      history={history}
    />
  );
}
```

### 与Redux集成

如果你的项目已经使用了Redux，可以这样集成：

```jsx
import React from 'react';
import { Provider } from 'react-redux';
import { MeetingMobile } from './meeting-mobile-standalone';
import { store as meetingStore } from './meeting-mobile-standalone';
import yourStore from './your-store';

// 合并store或使用独立的store
function App() {
  return (
    <Provider store={yourStore}>
      <div className="app">
        {/* 你的其他组件 */}
        
        <Provider store={meetingStore}>
          <MeetingMobile />
        </Provider>
      </div>
    </Provider>
  );
}
```

## 配置选项

### initMeetingMobile(config)

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| apiBaseUrl | string | '' | API服务器基础URL |
| debug | boolean | false | 是否开启调试模式 |
| globalObjects | object | {} | 自定义全局对象 |

### MeetingMobile组件Props

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| onLeave | function | - | 离开会议回调函数 |
| history | object | - | React Router历史对象（可选） |
| config | object | {} | 组件级别配置 |

## 全局对象要求

组件依赖以下全局对象，如果你的环境中没有，组件会使用模拟版本：

### window.scooper
```javascript
window.scooper = {
  meetManager: {
    meetsObj: {
      // 会议操作方法
    }
  },
  meetDispatchManager: {
    accountDetail: {
      // 账户详情
    }
  },
  meetvideoController: {
    // 视频控制器
  },
  util: {
    isTopFrame: () => boolean
  }
};
```

### window.newShandleUtil
```javascript
window.newShandleUtil = {
  getSHandle: () => object,
  enableCamera: (enabled) => void,
  audioClose: (muted) => void,
  updateVoiceSpeaker: (useSpeaker) => void,
  deregister: () => void,
  reRegister: () => void
};
```

### window.newCometd
```javascript
window.newCometd = {
  subscribe: (channel, callback) => subscriptionId,
  unsubscribe: (subscriptionId) => void
};
```

## 样式定制

组件使用Less样式，你可以通过以下方式定制：

1. 直接修改样式文件
2. 通过CSS覆盖样式
3. 使用CSS变量（如果支持）

```css
/* 自定义样式示例 */
.meetingMobile {
  /* 你的自定义样式 */
}

.meetingMobile-top {
  background-color: your-color;
}
```

## API接口

组件需要以下API接口支持：

- `GET /api/meet/getMeetAdd` - 获取会议详情
- `GET /api/meet/getMeetScreenType` - 获取分屏类型
- `POST /api/meet/setMeetScreenType` - 设置分屏类型
- `POST /conf/kiv` - 获取配置密钥
- `POST /conf/data` - 获取配置数据
- 其他相关接口...

## 浏览器兼容性

- Chrome 70+
- Safari 12+
- Firefox 65+
- Edge 79+

## 注意事项

1. 确保API服务器支持CORS
2. 移动端需要HTTPS环境才能使用摄像头和麦克风
3. 某些功能需要用户授权（摄像头、麦克风权限）
4. 建议在真实设备上测试，模拟器可能无法完全模拟硬件功能

## 故障排除

### 常见问题

1. **组件无法加载**
   - 检查依赖是否正确安装
   - 确认API配置是否正确

2. **视频无法显示**
   - 检查摄像头权限
   - 确认HTTPS环境
   - 查看控制台错误信息

3. **音频问题**
   - 检查麦克风权限
   - 确认设备支持
   - 检查全局对象配置

### 调试模式

开启调试模式可以获得更多日志信息：

```javascript
initMeetingMobile({
  debug: true
});
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础会议功能
- 移动端优化

## 许可证

[MIT License](LICENSE)

## 支持

如有问题，请联系开发团队或提交Issue。
