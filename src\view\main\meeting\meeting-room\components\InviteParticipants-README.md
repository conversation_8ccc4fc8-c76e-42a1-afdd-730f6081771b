# 邀请参会人组件使用指南

## 组件概述

`InviteParticipants` 是一个用于邀请参会人员的弹窗组件，支持按部门浏览和搜索用户功能。

## 功能特性

- ✅ 真实部门树结构浏览
- ✅ 智能面包屑导航
- ✅ 根目录默认显示
- ✅ 层级跳转支持
- ✅ 用户搜索功能
- ✅ 多选用户邀请
- ✅ 实时显示已选择用户
- ✅ 响应式设计
- ✅ 暗色模式支持
- ✅ 集成 scooper_core_rest_api

## 组件结构

```
InviteParticipants/
├── InviteParticipants.jsx          # 主组件
├── invite-participants.module.scss  # 样式文件
├── InviteParticipantsExample.jsx   # 使用示例
└── InviteParticipants-README.md    # 说明文档
```

## 使用方法

### 基本用法

```jsx
import InviteParticipants from './components/InviteParticipants';

function MyComponent() {
  const [showInvite, setShowInvite] = useState(false);
  
  const meetingData = {
    meetId: 'meeting_123',
    meetAccess: 'access_token',
    meetingName: '项目会议'
  };

  const handleInviteSuccess = (invitedUsers) => {
    console.log('邀请成功:', invitedUsers);
    // 处理邀请成功逻辑
  };

  return (
    <>
      <button onClick={() => setShowInvite(true)}>
        邀请参会人
      </button>
      
      <InviteParticipants
        visible={showInvite}
        onClose={() => setShowInvite(false)}
        meetingData={meetingData}
        onInviteSuccess={handleInviteSuccess}
      />
    </>
  );
}
```

### 面包屑导航使用

组件支持智能面包屑导航，默认行为：

```javascript
// 初始状态：显示通讯录根目录
面包屑: "通讯录"
内容: 显示所有顶级部门

// 点击部门进入下级
面包屑: "通讯录 > 财务部"
内容: 显示财务部的子部门和成员

// 继续深入
面包屑: "通讯录 > 财务部 > 会计组"
内容: 显示会计组的成员

// 点击面包屑跳转
点击"财务部" -> 返回财务部层级
点击"通讯录" -> 返回根目录
```

### 与 ParticipantsList 组件集成

```jsx
import ParticipantsList from './components/ParticipantsList';

function MeetingRoom() {
  const [showParticipants, setShowParticipants] = useState(false);

  return (
    <ParticipantsList
      visible={showParticipants}
      participants={participants}
      onClose={() => setShowParticipants(false)}
      isHost={true}
      meetingData={meetingData}
      onInvite={(invitedUsers) => {
        // 处理邀请成功
        console.log('新邀请的用户:', invitedUsers);
      }}
    />
  );
}
```

## Props 参数

### InviteParticipants

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| visible | boolean | ✅ | false | 是否显示弹窗 |
| onClose | function | ✅ | - | 关闭弹窗回调 |
| meetingData | object | ✅ | {} | 会议数据 |
| onInviteSuccess | function | ❌ | - | 邀请成功回调 |

### meetingData 对象结构

```javascript
{
  meetId: string,        // 会议ID
  meetAccess: string,    // 会议访问令牌
  meetingName?: string,  // 会议名称（可选）
  startTime?: string     // 开始时间（可选）
}
```

### onInviteSuccess 回调参数

```javascript
// invitedUsers: 被邀请的用户数组
[
  {
    id: number,
    name: string,
    tel: string,
    department: string,
    avatar?: string
  }
]
```

## 数据接口

### 部门树接口

组件使用 `scooper_core_rest_api.listDeptByParent` 获取部门树结构：

```javascript
scooper_core_rest_api.listDeptByParent({
  id: number,           // 父部门ID，0表示根部门
  expandMember: boolean // 是否展开成员信息
})
```

**返回数据结构：**
```javascript
[
  {
    id: number,           // 部门/成员ID
    name: string,         // 部门/成员名称
    dataType: string,     // 'orgDept' | 'orgMember'
    memTel?: string,      // 成员电话（仅成员有）
    deptName?: string     // 所属部门（仅成员有）
  }
]
```

### 搜索用户接口

组件使用 `scooper_core_rest_api.listOrgMember` 搜索用户：

```javascript
scooper_core_rest_api.listOrgMember({
  keyword: string  // 搜索关键字
})
```

### 邀请用户 API

组件调用 `dispatch_web_api.inviteMeetingMember` 邀请用户：

```javascript
dispatch_web_api.inviteMeetingMember({
  meetId: string,     // 会议ID
  tel: string,        // 用户电话
  name: string,       // 用户姓名
  meetAccess: string  // 会议访问令牌
})
```

## 样式定制

### CSS 变量

```scss
// 主色调
--primary-color: #1890ff;
--success-color: #52c41a;
--warning-color: #fa8c16;

// 背景色
--bg-color: #ffffff;
--bg-secondary: #fafafa;
--bg-hover: #f5f5f5;

// 文字颜色
--text-primary: #333333;
--text-secondary: #666666;
--text-disabled: #999999;
```

### 响应式断点

```scss
// 移动端适配
@media (max-width: 768px) {
  // 移动端样式
}

// 暗色模式
@media (prefers-color-scheme: dark) {
  // 暗色模式样式
}
```

## 开发注意事项

1. **API 集成**: 需要实现真实的用户和部门数据接口
2. **权限控制**: 根据用户权限显示不同的部门和用户
3. **错误处理**: 添加网络错误和权限错误的处理
4. **性能优化**: 大量用户数据时考虑虚拟滚动
5. **国际化**: 支持多语言文本

## 更新日志

### v2.1.0 (2025-01-28)
- ✅ 优化面包屑导航逻辑
- ✅ 默认显示通讯录根目录
- ✅ 根目录只显示顶级部门
- ✅ 支持面包屑层级跳转
- ✅ 改进用户体验和交互

### v2.0.0 (2025-01-28)
- ✅ 集成真实的部门树 API
- ✅ 实现面包屑导航功能
- ✅ 支持部门层级浏览
- ✅ 真实用户搜索功能
- ✅ 优化数据结构和交互

### v1.0.0 (2025-01-28)
- ✅ 初始版本发布
- ✅ 基础部门浏览和用户搜索
- ✅ 多选邀请功能
- ✅ 响应式设计
- ✅ 与 ParticipantsList 组件集成

## 技术栈

- React 18+
- Ant Design 5.x
- Ant Design Mobile
- SCSS Modules
- ES6+

## 浏览器支持

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+
- 移动端浏览器
