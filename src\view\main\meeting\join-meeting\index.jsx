/**
 * @Create: 周颖仁
 * @Date: 2023/11/1
 * @desc: 加入会议
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/1
 */

import React, { useState, useContext } from "react";
import { useNavigate } from "react-router-dom";
import style from './style/sass/index.module.scss';
import { Input, Button, NavBar, Toast } from "antd-mobile";
import { dispatch_web_api } from "../../../../util/api";
import {
  AudioOutlined,
  AudioMutedOutlined,
  VideoCameraOutlined,
  EyeInvisibleOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { MainContext } from "../../context";

function JoinMeeting({ onBack }) {
  const navigate = useNavigate();
  const { setShowType } = useContext(MainContext);

  const [meetingId, setMeetingId] = useState('');
  const [meetingPassword, setMeetingPassword] = useState('');
  const [isMicOn, setIsMicOn] = useState(true);
  const [isCameraOn, setIsCameraOn] = useState(true);
  const [isJoining, setIsJoining] = useState(false);

  // 返回会议主页
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      setShowType('normal');
    }
  };

  // 切换麦克风状态
  const toggleMic = () => {
    setIsMicOn(!isMicOn);
  };

  // 切换摄像头状态
  const toggleCamera = () => {
    setIsCameraOn(!isCameraOn);
  };

  // 进入会议
  const handleJoinMeeting = async () => {
    if (!meetingId.trim()) {
      Toast.show('请输入会议号');
      return;
    }

    setIsJoining(true);

    try {
      console.log('加入会议:', {
        meetingId,
        password: meetingPassword,
        micOn: isMicOn,
        cameraOn: isCameraOn
      });

      // 获取用户信息
      const ownerInformation = JSON.parse(localStorage.getItem('scooperOwnerInformation') || '{}');
      const userName = ownerInformation.memName || '参会者';
      const tel = ownerInformation.mainTel || '';

      // 获取会议信息
      const meetingRes = await dispatch_web_api.getMeetByMeetAccess({meetAccess: meetingId});
      console.log('会议信息:', meetingRes);

      if (meetingRes.code === 0) {
        const meetingData = meetingRes.data;

        // 验证会议密码
        const isPasswordValid = validateMeetingPassword(meetingData, meetingPassword);

        if (!isPasswordValid) {
          Toast.show('会议密码错误，请重新输入');
          return;
        }

        // 密码验证通过，加入会议
        const joinRes = await dispatch_web_api.joinVideoMember({
          id: meetingData.meetId,
          tel,
          level: isMicOn ? 'speak' : 'audience',
          businessId: '',
          autoAnswer: '',
          meetAccess: meetingId
        });

        console.log('加入会议结果:', joinRes);

        if (joinRes.code === 0) {
          // 保存会议数据到 sessionStorage
          const meetingInfo = {
            ...meetingData,
            joinData: joinRes.data,
            userRole: isMicOn ? 'speaker' : 'audience',
            isHost: meetingData.ctrlTel === ownerInformation.mainTel
          };
          sessionStorage.setItem('curParam', JSON.stringify(meetingInfo));

          Toast.show('加入会议成功');
          // 跳转到会议室
          navigate('/main/meeting/room', {
            state: {
              meetingData: meetingInfo
            }
          });
        } else {
          Toast.show(joinRes.msg || '加入会议失败');
        }
      } else {
        Toast.show(meetingRes.msg || '会议不存在或已结束');
      }
    } catch (error) {
      console.error('加入会议失败:', error);
      Toast.show('网络错误，请重试');
    } finally {
      setIsJoining(false);
    }
  };

  // 验证会议密码
  const validateMeetingPassword = (meetingData, inputPassword) => {
    // 如果没有设置密码，直接通过
    if (!meetingData.passwdSpeaker && !meetingData.passwdAudience) {
      return true;
    }

    if(inputPassword === meetingData.passwdSpeaker || inputPassword === meetingData.passwdAudience) {
      return true;
    }else{
      return false;
    }
  };

  return (
    <div className={style['join-meeting']}>
      {/* 装饰元素 */}
      <div className={style['decoration-left']}></div>
      <div className={style['decoration-right']}></div>

      {/* 导航栏 */}
      <div className={style['nav-bar']}>
        <NavBar onBack={handleBack} back="返回" style={{color: '#fff'}}>
          加入会议
        </NavBar>
      </div>

      {/* 主要内容区域 */}
      <div className={style.content}>
        {/* 会议号输入区域 */}
        <div className={style['input-section']}>
          <div className={style['input-container']}>
            <Input
              placeholder="请输入会议号"
              value={meetingId}
              onChange={setMeetingId}
              className={style['meeting-input']}
              clearable
              maxLength={20}
              style={{
                '--color': '#ffffff',
                '--placeholder-color': 'rgba(255, 255, 255, 0.7)',
                color: '#ffffff'
              }}
            />
          </div>
          <div className={style['input-underline']}></div>
        </div>

        {/* 会议密码输入区域 */}
        <div className={style['input-section']}>
          <div className={style['input-container']}>
            <Input
              placeholder="请输入会议密码（可选）"
              value={meetingPassword}
              onChange={setMeetingPassword}
              className={style['meeting-input']}
              type="password"
              clearable
              maxLength={50}
              style={{
                '--color': '#ffffff',
                '--placeholder-color': 'rgba(255, 255, 255, 0.7)',
                color: '#ffffff'
              }}
            />
          </div>
          <div className={style['input-underline']}></div>
        </div>

        {/* 控制按钮区域 */}
        <div className={style['control-section']}>
          <div className={style['control-buttons']}>
            <div
              className={`${style['control-button']} ${!isMicOn ? style.disabled : ''}`}
              onClick={toggleMic}
            >
              {isMicOn ? <AudioOutlined style={{color: '#fff'}}/> : <AudioMutedOutlined style={{color: '#fff'}}/>}
            </div>

            <div
              className={`${style['control-button']} ${!isCameraOn ? style.disabled : ''}`}
              onClick={toggleCamera}
            >
              {isCameraOn ? <VideoCameraOutlined style={{color: '#fff'}}/> : <VideoCameraOutlined style={{color: '#fff'}}/>}
            </div>
          </div>
        </div>

        {/* 进入会议按钮 */}
        <div className={style['join-button-section']}>
          <Button
            block
            color="primary"
            size="large"
            className={style['join-button']}
            onClick={handleJoinMeeting}
            disabled={!meetingId.trim() || isJoining}
            loading={isJoining}
          >
            {isJoining ? '正在加入...' : '进入会议'}
          </Button>
        </div>
      </div>
    </div>
  );
}

export default JoinMeeting;
