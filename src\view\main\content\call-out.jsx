/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 电话呼出
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext, useEffect, useState} from "react";
import style from '../style/sass/call-out.module.scss';
import {MainContext} from "../context";
import {message} from "antd";

function CallOut() {

	const { nonOwnerInformation, allMemberInfo } = useContext(MainContext);

	const [isMute, setIsMute] = useState(false);
	const [isSpeaker, setIsSpeaker] = useState(true);

	useEffect(() => {
		window.shandleUtil.updateVoiceSpeaker(true);
	}, []);

	/**
	 * @desc 切换是否静音
	 */
	function setDeviceMute() {
		window.shandleUtil.audioClose(!isMute);
		setIsMute(!isMute);
	}

	/**
	 * @desc 挂断电话
	 */
	function hangupCall() {
		window.shandleUtil.hungUp();
	}

	/**
	 * @desc 切换扬声器
	 */
	function changeSpeaker() {
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice();
		if (!window.shandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
			message.error("当前设备不支持切换");
			return
		}
		window.shandleUtil.updateVoiceSpeaker(!isSpeaker);
		setIsSpeaker(!isSpeaker);
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice().then(() => {
		// 	setIsSpeaker(!isSpeaker);
		// }).catch(error => {
		// 	message.error(error);
		// });
	}

	return(
		<div className={style.callOut}>
			<div className={style.callOutHeader}>
				<span className={style.memName}>{nonOwnerInformation.memName || allMemberInfo.current[nonOwnerInformation.telNumber]?.memName || nonOwnerInformation.telNumber}</span>
				<span className={style.defaultTip}>正在呼叫默认号码...</span>
			</div>
			<div className={style.callOutActionContent}>
				<span className={`${style.iconMute} ${isMute? style.isMute: ''}`} onClick={() => setDeviceMute()}></span>
				<span className={style.iconHungUp} onClick={() => hangupCall()}></span>
				<span className={`${style.iconSpeaker} ${!isSpeaker? style.isSpeaker: ''}`} onClick={() => changeSpeaker()}></span>
			</div>
		</div>
	)
}

export default CallOut;