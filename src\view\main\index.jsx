/**
 * @Create: 周颖仁
 * @Date: 2023/11/1
 * @desc: 主页
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/1
 */

import React, {useMemo, useState, useEffect, useRef, Suspense} from "react";
import { Routes, Route } from "react-router-dom";
import style from './style/sass/index.module.scss';
import {TabBar} from "antd-mobile";
import { UserOutlined, PhoneOutlined, VideoCameraOutlined } from '@ant-design/icons';
import AddressBook from "./address-book";
import {message} from "antd";
import {MainContext} from "./context";
import MemberInfo from "./content/member-info";
import CallOut from "./content/call-out";
import CallIng from "./content/call-ing";
import CallIn from "./content/call-in";
import { useNavigate } from "react-router-dom";
import VideoOut from "./content/video-out";
import VideoIng from "./content/video-ing";
import Call from "./call";
import Meeting from "./meeting";
import {scooper_core_rest_api, dispatch_web_api} from "../../util/api";
import dayjs from "dayjs";
import PubSub from 'pubsub-js';

// 懒加载会议室组件
const MeetingRoom = React.lazy(() => import('./meeting/meeting-room'));


// 导航栏
const TAB_LIST = [
	{
		key: 'addressBook',
		title: '联系人',
		icon: <UserOutlined />,
	},
	{
		key: 'call',
		title: '拨号',
		icon:<PhoneOutlined />,
	},
	{
		key: 'meeting',
		title: '会议',
		icon: <VideoCameraOutlined />,
	}
]

function Main() {
	const navigate = useNavigate();
	const showTypeRef = useRef('normal');
	const videoCallRef = useRef(false);
	const allMemberInfo = useRef({});

	let ownerInformation = JSON.parse(localStorage.getItem('scooperOwnerInformation'));

	const [activeKey, setActiveKey] = useState('meeting');
	// 用于判断是否处于搜索功能
	const [showType, setShowType] = useState('normal');
	// 是否展示人员详情
	const [showMemInfo, setShowMemInfo] = useState(false);
	// 是否处于呼入呼出状态
	const [showOverContent, setShowOverContent] = useState(false);
	// 呼入呼出类型
	const [overType, setOverType] = useState('memInfo');
	// 当前选中的人员id
	const [selectedMemId, setSelectedMemId] = useState(undefined);
	// 非机主信息
	const [nonOwnerInformation, setNonOwnerInformation] = useState({});
	// 软手柄是否注册失败
	const [registerFailed, setRegisterFailed] = useState(true);
	// 刷新通话记录
	const [refreshList, setRefreshList] = useState(dayjs().valueOf());
	// 号码状态列表
	const [telStatusList, setTelStatusList] = useState({});

	
	/**
	 * @desc 初始化调度登录
	 */
	useEffect(() => {
		if (window.scooper && window.scooper.dispatch) {
			return
		}
		let scooperCoreToken = localStorage.getItem('scooperCoreToken');
		window.requirejs(window.home, ['scooper.dispatch'], dispatch => {
			dispatch.setRemoteBaseUrl(`/dispatch-web/`);
			dispatch.initialize('', { useShandle: true });
			dispatch.loginByToken(scooperCoreToken, async data => {
				const myDispatch = window.scooper.dispatch;
				if (data.code) {
					message.error('当前网络存在问题,请稍后再试');
					console.error('scooper.dispatch登录失败！');
					setRegisterFailed(true);
				} else {
					const ownerInfo = {
						mainTel: data.data.activeHandler,
						id: data.data.accountPo.id,
						name:data.data.accountPo.accShowname,
					}
					if (ownerInformation) {
						let info = Object.assign(ownerInformation, ownerInfo);
						localStorage.setItem('scooperOwnerInformation', JSON.stringify(info));
					}else{
						localStorage.setItem('scooperOwnerInformation', JSON.stringify(ownerInfo));
					}
					
					ownerInformation = JSON.parse(localStorage.getItem('scooperOwnerInformation'));

					myDispatch.listen(myDispatch.event_const.SHANDLE_REGISTER_NOTIFY,  data => {
						let msg = data.msg;
						// if(msg.tel !== ownerInformation.memTel) return;
						console.log("SHANDLE_REGISTER_NOTIFY=============", data.msg);
						if(msg.type === 'registered') {
							console.log('号码已注册,开始进行抢注册================');
							myDispatch.calls.shandlePreemptRegister();
						}else if(msg.type === 'register_succ') {
							setRegisterFailed(false);
							// 如果没有被邀请至会议中,把操作员员号码邀请进来
							if(sessionStorage.getItem('curParam')) {
								const curParam = JSON.parse(sessionStorage.getItem('curParam'));
								if(curParam.meetId&&window.location.hash.indexOf('main/meeting/room') > -1 ) {
									dispatch_web_api.joinVideoMember({id: curParam.meetId, tel: ownerInformation.mainTel, level: 'speak', businessId: '', autoAnswer: '', meetAccess: curParam.meetAccess}).then(res => {
										console.log(res)
									})
								}
							}
							// message.success('软手柄注册成功!');
						}else if(msg.type === 'register_fail') {
							setRegisterFailed(true);


						}
					})

					myDispatch.listen(myDispatch.event_const.SHANDLE_CALL_NOTIFY, data => {
						// 监听呼入
						let msg = data.msg;
						// if(msg.tel !== ownerInformation.memTel) return;
						videoCallRef.current = msg.video;
						let nonOwnerInformation = {
							audio: msg.audio,
							video: msg.video,
							telNumber: msg.telNumber,
						}
						setNonOwnerInformation(nonOwnerInformation);
						setOverType('callIn');
						console.log('SHANDLE_CALL_NOTIFY===========',data.msg);
					})

					// 监听号码变动
					myDispatch.listen(myDispatch.event_const.CALL_STS, data => {
						let msg = data.msg;
						
						if(msg.status === 'callst_meet'){
							// 更新号码状态
							if (window.location.hash.indexOf('main/meeting/room') > 0 && sessionStorage.getItem('curParam')){
								const curParam = JSON.parse(sessionStorage.getItem('curParam'));
								if(curParam.meetId === msg.meetId) {
									if (msg.meetLevel===6) {
										msg.level = '';
									}else{
										msg.level = msg.meetLevel === 0 ? 'audience' : 'speaker';
									}
									PubSub.publish('会场成员状态', msg)
								}

							}
						}
						// 当号码状态为会议中且号码状态之前为非会议时，则跳转至会议室
						if(msg.status === 'callst_meet'&&telStatusList[msg.tel]?.status != 'callst_meet') {
							if(msg.tel === ownerInformation.mainTel) {
								if (window.location.hash.indexOf('main/meeting/room') < 0 ) {
									navigate('/main/meeting/room', {
										state: {
										  meetingData: {
											  meetId: msg.meetId,
										  },
										}
									});
								}else{
									PubSub.publish('会场加入', msg)
								}
							}else{
								PubSub.publish('会场加入', msg)
							}
						}
						
						// 更新号码状态
						setTelStatusList(prev => {
							const updatedTelStatusList = { ...prev };
							updatedTelStatusList[msg.tel] = {
								...updatedTelStatusList[msg.tel],
								status: msg.status,
								onlineStatus: msg.onlineStatus,
								lastUpdateTime: new Date().toISOString()
							};
							return updatedTelStatusList;
						});
						if(msg.tel !== ownerInformation.mainTel) return;
						// 振铃或等待振铃
						if((msg.status === 'callst_ring' || msg.status === 'callst_waitring') && !sessionStorage.getItem('curParam')) {
							setShowOverContent(true);
						}else if(msg.status === 'callst_ring' || msg.status === 'callst_waitring' && sessionStorage.getItem('curParam')) {
							const curParam = JSON.parse(sessionStorage.getItem('curParam'));
							if(curParam.meetId === msg.meetId) {
								// 自应答
							}else{
								setShowOverContent(true);
							}
						}
						// 双方通话
						if(msg.status === 'callst_doubletalk') {
							setShowOverContent(true);
							if(videoCallRef.current) {
								setOverType('videoing')
							}else {
								setOverType('calling')
							}
						}

						// 恢复空闲状态
						if(msg.status === 'callst_idle') {
							setShowOverContent(false);
							setOverType('callIn');
							setRefreshList(dayjs().valueOf());
						}

						// 离线
						if(msg.status === 'callst_offline') {
							console.log('当前账号被其他设备登录,请退出重新登录');
						}
						

						

						console.log('CALL_STS===========', data.msg);
					})

					// 监听视频播放
					myDispatch.listen(myDispatch.event_const.CHANGE_CFG, (evt) => {
						console.log("CHANGE_CFG", evt.msg);
						let msg = evt.msg;
						if (!msg || !msg.type) {
							return false;
						}
						// 软手柄入会视频通知?
						if (msg.type == 'decoder_info') {
							// this.handleDecodeInfo(msg);
							// "decoder_type=activeX;type=CHN;PT=98;Format=H264;addr=192.168.108.44:30306;a=sendrecv;call_number=142001;local_port=31142;h_n=142001" 以；隔开后在以=切成对象
							
							// const jsonMsg = msg.value.split(';').map(item => {
							// 	return {
							// 		[item.split('=')[0]]: item.split('=')[1]
							// 	}
							// });
							// if(msg.tel === ownerInformation.mainTel&&window.location.hash.indexOf('main/meeting/room') < 0 ) {
							// 	navigate('/main/meeting/room', {
							// 		state: {
							// 			meetingData: {
							// 				meetId: msg.meetId,
							// 			},
							// 		}
							// 	});
							
							// }else{
							// 	if(jsonMsg.meetId === JSON.parse(sessionStorage.getItem('curParam')).meetId) {
									
							// 		PubSub.publish('视频打开', msg)
							// 	}
							// }
						} else if (msg.type == 'stop_decoder') {
							// 离开会议
							PubSub.publish('视频关闭', msg)
						}
					})

					myDispatch.listen(myDispatch.event_const.MEET_LST, data => {
						let msg = data.msg;
						if(msg.type === 'add') {
							console.log('收到会议列表变化通知：', data);
							// 会议创建成功
							PubSub.publish('MEET_LST', data.msg);
						}
					})

					myDispatch.listen(myDispatch.event_const.MEET_MEM, (evt) => {
						console.log('收到会议成员变化通知：', evt.msg);
						PubSub.publish('MEET_MEM', evt.msg);

					})

					// myDispatch.listen(myDispatch.event_const.MEET_STS, (evt) => {
					// 	console.log('收到会议状态变化通知：', evt.msg);
					// })

					// myDispatch.listen(myDispatch.event_const.IN_CALL_ANSWER, data => {
					// 	let msg = data.msg;
					// 	console.log('IN_CALL_ANSWER===========', data.msg);
					// })
					initTelStatus()
				}
			})
		})
	}, [window.scooper]);

	/**
	 * @desc 查询
	 */
	useEffect(() => {
		let params = {
			currentPage: 1,
			pageSize: 100000,
			useSort: false,
		}
		scooper_core_rest_api.queryOrgMember(params).then(data => {
			let memberInfo = {};
			(data.list || []).forEach(item => {
				memberInfo[item.memTel] = {
					memName: item.memName,
					accId: item.accId,
				};
			})
			console.log(memberInfo);
			allMemberInfo.current = memberInfo;
		}).catch(error => {
			console.log(`queryOrgMember: ${error}`);
		})
	}, []);

	/**
	 * @desc 初始化号码状态
	 */
	const initTelStatus = () => {
		console.log('🔄 [号码状态] 开始初始化号码状态');

		// 164B1修改 - 获取初始号码状态
		dispatch_web_api.listCallStatus().then(data => {
			const telStatusMap = {};

			(data || []).forEach(item => {
				if (item.tel) {
					telStatusMap[item.tel] = {
						...item,
						// 如果在线状态为离线，则设置状态为离线
						status: item.onlineStatus === 'callst_offline' ? 'callst_offline' : item.status,
						lastUpdateTime: new Date().toISOString()
					};
				}
			});

			console.log('✅ [号码状态] 初始化完成，共', Object.keys(telStatusMap).length, '个号码');
			setTelStatusList(telStatusMap);

			// 启动定时更新
			updateTelStatus();
		}).catch(error => {
			console.error('❌ [号码状态] 初始化失败:', error);
		});
	};

	/**
	 * @desc 定时更新号码状态
	 */
	const updateTelStatus = () => {
		console.log('🔄 [号码状态] 启动定时更新，间隔60秒');

		// 清除先前的定时器
		if (window.updateTelStatusInterval) {
			clearInterval(window.updateTelStatusInterval);
		}

		window.updateTelStatusInterval = setInterval(() => {
			console.log('🔄 [号码状态] 执行定时更新');

			const params = {
				timeInterval: 240, // 4分钟内的状态变化
			};

			dispatch_web_api.listCallStatus(params).then(res => {
				setTelStatusList(prevTelStatusList => {
					const updatedTelStatusList = { ...prevTelStatusList };
					let updateCount = 0;

					(res || []).forEach((item) => {
						if (item.tel) {
							const tel = item.tel;
							const currentTime = new Date().toISOString();

							if (updatedTelStatusList[tel]) {
								// 更新现有号码状态
								const hasChanged =
									updatedTelStatusList[tel].status !== item.status ||
									updatedTelStatusList[tel].onlineStatus !== item.onlineStatus;

								if (hasChanged) {
									updatedTelStatusList[tel] = {
										...updatedTelStatusList[tel],
										status: item.status,
										onlineStatus: item.onlineStatus,
										timestamp: item.timestamp,
										lastUpdateTime: currentTime
									};
									updateCount++;
									console.log(`📱 [号码状态] ${tel} 状态更新:`, item.status);
								}
							} else {
								// 添加新号码
								updatedTelStatusList[tel] = {
									...item,
									lastUpdateTime: currentTime
								};
								updateCount++;
								console.log(`📱 [号码状态] 新增号码:`, tel, item.status);
							}
						}
					});

					if (updateCount > 0) {
						console.log(`✅ [号码状态] 更新完成，共更新 ${updateCount} 个号码`);
					}

					return updatedTelStatusList;
				});
			}).catch(error => {
				console.error('❌ [号码状态] 定时更新失败:', error);
			});
		}, 60000); // 60秒更新一次
	};

	/**
	 * @desc 初始化号码状态
	 */
	useEffect(() => {
		// 在组件挂载后初始化号码状态
		initTelStatus();

		// 组件卸载时清理定时器
		return () => {
			if (window.updateTelStatusInterval) {
				clearInterval(window.updateTelStatusInterval);
				console.log('🧹 [号码状态] 清理定时器');
			}
		};
	}, []);

	/**
	 * @dec 渲染页面
	 */
	const renderPageContent = useMemo(() => {
		switch (activeKey) {
			case 'addressBook': return <AddressBook />;
			case 'call': return <Call />;
			case 'meeting': return <Meeting />;
			default: return null;
		}
	}, [activeKey])

	/**
	 * @desc 渲染覆盖页内容
	 */
	const renderOverContent = useMemo(() => {
		switch (overType) {
			case 'callOut': return <CallOut />
			case 'calling': return <CallIng />
			case 'callIn': return <CallIn />
			case 'videoOut': return <VideoOut />
			case 'videoing': return <VideoIng />
			default: return null;
		}
	}, [overType])

	return(
		<MainContext.Provider value={{
			showType,
			setShowType,
			showTypeRef,
			showOverContent,
			setShowOverContent,
			overType,
			setOverType,
			selectedMemId,
			setSelectedMemId,
			setShowMemInfo,
			nonOwnerInformation,
			setNonOwnerInformation,
			videoCallRef,
			registerFailed,
			allMemberInfo,
			ownerInformation,
			refreshList,
			telStatusList,
			setTelStatusList,
			initTelStatus
		}}>
			{/* 处理子路由 */}
			<Routes>
				{/* 会议室页面 - 子路由 */}
				<Route path="/meeting/room" element={
					<Suspense fallback={<div>会议室加载中...</div>}>
						<MeetingRoom />
					</Suspense>
				} />

				{/* 主应用页面 - 默认路由 */}
				<Route path="/*" element={
					<div className={style.main}>
						<div className={`${style.pageContent} ${showType === 'normal'? '': style.full}`}>
							{ renderPageContent }
						</div>
						{
							showType === 'normal' &&
							<div className={style.navBar}>
								<TabBar activeKey={activeKey} onChange={value => setActiveKey(value)}>
									{
										TAB_LIST.map(item => {
											return <TabBar.Item key={item.key} title={item.title} icon={item.icon} />
										})
									}
								</TabBar>
							</div>
						}
						{
							showMemInfo &&
							<div className={style.memInfoContent}>
								<MemberInfo />
							</div>
						}
						{
							showOverContent &&
							<div className={style.overContent}>
								{ renderOverContent }
							</div>
						}
					</div>
				} />
			</Routes>
		</MainContext.Provider>
	)
}

export default Main;