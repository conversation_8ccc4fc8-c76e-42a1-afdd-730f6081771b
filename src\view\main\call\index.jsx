/**
 * @Create: 周颖仁
 * @Date: 2023/11/8
 * @desc: 拨号主入口
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/8
 */

import React, {useContext, useState} from "react";
import style from './style/sass/index.module.scss';
import {message, Radio} from "antd";
import {MainContext} from "../context";
import {InfiniteScroll, List, NoticeBar} from "antd-mobile";
import {PhoneOutlined, VideoCameraOutlined} from "@ant-design/icons";
import { TextDeletionOutline } from 'antd-mobile-icons'
import { LoginOutlined, LogoutOutlined } from '@ant-design/icons';
import {cloneDeep} from "lodash";
import {dispatch_web_api, ecis_access_api} from "../../../util/api";
import {useUpdateEffect} from "ahooks";

const KEY_NUMBER_LIST = [1, 2, 3, 4, 5, 6, 7, 8, 9, '*', 0, '#'];

function Call() {

	const { registerFailed, setOverType, setNonOwnerInformation, videoCallRef, allMemberInfo, ownerInformation, refreshList } = useContext(MainContext);

	// 是否处于拨号
	const [isDial, setIsDial] = useState(false);
	// 拨号内容
	const [dialText, setDialText] = useState([]);
	// 未接还是全部
	const [callType, setCallType] = useState('all');
	const [pageNum, setPageNum] = useState(0);
	const [callRecordList, setCallRecordList] = useState([]);
	const [hasMore, setHasMore] = useState(true);

	const pageSize = 10;

	/**
	 * @desc 根据更新刷新列表
	 */
	useUpdateEffect(() => {
		setCallRecordList([]);
		loadMore(1);
	}, [refreshList]);

	async function loadMore(page) {
		let newPage = page? page: pageNum + 1;
		setPageNum(newPage)
		let params = {
			pageNum: newPage,
			pageSize
		}
		let appendData = {};
		if(callType === 'miss') {
			appendData = await dispatch_web_api.queryCallInMiss(params);
		}else {
			params.permType = 1;
			appendData = await dispatch_web_api.queryCallRecord(params);
		}
		setCallRecordList(list => [...list, ...(appendData.list || [])]);
		setHasMore(appendData.hasNextPage);
	}


	/**
	 * @desc 数字点击
	 * @param value
	 */
	function numberClick(value) {
		console.log(value);
		if(!isDial) setIsDial(true);
		let newDialText = cloneDeep(dialText);
		newDialText.push(value);
		setDialText(newDialText);
	}

	/**
	 * @desc 删除输入的数字
	 */
	function delNumber() {
		let newDialText = cloneDeep(dialText);
		if(dialText.length > 1) {
			newDialText.pop();
		}else {
			newDialText = [];
			setIsDial(false);
		}
		setDialText(newDialText);
	}

	/**
	 * @desc 呼叫
	 */
	function callNumber() {
		if(!dialText.length) return;
		setNonOwnerInformation({ telNumber: dialText.join('') });
		if(ownerInformation.mainTel === dialText.join('')) return message.error('不能呼叫自己!');
		let params = {
			tels: dialText.join('')
		}
		dispatch_web_api.telsStatus(params).then(data => {
			let telInfo = data?.[0] || {};
			if(telInfo.status === 'callst_offline' || !telInfo.status) {
				let ownerInfo = JSON.parse(localStorage.getItem('scooperOwnerInformation'));
				let params = {
					phone: dialText.join(''),
					pushAccId: ownerInfo.id,
					type: 1
				}
				message.success('当前成员不在线,已发送邀请!');
				// ecis_access_api.publishMsg(params).then(() => {
				// 	message.success('当前成员不在线,已发送邀请!');
				// }).catch(error => {
				// 	console.log(`publishMsg: ${error}`);
				// })
			}else if(telInfo.status === 'callst_idle') {
				setOverType('callOut');
				window.scooper.dispatch.calls.makeAudioCall(dialText.join(''));
				videoCallRef.current = false;
				setDialText([]);
				setIsDial(false);
			}else {
				message.warning('号码正在通话中请稍后再拨...');
			}
		}).catch(error => {
			console.log(`telsStatus: ${error}`);
		})
	}

	/**
	 * @desc 视频呼叫
	 */
	function videoNumber() {
		if(!dialText.length) return;
		setNonOwnerInformation({ telNumber: dialText.join('') });
		if(ownerInformation.mainTel === dialText.join('')) return message.error('不能呼叫自己!');
		let params = {
			tels: dialText.join('')
		}
		dispatch_web_api.telsStatus(params).then(data => {
			let telInfo = data?.[0] || {};
			if(telInfo.status === 'callst_offline' || !telInfo.status) {
				let ownerInfo = JSON.parse(localStorage.getItem('scooperOwnerInformation'));
				let params = {
					phone: dialText.join(''),
					pushAccId: ownerInfo.id,
					type: 1
				}
				message.success('当前成员不在线,已发送邀请!');
				// ecis_access_api.publishMsg(params).then(() => {
				// 	message.success('当前成员不在线,已发送邀请!');
				// }).catch(error => {
				// 	console.log(`publishMsg: ${error}`);
				// })
			}else if(telInfo.status === 'callst_idle') {
				setOverType('videoOut');
				window.scooper.dispatch.calls.makeVideoCall(dialText.join(''));
				videoCallRef.current = true;
				setDialText([]);
				setIsDial(false);
			}else {
				message.warning('号码正在通话中请稍后再拨...');
			}
		}).catch(error => {
			console.log(`telsStatus: ${error}`);
		})
	}

	/**
	 * @desc 类型切换
	 * @param value
	 */
	function callTypeChange(value) {
		setCallType(value);
		setPageNum(0);
		setCallRecordList([]);
		setHasMore(true);
	}

	/**
	 * @desc 计算时长
	 * @param length
	 */
	function calculateLength(length) {
		let hours = Math.floor(length / 3600);
		let minutes = Math.floor( (length - (hours * 3600)) / 60);
		let seconds = length % 60;
		return `${hours ? `${hours}时`: ''}${minutes ? `${minutes}分`: ''}${seconds ? `${seconds}秒`: ''}`
	}

	/**
	 * @desc 拨打
	 * @param info 记录详情
	 */
	function recordClick(info) {
		setIsDial(true);
		let dialText;
		if(ownerInformation.mainTel === info.caller) {
			dialText = info.called.split('');
		}else {
			dialText = info.caller.split('');
		}
		setDialText(dialText);
	}

	return(
		<div className={style.call}>
			<div className={style.callTypeContent}>
				<Radio.Group value={callType} buttonStyle="solid" size='large' className={style.radioButton} onChange={e => callTypeChange(e.target.value)}>
					<Radio.Button value='miss'>未接来电</Radio.Button>
					<Radio.Button value='all'>所有通话</Radio.Button>
				</Radio.Group>
			</div>
			{
				registerFailed && <NoticeBar content='当前网络存在问题，请稍后再试' color='error' />
			}
			<div className={`${style.telListContent} ${!registerFailed? style.full: ''}`}>
				<List>
					{
						callRecordList.map(item => {
							return(
								<List.Item
									key={item.recId}
									extra={item.tmCallT}
									description={
										<div className={style.callInfo}>
											{
												ownerInformation.mainTel === item.called? <LoginOutlined className={style.iconCall} />: <LogoutOutlined className={style.iconCall}/>
											}
											{
												calculateLength(item.callLength)
											}
										</div>
									}
									arrow={false}
									onClick={() => recordClick(item)}
								>
									<span className={`${item.callLength > 0? '': `${style.noAnswer}`}`}>{ownerInformation.mainTel === item.called? (allMemberInfo.current[item.caller]?.memName || item.caller):  (allMemberInfo.current[item.called]?.memName || item.called)}</span>
								</List.Item>
							)
						})
					}
				</List>
				<InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
			</div>
			{
				isDial &&
				<div className={style.dialContent}>
					<div className={style.dialTextContent}>
						<span>{dialText}</span>
					</div>
				</div>
			}
			<div className={style.numberKeyboardContent}>
				<div className={style.numberContent}>
					{
						KEY_NUMBER_LIST.map(item => {
							return <span key={item} className={style.unitKey} onClick={() => numberClick(item)}>{item}</span>
						})
					}
				</div>
				<div className={style.actionContent}>
					<span className={style.unitKey} onClick={() => delNumber()}><TextDeletionOutline color='#1677ff' /></span>
					<span className={style.unitActionKey} onClick={() => callNumber()}><PhoneOutlined /></span>
					<span className={style.unitActionKey} onClick={() => videoNumber()}><VideoCameraOutlined /></span>
				</div>
			</div>
		</div>
	)
}

export default Call;