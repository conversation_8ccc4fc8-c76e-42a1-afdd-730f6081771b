.address-book {
	width: 100%;
	height: 100%;
	.search-content {
		width: 100%;
		padding: 12px;
		display: flex;
		align-items: center;
	}
	.person-list-content {
		width: 100%;
		height: calc(100% - 64px);
		padding: 0 12px 12px 12px;
		.breadcrumb-content {
			width: 100%;
			height: 32px;
			overflow-x: auto;
			overflow-y: hidden;
			padding-bottom: 10px;
			border-bottom: 1px solid #F0F0F0;
			display: flex;
			.my-breadcrumb {
				white-space: nowrap;
				& ol {
					flex-wrap: nowrap;
				}
				.unit-breadcrumb-item {
					font-size: 16px;
					.current-breadcrumb {
						color: #1677ff;
					}
				}
			}
		}
		.person-list {
			width: 100%;
			height: calc(100% - 32px);
			overflow-y: auto;
			.unit-dept-info {
				padding: 12px;
				font-size: 16px;
				:global {
					.ant-list-item-meta {
						.ant-list-item-meta-avatar {
							font-size: 18px;
						}
						.ant-list-item-meta-title {
							font-size: 16px;
						}
					}
				}
				&:active {
					background: #eee;
				}
			}
		}
		.search-person-list {
			width: 100%;
			height: 100%;
			.unit-person-info {
				padding: 12px;
				font-size: 16px;
				:global {
					.ant-list-item-meta {
						.ant-list-item-meta-avatar {
							font-size: 18px;
						}
						.ant-list-item-meta-title {
							font-size: 16px;
						}
					}
				}
				&:active {
					background: #eee;
				}
			}
		}
	}
	.mem-info {
		width: 100%;
		height: 100%;
	}
}

:global {
	.keyboard-call-action-content {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		height: 100%;
		width: 100%;
		.icon-call {
			height: 50%;
			font-size: 24px;
		}
		.icon-video {
			height: 50%;
			font-size: 24px;
		}
	}
}