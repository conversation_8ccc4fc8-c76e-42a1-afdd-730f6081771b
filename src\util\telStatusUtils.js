/**
 * @Create: 号码状态工具函数
 * @Date: 2025/01/28
 * @desc: 提供号码状态相关的工具函数
 */

/**
 * 号码状态对应中文
 */
export const TEL_STATUS_TEXT = {
    "callst_offline": '离线',
    "callst_idle": '空闲',
    "callst_hold": '保持',
    "callst_waitring": '等待振铃',
    "callst_ring": '振铃',
    "callst_answer": '应答',
    "callst_doubletalk": '通话中',
    "callst_transfer": '转接成功',
    "callst_transfering": '转接中',
    "callst_turning": '轮询中',
    "callst_meet": '会议中',
    "callst_breakin": '强插通话',
    "callst_monitor": '监听通话',
    "callst_callinwaitanswer": '等待应答',
    "callst_monitorring": '监控振铃',
    "callst_monitoranswer": '监控通话',
    "callst_monitoroffhook": '监听摘机'
};

/**
 * 号码状态对应颜色
 */
export const TEL_STATUS_COLOR = {
    "callst_offline": '#999999',     // 灰色 - 离线
    "callst_idle": '#52c41a',        // 绿色 - 空闲
    "callst_hold": '#faad14',        // 黄色 - 保持
    "callst_waitring": '#1890ff',    // 蓝色 - 等待振铃
    "callst_ring": '#1890ff',        // 蓝色 - 振铃
    "callst_answer": '#52c41a',      // 绿色 - 应答
    "callst_doubletalk": '#ff4d4f',  // 红色 - 通话中
    "callst_transfer": '#722ed1',    // 紫色 - 转接成功
    "callst_transfering": '#722ed1', // 紫色 - 转接中
    "callst_turning": '#fa8c16',     // 橙色 - 轮询中
    "callst_meet": '#13c2c2',        // 青色 - 会议中
    "callst_breakin": '#ff4d4f',     // 红色 - 强插通话
    "callst_monitor": '#fa8c16',     // 橙色 - 监听通话
    "callst_callinwaitanswer": '#1890ff', // 蓝色 - 等待应答
    "callst_monitorring": '#fa8c16', // 橙色 - 监控振铃
    "callst_monitoranswer": '#fa8c16', // 橙色 - 监控通话
    "callst_monitoroffhook": '#fa8c16' // 橙色 - 监听摘机
};

/**
 * 获取号码状态信息
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {Object} 号码状态信息
 */
export const getTelStatus = (tel, telStatusList = {}) => {
    if (!tel || !telStatusList) {
        return {
            status: 'callst_offline',
            onlineStatus: 'callst_offline',
            text: TEL_STATUS_TEXT['callst_offline'],
            color: TEL_STATUS_COLOR['callst_offline'],
            isOnline: false,
            canCall: false
        };
    }

    const telInfo = telStatusList[tel];
    if (!telInfo) {
        return {
            status: 'callst_offline',
            onlineStatus: 'callst_offline',
            text: TEL_STATUS_TEXT['callst_offline'],
            color: TEL_STATUS_COLOR['callst_offline'],
            isOnline: false,
            canCall: false
        };
    }

    const status = telInfo.status || 'callst_offline';
    const onlineStatus = telInfo.onlineStatus || 'callst_offline';
    const isOnline = onlineStatus !== 'callst_offline';
    const canCall = isOnline && (status === 'callst_idle' || status === 'callst_answer');

    return {
        status,
        onlineStatus,
        text: TEL_STATUS_TEXT[status] || '未知',
        color: TEL_STATUS_COLOR[status] || '#999999',
        isOnline,
        canCall,
        timestamp: telInfo.timestamp,
        lastUpdateTime: telInfo.lastUpdateTime,
        raw: telInfo
    };
};

/**
 * 获取号码状态文本
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {string} 状态文本
 */
export const getTelStatusText = (tel, telStatusList = {}) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    return statusInfo.text;
};

/**
 * 获取号码状态颜色
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {string} 状态颜色
 */
export const getTelStatusColor = (tel, telStatusList = {}) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    return statusInfo.color;
};

/**
 * 检查号码是否在线
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {boolean} 是否在线
 */
export const isTelOnline = (tel, telStatusList = {}) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    return statusInfo.isOnline;
};

/**
 * 检查号码是否可以拨打
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {boolean} 是否可以拨打
 */
export const canCallTel = (tel, telStatusList = {}) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    return statusInfo.canCall;
};

/**
 * 检查号码是否正在通话中
 * @param {string} tel - 号码
 * @param {Object} telStatusList - 号码状态列表
 * @returns {boolean} 是否正在通话中
 */
export const isTelInCall = (tel, telStatusList = {}) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    const busyStatuses = [
        'callst_doubletalk',
        'callst_meet',
        'callst_breakin',
        'callst_monitor',
        'callst_monitoranswer'
    ];
    return busyStatuses.includes(statusInfo.status);
};

/**
 * 获取在线号码列表
 * @param {Object} telStatusList - 号码状态列表
 * @returns {Array} 在线号码列表
 */
export const getOnlineTels = (telStatusList = {}) => {
    return Object.keys(telStatusList).filter(tel => 
        isTelOnline(tel, telStatusList)
    );
};

/**
 * 获取可拨打号码列表
 * @param {Object} telStatusList - 号码状态列表
 * @returns {Array} 可拨打号码列表
 */
export const getCallableTels = (telStatusList = {}) => {
    return Object.keys(telStatusList).filter(tel => 
        canCallTel(tel, telStatusList)
    );
};

/**
 * 获取通话中号码列表
 * @param {Object} telStatusList - 号码状态列表
 * @returns {Array} 通话中号码列表
 */
export const getBusyTels = (telStatusList = {}) => {
    return Object.keys(telStatusList).filter(tel => 
        isTelInCall(tel, telStatusList)
    );
};

/**
 * 格式化号码状态更新时间
 * @param {string} lastUpdateTime - 最后更新时间
 * @returns {string} 格式化的时间
 */
export const formatUpdateTime = (lastUpdateTime) => {
    if (!lastUpdateTime) return '未知';
    
    try {
        const updateTime = new Date(lastUpdateTime);
        const now = new Date();
        const diffMs = now - updateTime;
        const diffSeconds = Math.floor(diffMs / 1000);
        const diffMinutes = Math.floor(diffSeconds / 60);
        const diffHours = Math.floor(diffMinutes / 60);
        
        if (diffSeconds < 60) {
            return `${diffSeconds}秒前`;
        } else if (diffMinutes < 60) {
            return `${diffMinutes}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else {
            return updateTime.toLocaleDateString();
        }
    } catch (error) {
        return '未知';
    }
};

/**
 * 获取号码状态统计信息
 * @param {Object} telStatusList - 号码状态列表
 * @returns {Object} 统计信息
 */
export const getTelStatusStats = (telStatusList = {}) => {
    const allTels = Object.keys(telStatusList);
    const onlineTels = getOnlineTels(telStatusList);
    const callableTels = getCallableTels(telStatusList);
    const busyTels = getBusyTels(telStatusList);
    
    const statusCounts = {};
    allTels.forEach(tel => {
        const status = getTelStatus(tel, telStatusList).status;
        statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
    
    return {
        total: allTels.length,
        online: onlineTels.length,
        offline: allTels.length - onlineTels.length,
        callable: callableTels.length,
        busy: busyTels.length,
        statusCounts,
        onlineRate: allTels.length > 0 ? (onlineTels.length / allTels.length * 100).toFixed(1) : '0'
    };
};
