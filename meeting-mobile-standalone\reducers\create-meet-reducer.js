/*
 * @File: 创建会议reducer
 * @Author: liu<PERSON>
 * @Date: 2021-06-10 14:56:00
 * @version: V0.0.0.1
 * @LastEditTime: 2024-05-13 10:32:33
 */
const ISSHOWCREATENEET = 'ISSHOWCREATENEET'
const CURMEETDETAIL = 'CURMEETDETAIL'
const MYMEETLIST = 'MYMEETLIST'
const HISTORYMEETLIST = 'HISTORYMEETLIST'
const HISTORYALLDATA = 'HISTORYALLDATA'
const MEETEDITED = 'MEETEDITED'
const ISNEEDFILL = 'ISNEEDFILL'
const CREATEMEETMEM = 'CREATEMEETMEM'
const CREATEMEETINFO = 'CREATEMEETINFO'
const ISRECREATE = 'ISRECREATE'
const MEETINGRECORDING = 'MEETINGRECORDING'
const ISSHOWJOINMEET = 'ISSHOWJOINMEET'
const CONTROLMEETDETAIL ='CONTROLMEETDETAIL'
const ISVENUECONTROL = 'ISVENUECONTROL'
const SETISCHANGEFROM = 'SETISCHANGEFROM'
const SEARCHRESULTLIST = 'SEARCHRESULTLIST'


const initState = {
    isShowCreateMeet:false,
    myMeetList: [],   //我的会议列表
    controlMeetDetail:{}, // 主控页面会议详情
    historyMeetList: [],   //历史会议列表
    historyAllData:{},  //历史会议所有记录
    curMeetDetail:{},  //当前会议详情，curMeetType:history/reserve/instant
    meetEdited:false,  //当前是否是编辑会议状态
    isNeedFill:false,  //是否需要填充表格信息
    createMeetMem:[],  //创建会议的人员信息
    createMeetInfo:{}, //创建会议的除人员之外的其他信息
    isReCreate:false, //是否为历史会议再次创建
    meetingRecording:false,//会议录制界面
    isshowjoinmeet:false,//id入会界面
    isVenueControl:false,
    ischangeFrom:false, //是否更新创建会议
    searchResultList:null //搜索结果列表
}; 

export function createMeetReducer(state=initState,action) {
    switch (action.type){
        case ISSHOWCREATENEET:return {...state,isShowCreateMeet:action.data};
        case CURMEETDETAIL:return {...state,curMeetDetail:action.data};
        case MYMEETLIST:return {...state,myMeetList:action.data};
        case HISTORYMEETLIST:return {...state,historyMeetList:action.data};
        case HISTORYALLDATA:return {...state,historyAllData:action.data};
        case MEETEDITED:return {...state,meetEdited:action.data};
        case ISNEEDFILL:return {...state,isNeedFill:action.data};
        case CREATEMEETMEM:return {...state,createMeetMem:action.data};
        case CREATEMEETINFO:return {...state,createMeetInfo:action.data};
        case ISRECREATE:return {...state,isReCreate:action.data};
        case SETISCHANGEFROM:return {...state,ischangeFrom:action.data};
        case MEETINGRECORDING:return {...state,meetingRecording:action.data};
        case ISSHOWJOINMEET:return {...state,isshowjoinmeet:action.data};
        case CONTROLMEETDETAIL:return {...state,controlMeetDetail:action.data}
        case ISVENUECONTROL:return {...state,isVenueControl:action.data}
        case SEARCHRESULTLIST:return {...state,searchResultList:action.data}
        default: return state;
    }
}

// 设置搜索结果列表
export function setSearchResultList(data){
    return {type:SEARCHRESULTLIST,data:data}
}

/**
 * 设置是否显示会场控制
 */
export function setIsVenueControl(data){
    return {type:ISVENUECONTROL,data:data}
}
/**
 * 设置是否显示会议录制页面
 */
export function setMeetingRecording(data){
    return {type:MEETINGRECORDING,data:data}
}

/**
 * 设置是否显示创建会议页面
 */
export function setIsShowCreateMeet(data){
    return {type:ISSHOWCREATENEET,data:data}
}

/**
 * 设置当前会议详情
 */
export function setCurMeetDetail(data){
    return {type:CURMEETDETAIL,data:data}
}

/**
 * 设置我的会议列表
 */
export function setMyMeetList(data){
    return {type:MYMEETLIST,data:data}
}

/**
 * 设置历史会议列表
 */
export function setHistoryMeetList(data){
    return {type:HISTORYMEETLIST,data:data}
}

/**
 * 设置历史会议所有数据
 */
export function setHistoryAllData(data){
    return {type:HISTORYALLDATA,data:data}
}

/**
 * 设置当前是否是编辑会议状态
 */
export function setMeetEdited(data){
    return {type:MEETEDITED,data:data}
}

/**
 * 设置是否需要填充表格信息
 */
export function setIsNeedFill(data){
    return {type:ISNEEDFILL,data:data}
}

/**
 * 设置创建会议的人员信息
 */
export function setCreateMeetMem(data){
    return {type:CREATEMEETMEM,data:data}
}

/**
 * 设置创建会议的除人员之外的其他信息
 */
export function setCreateMeetInfo(data){
    return {type:CREATEMEETINFO,data:data}
}

/**
 * 设置是否为历史会议再次创建
 */
export function setIsReCreate(data){
    return {type:ISRECREATE,data:data}
}

/**
 * 设置是否显示加入会议页面
 */
export function setIsShowjoinMeet(data){
    return {type:ISSHOWJOINMEET,data:data}
}

/**
 * 设置主控页面会议详情
 */
export function setControlMeetDetail(data){
    return {type:CONTROLMEETDETAIL,data:data}
}

/**
 * 设置是否更新创建会议
 */
export function setIschangeFrom(data){
    return {type:SETISCHANGEFROM,data:data}
}
