.call {
	width: 100%;
	height: 100%;
	.call-type-content {
		width: 100%;
		height: 64px;
		display: flex;
		align-items: center;
		justify-content: center;
		border-bottom: 1px solid #F0F0F0;
		.radio-button {
			height: 40px;
			line-height: 40px;
		}
	}
	.tel-list-content {
		width: 100%;
		height: calc(100% - 64px - 38px - 200px);
		overflow-y: auto;
		overflow-x: hidden;
		.call-info {
			display: flex;
			align-items: center;
			.icon-call {
				font-size: 15px;
				margin-right: 6px;
			}
		}
		.no-answer {
			color: #ff7875;
		}
		&.full {
			height: calc(100% - 64px - 200px);
		}
	}
	.dial-content {
		position: absolute;
		top: 0;
		height: calc(100% - 200px);
		width: 100%;
		z-index: 2;
		background: #FFFFFF;
		.dial-text-content {
			position: absolute;
			top: 20px;
			width: 100%;
			padding: 12px;
			overflow-x: auto;
			overflow-y: hidden;
			font-size: 36px;
			text-align: center;
		}
	}
	.number-keyboard-content {
		position: absolute;
		bottom: 0;
		height: 200px;
		width: 100%;
		display: flex;
		z-index: 3;
		.number-content {
			width: 75%;
			height: 100%;
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			.unit-key {
				width: 33.3%;
				height: 25%;
				border: 1px solid #F0F0F0;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24px;
			}
		}
		.action-content {
			width: 25%;
			height: 100%;
			display: flex;
			flex-direction: column;
			.unit-key {
				width: 100%;
				height: 25%;
				border: 1px solid #F0F0F0;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24px;
				border-left: none;
			}
			.unit-action-key {
				width: 100%;
				height: 37.5%;
				border: 1px solid #F0F0F0;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24px;
				border-left: none;
				background: #73d13d;
				color: #FFFFFF;
			}
		}
	}
}