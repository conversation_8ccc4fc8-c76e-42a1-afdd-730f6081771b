/*
 * @File: 项目接口存放文件
 * @Author: liulian
 * @Date: 2019-11-20 19:11:49
 * @version: V0.0.0.1
 * @LastEditTime: 2024-12-19 17:49:01
 */ 
import getServices from "./axios-request";
import {getToken} from '../config/constants'

const videoPref = '/scooper-video';    //视频调度后台接口前缀
const loginPrefix = '/dispatch-web';   //login接口前缀
const disPreFix = '/dispatch-web';   // 网页调度后台接口前缀
const conferencePrefix = '/dispatch-web/api';   //协同会商接口前缀
const mitPrefix = '/scooper-mits-conf';   //mits接口前缀
const corePrefix = '/scooper-core-rest/data';   //通讯录前缀
const call = '/dispatch-web/api/call'; //call前缀
const sms = '/scooper-app-msg/message'; //sms前缀

export let apis = {

    file: {
        download: '/scooper-app-msg/file/download'  
    },
    auth: {
        captcha: "/dispatch-web/api/login/captcha"
    },
    call: getServices(`${call}`, getToken, {
        listCallStatus: { url: '/listCallStatus', type: 'post' }, //获取设备状态
    }),
    login: getServices(`${loginPrefix}`, getToken, {
        login: { url: '/api/login/login', type: 'post',getResp:true },
        logout: { url: '/login/doLoginOut', type: 'get' }
    }),
    video:getServices(`${videoPref}`,getToken,{
        queryFacilityList:{url:'/data/baseRes/queryBaseResList',type:'post'} ,  //条件查询设备列表（isPerm）
        pageSearchFacilityList:{url:'/data/baseRes/pageSearchBaseResList',type:'post'}, //分页查询设备列表
        queryFacilityPath:{url:'/data/baseRes/queryBaseResPathByResId',type:'post'}, //根据id查询设备或节点所在的路径
    }),
    core:getServices(`${corePrefix}`,getToken,{
        queryOrgMember:{ url: '/contacts/orgMemberManage/queryOrgMember', type: 'post'}, //分页查询部门成员信息
        findOrgMemberByTel:{url:'/contacts/orgMemberManage/findOrgMemberByTel',type:'post'}, //根据号码查询人员信息
        getDispOperDetail:{url:'/dispatch/dispOperManage/getDispOperDetail',type:'post'},  //获取调度操作员详情
        getDispCenterDetail:{url:'/dispatch/dispCenterManage/getDispCenterDetail',type:'post'},  //获取调度中心详情信息
        listDispGroup:{url:'/dispatch/dispGroupManage/listDispGroup',type:'post'},  //获取快捷组列表
        queryDispMember:{url:'/dispatch/dispMemberManage/queryDispMember',type:'post'},  //分页获取快捷组成员信息
        mod:{url:'/system/authManage/mod?name=scooper-meet',type:'get'},  //修改权限
    }),
    disp:getServices(`${disPreFix}`,getToken,{
        createMeet:{url:'/api/meet/createMeet',type:'get',getResp:true}, // 创建会议
        getMeetByMeetAccess:{url:'/api/meet/getMeetByMeetAccess',type:'post',getResp:true},//通过会议号获取会议id
        setMeetWait:{url:'/api/meet/setMeetWait',type:'post',getResp:true}, //设置等候室开关
        joinWaitRoom:{url:'/api/meet/joinWaitRoom',type:'post',getResp:true}, //加入成员到等候室 meetId=1001&memTel=100278
        getMeetBaseInfo:{url:'/api/meet/getMeetBaseInfo',type:'post',getResp:true},
        meetApplyAgree:{url:'/api/meet/meetApplyAgree',type:'post',getResp:true}, //同意或者不同意入会
        listMeetWaitRoom:{url:'/api/meet/listMeetWaitRoom',type:'post',getResp:true}, //查询等候室
        setMeetHands:{url:'/api/meet/setMeetHands',type:'post',getResp:true}, //设置举手发言
        queryMeetMinutes:{url:'/api/meet/queryMeetMinutes',type:'post'},  //查询会议纪要
        setConfig:{url:'/api/conn/changeCfg',type:'post'}, //设置配置
        getPeopleName:{url:'/data/orgMember/getResNameByTel',type:'postjson'}, //获取人员名称
        config:{url:'/conf/data',type:'post'}, //获取配置
        kiv:{url:'/conf/kiv',type:'post'}, //获取配置
        getNowTime:{url:'/api/conn/getNowTime',type:'get',getResp:true},
        subVoice:{url:'/api/call/subVoiceRecognition',type:'post'},
        getDispOperByTel:{url:'/data/dispOper/getDispOperByTel',type:'get',getResp:true} //通过号码查询是否操作员号码
    }),
    conference:getServices(`${conferencePrefix}`,getToken,{
        listMeetsHistory:{url:'/meet/listMeetsHistory',type:'get',getResp:true},    //查询历史会议记录列表
        getTmpMem:{url:'/meet/getTmpMem',type:'post',getResp:true},    //查询临时成员列表
        getMeetAdd:{url:'/meet/getMeetAdd',type:'get'},     //根据MeetId查询会场信息
        getMeetMediaInto:{url:'/meet/getMeetMediaInto',type:'get'},    //查询当前的可选音视频源（跟操作员关联 与会场无关）
        setMeetAudioInto:{url:'/meet/setMeetAudioInto',type:'post',getResp:true},   //设置会场音频输入
        setMeetVideoInto:{url:'/meet/setMeetVideoInto',type:'post',getResp:true},   //设置会场视频输入
        setMemberJoinLevel:{url:'/meet/setMemberJoinLevel',type:'post',getResp:true},     //设置成员入会时是否禁言
        getMeetScreenType:{url:'/meet/getMeetScreenType',type:'get',getResp:true},   //获取会场分屏类型
        setMeetScreenType:{url:'/meet/setMeetScreenType',type:'post',getResp:true},   //设置会场分屏类型
        setMeetMixScreen:{url:'/meet/setMeetMixScreen',type:'post',getResp:true},   //设置会场混屏
        getMeetMixScreen:{url:'/meet/getMeetMixScreen',type:'get',getResp:true},   //获取会场混屏
        setMeetRecord:{url:'/meet/setMeetRecord',type:'post',getResp:true},   //设置会场录制
        getMeetRecord:{url:'/meet/getMeetRecord',type:'get',getResp:true},   //获取会场录制状态
        setMeetShare:{url:'/meet/setMeetShare',type:'post',getResp:true},   //设置会场分享
        getMeetShare:{url:'/meet/getMeetShare',type:'get',getResp:true},   //获取会场分享状态
    }),
    shareURl:getServices(`${disPreFix}`,getToken,{
        getMeetShareURl:{url:'/api/meet/getMeetShareURl',type:'post',getResp:true},   //获取分享链接
        joinMeetShare:{url:'/api/meet/joinMeetShare',type:'post',getResp:true}, //通过分享链接加入会议
        getMeetInfo:{url:'/api/meet/getMeet',type:'get',getResp:true}, //通过会议ID查到
        joinMeetMuted:{url:'/api/meet/joinMeetMuted',type:'post',getResp:true}, //加入会场默认禁言
        allowRelieveMuted:{url:'/api/meet/allowRelieveMuted',type:'post',getResp:true}  // 允许解除禁言
    }),
    meet:getServices(`${disPreFix}`,getToken,{
        joinVideoMember:{url:'/api/meet/joinVideoMember',type:'get',getResp:true}, // 加入会议
        kickMember:{url:'/api/meet/kickMember',type:'get',getResp:true}, // 踢出成员
        changeMemberLevel:{url:'/api/meet/changeMemberLevel',type:'get',getResp:true}, // 设置成员入会时是否禁言
        editMeet:{url:'/api/meet/editMeet',type:'post',getResp:true}, // 编辑会议
    })
}
