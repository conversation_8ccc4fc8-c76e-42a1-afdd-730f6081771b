/*
 * @File: CometD PubSub 优化版本
 * @Author: liulian
 * @Date: 2025-01-21 00:00:00
 * @version: V1.0.0
 * @Description: 使用 PubSub 模式优化的 CometD 订阅管理器
 */

import { CometD } from 'cometd/cometd';
import PubSub from 'pubsub-js';
import { platUrl } from '../util/method';

/**
 * CometD PubSub 管理器
 * 使用发布订阅模式来处理 CometD 消息分发
 */
class CometDPubSubManager {
    constructor(options = {}) {
        this._cometd = new CometD();
        this._connected = false;
        this._subscriptions = new Map(); // CometD 订阅管理
        this._pubsubTokens = new Map(); // PubSub 令牌管理
        this._reconnectAttempts = 0;
        this._maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this._reconnectInterval = options.reconnectInterval || 3000;
        this._logLevel = options.logLevel || 'info';
        
        // 初始化 PubSub 事件主题
        this._initEventTopics();
    }

    /**
     * 初始化事件主题常量
     */
    _initEventTopics() {
        this.TOPICS = {
            // 连接状态相关
            CONNECTION_ESTABLISHED: 'cometd.connection.established',
            CONNECTION_LOST: 'cometd.connection.lost',
            CONNECTION_RETRY: 'cometd.connection.retry',
            CONNECTION_FAILED: 'cometd.connection.failed',
            
            // 会议相关事件
            MEET_SPLIT_SCREEN_SET: 'meet.splitscreen.set',
            MEET_SPLIT_SCREEN: 'meet.splitscreen',
            MEET_SHARE_TEL: 'meet.share.tel',
            MEET_OPERATION: 'meet.operation',
            MEET_HANDS_UP: 'meet.handsup',
            MEET_STATUS_CHANGE: 'meet.status.change',
            MEET_JOIN: 'meet.join',
            MEET_LEAVE: 'meet.leave',
            MEET_MEMBER_STATUS: 'meet.member.status',
            MEET_RECORD: 'meet.record',
            MEET_MEMBER_RECORD: 'meet.member.record',
            MEET_MEMBER_RECORD_DELETE: 'meet.member.record.delete',
            
            // 通话相关事件
            CALL_STATUS_CHANGE: 'call.status.change',
            CALL_INCOMING: 'call.incoming',
            
            // 系统相关事件
            SYSTEM_DISCONNECT: 'system.disconnect',
            
            // 错误事件
            ERROR_MESSAGE: 'error.message',
            ERROR_SUBSCRIPTION: 'error.subscription'
        };
    }

    /**
     * 初始化 CometD 连接
     * @param {Object} config 配置选项
     */
    async initialize(config = {}) {
        try {
            const accId = JSON.parse(localStorage.getItem('scooperOwnerInformation'))?.id;
            const token = config.token || localStorage.getItem('scooperCoreToken');
            const url = config.url || `${platUrl}/dispatch-web/cometd`;

            if (!token) {
                throw new Error('认证令牌不能为空');
            }

            this._setupCometD(url, token);
            await this._connect();
            
            this._setupAutoSubscriptions(accId);
            
            this._log('info', 'CometD PubSub 管理器初始化成功');
            
        } catch (error) {
            this._log('error', 'CometD 初始化失败:', error);
            PubSub.publish(this.TOPICS.CONNECTION_FAILED, { error });
            throw error;
        }
    }

    /**
     * 配置 CometD
     */
    _setupCometD(url, token) {
        this._cometd.websocketEnabled = true;
        this._cometd.configure({
            url: url,
            logLevel: this._logLevel
        });

        // 监听连接状态
        this._cometd.addListener('/meta/connect', (message) => {
            this._handleConnectionStatus(message);
        });

        // 监听握手状态
        this._cometd.addListener('/meta/handshake', (message) => {
            this._handleHandshake(message);
        });

        // 监听订阅状态
        this._cometd.addListener('/meta/subscribe', (message) => {
            this._handleSubscription(message);
        });

        // 监听断开连接
        this._cometd.addListener('/meta/disconnect', (message) => {
            this._handleDisconnection(message);
        });
    }

    /**
     * 建立连接
     */
    _connect() {
        return new Promise((resolve, reject) => {
            const token = localStorage.getItem('scooperCoreToken');
            
            this._cometd.handshake({
                ext: { 
                    'sc-auth': { token } 
                }
            }, (handshakeReply) => {
                if (handshakeReply.successful) {
                    this._connected = true;
                    this._reconnectAttempts = 0;
                    PubSub.publish(this.TOPICS.CONNECTION_ESTABLISHED, handshakeReply);
                    resolve(handshakeReply);
                } else {
                    this._connected = false;
                    PubSub.publish(this.TOPICS.CONNECTION_FAILED, handshakeReply);
                    reject(new Error('握手失败: ' + handshakeReply.error));
                }
            });
        });
    }

    /**
     * 处理连接状态变化
     */
    _handleConnectionStatus(message) {
        const wasConnected = this._connected;
        this._connected = message.successful;

        if (!wasConnected && this._connected) {
            this._log('info', 'CometD 连接已建立');
            PubSub.publish(this.TOPICS.CONNECTION_ESTABLISHED, message);
        } else if (wasConnected && !this._connected) {
            this._log('warn', 'CometD 连接丢失');
            PubSub.publish(this.TOPICS.CONNECTION_LOST, message);
            this._attemptReconnect();
        }
    }

    /**
     * 处理握手
     */
    _handleHandshake(message) {
        if (message.successful) {
            this._log('info', 'CometD 握手成功');
        } else {
            this._log('error', 'CometD 握手失败:', message.error);
        }
    }

    /**
     * 处理订阅状态
     */
    _handleSubscription(message) {
        if (message.successful) {
            this._log('info', `订阅成功: ${message.subscription}`);
        } else {
            this._log('error', `订阅失败: ${message.subscription}`, message.error);
            PubSub.publish(this.TOPICS.ERROR_SUBSCRIPTION, {
                channel: message.subscription,
                error: message.error
            });
        }
    }

    /**
     * 处理断开连接
     */
    _handleDisconnection(message) {
        this._connected = false;
        this._log('info', 'CometD 连接已断开');
        PubSub.publish(this.TOPICS.CONNECTION_LOST, message);
    }

    /**
     * 尝试重连
     */
    _attemptReconnect() {
        if (this._reconnectAttempts >= this._maxReconnectAttempts) {
            this._log('error', '重连次数已达上限，停止重连');
            PubSub.publish(this.TOPICS.CONNECTION_FAILED, {
                reason: 'max_reconnect_attempts_reached'
            });
            return;
        }

        this._reconnectAttempts++;
        this._log('info', `尝试重连 (${this._reconnectAttempts}/${this._maxReconnectAttempts})`);
        
        PubSub.publish(this.TOPICS.CONNECTION_RETRY, {
            attempt: this._reconnectAttempts,
            maxAttempts: this._maxReconnectAttempts
        });

        setTimeout(() => {
            if (!this._connected) {
                this._connect().catch(() => {
                    this._attemptReconnect();
                });
            }
        }, this._reconnectInterval);
    }

    /**
     * 设置自动订阅
     */
    _setupAutoSubscriptions(accId) {
        if (!accId) {
            this._log('warn', '账户ID为空，跳过自动订阅');
            return;
        }

        // 定义频道到主题的映射
        const channelTopicMap = [
            { channel: '/meetSplitScreenSetIndex', topic: this.TOPICS.MEET_SPLIT_SCREEN_SET },
            { channel: '/meetSplitScreen', topic: this.TOPICS.MEET_SPLIT_SCREEN },
            { channel: '/meetShareTel', topic: this.TOPICS.MEET_SHARE_TEL },
            { channel: '/cdispatch/disconnect', topic: this.TOPICS.SYSTEM_DISCONNECT },
            { channel: '/meet/meetOper', topic: this.TOPICS.MEET_OPERATION },
            { channel: '/meet/handsUp', topic: this.TOPICS.MEET_HANDS_UP },
            { channel: `/${accId}/meet/memRecordDelAL`, topic: this.TOPICS.MEET_MEMBER_RECORD_DELETE },
            { channel: `/${accId}/call/status`, topic: this.TOPICS.CALL_STATUS_CHANGE },
            { channel: `/${accId}/call/in`, topic: this.TOPICS.CALL_INCOMING },
            { channel: `/${accId}/meet/status`, topic: this.TOPICS.MEET_STATUS_CHANGE },
            { channel: `/${accId}/meet/join`, topic: this.TOPICS.MEET_JOIN },
            { channel: `/${accId}/meet/leave`, topic: this.TOPICS.MEET_LEAVE },
            { channel: `/${accId}/meet/memsts`, topic: this.TOPICS.MEET_MEMBER_STATUS },
            { channel: `/${accId}/meet/record`, topic: this.TOPICS.MEET_RECORD },
            { channel: `/${accId}/meet/memRecord`, topic: this.TOPICS.MEET_MEMBER_RECORD }
        ];

        // 批量订阅
        channelTopicMap.forEach(({ channel, topic }) => {
            this.subscribeChannel(channel, topic);
        });
    }

    /**
     * 订阅频道并发布到指定主题
     * @param {string} channel CometD 频道
     * @param {string} topic PubSub 主题
     * @param {Object} options 订阅选项
     */
    subscribeChannel(channel, topic, options = {}) {
        try {
            const subscription = this._cometd.subscribe(channel, (message) => {
                this._handleChannelMessage(channel, topic, message, options);
            });

            this._subscriptions.set(channel, {
                subscription,
                topic,
                options,
                createdAt: new Date()
            });

            this._log('info', `已订阅频道: ${channel} -> ${topic}`);
            return channel;

        } catch (error) {
            this._log('error', `订阅频道失败: ${channel}`, error);
            PubSub.publish(this.TOPICS.ERROR_SUBSCRIPTION, {
                channel,
                topic,
                error
            });
            throw error;
        }
    }

    /**
     * 处理频道消息
     */
    _handleChannelMessage(channel, topic, message, options) {
        try {
            // 消息预处理
            const processedMessage = this._preprocessMessage(message, options);
            
            // 记录日志
            this._log('debug', `收到消息 [${channel}]:`, processedMessage);
            
            // 发布到 PubSub
            PubSub.publish(topic, {
                channel,
                data: processedMessage.data,
                timestamp: new Date(),
                original: message
            });

        } catch (error) {
            this._log('error', `处理消息失败 [${channel}]:`, error);
            PubSub.publish(this.TOPICS.ERROR_MESSAGE, {
                channel,
                topic,
                error,
                message
            });
        }
    }

    /**
     * 消息预处理
     */
    _preprocessMessage(message, options) {
        let processedData = message.data || message;

        // 应用过滤器
        if (options.filter && typeof options.filter === 'function') {
            processedData = options.filter(processedData);
        }

        // 应用转换器
        if (options.transform && typeof options.transform === 'function') {
            processedData = options.transform(processedData);
        }

        return {
            data: processedData,
            metadata: {
                channel: message.channel,
                id: message.id,
                timestamp: message.timestamp || Date.now()
            }
        };
    }

    /**
     * 取消订阅频道
     * @param {string} channel 频道名称
     */
    unsubscribeChannel(channel) {
        const subscription = this._subscriptions.get(channel);
        if (subscription) {
            this._cometd.unsubscribe(subscription.subscription);
            this._subscriptions.delete(channel);
            this._log('info', `已取消订阅频道: ${channel}`);
            return true;
        }
        return false;
    }

    /**
     * 订阅 PubSub 主题
     * @param {string} topic 主题名称
     * @param {Function} callback 回调函数
     * @returns {string} 订阅令牌
     */
    subscribe(topic, callback) {
        const token = PubSub.subscribe(topic, callback);
        this._pubsubTokens.set(token, { topic, callback, createdAt: new Date() });
        return token;
    }

    /**
     * 取消 PubSub 订阅
     * @param {string} token 订阅令牌
     */
    unsubscribe(token) {
        if (this._pubsubTokens.has(token)) {
            PubSub.unsubscribe(token);
            this._pubsubTokens.delete(token);
            return true;
        }
        return false;
    }

    /**
     * 发布消息到 PubSub
     * @param {string} topic 主题
     * @param {*} data 数据
     */
    publish(topic, data) {
        PubSub.publish(topic, data);
    }

    /**
     * 获取连接状态
     */
    isConnected() {
        return this._connected;
    }

    /**
     * 获取订阅统计
     */
    getSubscriptionStats() {
        return {
            cometdSubscriptions: this._subscriptions.size,
            pubsubSubscriptions: this._pubsubTokens.size,
            connected: this._connected,
            reconnectAttempts: this._reconnectAttempts
        };
    }

    /**
     * 断开连接并清理资源
     */
    disconnect() {
        // 清理 CometD 订阅
        this._subscriptions.forEach((sub, channel) => {
            this._cometd.unsubscribe(sub.subscription);
        });
        this._subscriptions.clear();

        // 清理 PubSub 订阅
        this._pubsubTokens.forEach((sub, token) => {
            PubSub.unsubscribe(token);
        });
        this._pubsubTokens.clear();

        // 断开 CometD 连接
        this._cometd.disconnect();
        this._connected = false;

        this._log('info', 'CometD PubSub 管理器已断开连接');
    }

    /**
     * 日志记录
     */
    _log(level, message, ...args) {
        if (console[level]) {
            console[level](`[CometD-PubSub] ${message}`, ...args);
        }
    }
}

// 创建单例实例
const cometdPubSub = new CometDPubSubManager({
    maxReconnectAttempts: 5,
    reconnectInterval: 3000,
    logLevel: 'info'
});

export default cometdPubSub;
export { CometDPubSubManager };
