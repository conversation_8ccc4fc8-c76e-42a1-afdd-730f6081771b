/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-08-20 10:45:58
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-10-11 09:48:47
 * @FilePath: \wll-h5\src\view\third-login\index.jsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, {startTransition, useEffect, useState} from "react";
import style from './style/sass/index.module.scss';
import {dispatch_web_api, ecis_access_api,scooper_video_api,snrhtx_gw_sso_api} from "../../util/api";
import {message, Result, Spin} from "antd";
import {useSearchParams} from "react-router-dom";
import {useNavigate} from "react-router-dom";

function ThirdLogin() {

	const [searchParams] = useSearchParams();

	const navigate = useNavigate()

	const [registrationFailed, setRegistrationFailed] = useState(false);
	const [loading, setLoading] = useState(false);

	/**
	 * @desc 初始化程序
	 */
	useEffect(() => {
		let openId = searchParams.get('openId');
		let username = searchParams.get('username');
		let password = searchParams.get('password');
		let token = searchParams.get('token');
		let code = searchParams.get('code');
		setLoading(true);

		if (code) {
			snrhtx_gw_sso_api.loginH5({ code }).then(info => {
				setLoading(false);
				let scooperCoreToken = info;
				localStorage.setItem('scooperCoreToken', scooperCoreToken);
				setRegistrationFailed(false);
				startTransition(() => {
					navigate('/main');
				})
			})
		}else if (token) {
			setLoading(false);
			localStorage.setItem('scooperCoreToken', token);
			setRegistrationFailed(false);
			startTransition(() => {
				navigate('/main');
			})
		}else{
			let params = {
				username,
				password,
			}
			scooper_video_api.login(params).then(info => {
				setLoading(false);
				let scooperCoreToken = info.token;
				localStorage.setItem('scooperOwnerInformation', JSON.stringify(info));
				localStorage.setItem('scooperCoreToken', scooperCoreToken);
				setRegistrationFailed(false);
				startTransition(() => {
					navigate('/main');
				})
			}).catch(error => {
				setLoading(false);
				console.log(`login: ${error}`);
				setRegistrationFailed(true);
				// message.error('未注册呼叫号码，请联系管理员');
			})
		}
		// ecis_access_api.getAccountByOpenId({ openId }).then(account => {
		// 	let params = {
		// 		username: account.accUsername,
		// 		password: account.accPassword,
		// 		captcha: 777,
		// 	}
		// 	dispatch_web_api.login(params).then(info => {
		// 		setLoading(false);
		// 		let scooperCoreToken = info.token;
		// 		localStorage.setItem('scooperOwnerInformation', JSON.stringify(info));
		// 		localStorage.setItem('scooperCoreToken', scooperCoreToken);
		// 		setRegistrationFailed(false);
		// 		startTransition(() => {
		// 			navigate('/main');
		// 		})
		// 	}).catch(error => {
		// 		setLoading(false);
		// 		console.log(`login: ${error}`);
		// 		setRegistrationFailed(true);
		// 		// message.error('未注册呼叫号码，请联系管理员');
		// 	})
		// }).catch(error => {
		// 	setLoading(false);
		// 	console.log(`getAccountByOpenId: ${error}`);
		// 	// message.error('未注册呼叫号码，请联系管理员');
		// 	setRegistrationFailed(true);
		// })
		
	}, []);

	return(
		<div className={style.thirdLogin}>
			{
				loading && (
					<Spin size='large' tip='系统加载中...' className={style.pageLoading}>
						<div style={{ minHeight: '200px' }}></div>
					</Spin>
				)
			}
			{
				registrationFailed &&
				<Result  status="403" title='未注册呼叫号码，请联系管理员' />
			}
		</div>
	)
}

export default ThirdLogin;