/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 全局使用的页面加载loading
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

import React from 'react';
import { Spin } from 'antd';
import { useSelector } from 'react-redux';

function PageLoading() {

    // 获取延迟、提示、以及是否展示
    const { delay, tip, showLoading } = useSelector(state => state.pageLoading);

    return(
        <div className='page-loading-content' style={{ display: showLoading? 'flex': 'none' }}>
            <Spin className='page-loading' delay={delay} tip={tip} size='large' spinning={showLoading}>
                <div style={{ minHeight: '100px', width: '100px' }}></div>
            </Spin>
        </div>
    )
}

export default PageLoading;
