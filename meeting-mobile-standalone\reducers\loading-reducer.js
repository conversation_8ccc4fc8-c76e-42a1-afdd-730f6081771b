/*
 * @File: 加载状态reducer
 * @Author: liulian
 * @Date: 2020-10-29 10:08:20
 * @version: V0.0.0.1
 * @LastEditTime: 2024-05-13 10:32:33
 */

const CHANG_LOADING = 'CHANG_LOADING';
const SKIN = 'SKIN';
const MEMIDMAPCACHE = 'MEMIDMAPCACHE'
const MEMTELMAPCACHE = 'MEMTELMAPCACHE';
const TELSTATUS_LIST = 'TELSTATUS_LIST';
const ALLCONFIG = 'ALLCONFIG';
const CREAREVIDEOUI = 'CREAREVIDEOUI';
const SETISVISITOR = 'SETISVISITOR'
const SETISLEAVEMEETING = 'SETISLEAVEMEETING'
const FLAG = 'FLAG'
const CONFIGDATA = 'CONFIGDATA'

const initState = {
    loading: true,
    skin: 'light',
    configData: {},  //所有配置项
    memIdMapCache: {},  //完整通讯录人员清单缓存  memId 和 数据的缓存信息
    memTelMapCache: {},  //完整通讯录人员清单缓存  memTel 和 Info
    telStatusList: {},   //号码状态列表  
    allConfig:{},
    flag:false,   //是否登陆
    createVideoUI:false,
    isLeaveMeeting:false,
    visitor:4, // 0 主持人 1.普通用户 2游客 3 听众
};

export function loadingReducer(state = initState, action) {
    switch (action.type) {
        case CHANG_LOADING: return { ...state, loading: action.loading };
        case SKIN: return { ...state, skin: action.data };
        case MEMIDMAPCACHE: return { ...state, memIdMapCache: action.data };
        case MEMTELMAPCACHE: return { ...state, memTelMapCache: action.data };
        case TELSTATUS_LIST: return { ...state, telStatusList: action.data };
        case ALLCONFIG:return {...state,allConfig:action.data};
        case FLAG:return {...state,flag:action.data}
        case CREAREVIDEOUI:return {...state,createVideoUI:action.data}
        case SETISVISITOR:return {...state,visitor:action.data}
        case SETISLEAVEMEETING:return {...state,isLeaveMeeting:action.data}
        case CONFIGDATA: return { ...state, configData: action.data };
        

        default: return state;
    }
}
export function setConfigData(data) {
    return { type: CONFIGDATA, data: data }

    
}
export function setFlag(data){
    return { type: FLAG, data: data }
}

export function changeLoading(loading) {
    return {
        type: CHANG_LOADING,
        loading: loading
    }
}
export function createVideoUI(data) {
    return {
        type: CREAREVIDEOUI,
        data: data
    }
}

export function setSkin(data) {
    return { type: SKIN, data: data }
}
/**
 * 设置完整通讯录人员清单信息缓存 memID 和 info
 */
export function setMemIdMapCache(data) {
    return { type: MEMIDMAPCACHE, data: data }
}
/**
 * 设置完整通讯录人员清单信息缓存 memTel 和 info
 */
export function setMemTelMapCache(data) {
    return { type: MEMTELMAPCACHE, data: data }
}
/**
 * 设置号码状态列表
 */
export function setTelStausList(data) {
    return { type: TELSTATUS_LIST, data: data }
}

/**
 * 设置所有配置
 */
export function setAllConfig(data){
    return {type:ALLCONFIG,data:data}
}

/**
 * 设置是否是游客
 */
export function setIsVisitor(data){
    return {type:SETISVISITOR,data:data}
}

/**
 * 设置是否离开会议
 */
export function setIsLeaveMeeting(data){
    return {type:SETISLEAVEMEETING,data:data}
}
