.call-in {
	width: 100%;
	height: 100%;
	.call-in-header {
		position: absolute;
		top: 10%;
		width: 100%;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		flex-direction: column;
		.mem-name {
			font-size: 32px;
		}
		.default-tip {
			font-size: 18px;
			margin-top: 12px;
		}
	}
	.call-in-action-content {
		position: absolute;
		bottom: 10%;
		height: 40%;
		width: 100%;
		display: flex;
		align-items: flex-end;
		justify-content: space-around;
		.icon-hung-up {
			width: 100px;
			height: 100px;
			background: url("../images/icon_hung_up.png") no-repeat;
			background-size: 100% 100%;
		}
		.icon-connect {
			width: 100px;
			height: 100px;
			background: url("../images/icon_connect.png") no-repeat;
			background-size: 100% 100%;
		}
	}
}