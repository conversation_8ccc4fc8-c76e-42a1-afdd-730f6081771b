.MeetBox{
    height: 100%;
    color: #fff;
    background: rgba(0, 0, 0, 0.8);
    
    .Meeting{
        height: 100%;
        overflow-y: auto;
        ul{
            .MeetingMembersList{
                margin-top: 0.4rem;
                padding: 0.1rem;
                height: 4rem;
                position: relative;
                display: flex;
                align-items: center;
                color: #fff;
                border-bottom: 1px solid rgba(255, 255, 255, 0.6);
                p{
                    white-space: nowrap;
                }
                .right{
                    position: absolute;
                    right: 0px;
                }
                .iconRadio{
                    height: 3rem;
                    width: 3rem;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    background-color:#02184e;
                    color: #fff;
                    border-radius:50%;
                    text-align: center;
                    line-height: 40px;
                    margin-right: 14px;
                    position: relative;
                    .anticon{
                        position: absolute;
                        bottom:0px;
                        right: 2px;
                    }
                }
                .MeetingMemName{
                    width:6rem;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
            .liveMetted{
                opacity: 0.6
            }
            .active{
                background-color:rgba(214, 235, 142, 0.6);
            }
        }
    }
    .Meeting-foot{
        display: flex;
        width: 260px;
        justify-content: space-around;
        margin-top: 20px;
    }
}

// 移动端右侧抽屉样式
.moblieRight {
    .ant-drawer-content {
        background: rgba(0, 0, 0, 0.9);
    }
    
    .ant-drawer-header {
        background: rgba(0, 0, 0, 0.9);
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        
        .ant-drawer-title {
            color: #fff;
        }
    }
    
    .ant-drawer-body {
        background: rgba(0, 0, 0, 0.9);
        padding: 0;
    }
}
