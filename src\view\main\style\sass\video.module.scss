.video-call {
	width: 100%;
	height: 100%;
	.video-header {
		position: absolute;
		top: 10%;
		width: 100%;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		flex-direction: column;
		.mem-name {
			font-size: 32px;
		}
		.default-tip {
			font-size: 18px;
			margin-top: 12px;
		}
	}
	.my-video-content {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		overflow: hidden;
		video {
			height: 100%;
			width: 100%;
		}
	}
	.other-video-content {
		position: absolute;
		top: 50px;
		right: 12px;
		width: 150px;
		height: 200px;
		overflow: hidden;
		z-index: 2;
		video {
			height: 100%;
			width: 100%;
		}
	}
	.actions-content {
		position: absolute;
		bottom: calc(10% + 100px + 24px);
		width: 100%;
		display: flex;
		justify-content: space-around;
		align-items: flex-end;
		.icon-mute {
			width: 60px;
			height: 60px;
			background: url("../images/icon_mute.png") no-repeat;
			background-size: 100% 100%;
			&.is-mute {
				background: url("../images/icon_mute_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}

		.icon-speaker {
			width: 60px;
			height: 60px;
			background: url("../images/icon_speaker.png") no-repeat;
			background-size: 100% 100%;
			&.is-speaker {
				background: url("../images/icon_speaker_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}
		.icon-close-camera {
			width: 60px;
			height: 60px;
			background: url("../images/icon_close_camera.png") no-repeat;
			background-size: 100% 100%;
			&.is-close-camera {
				background: url("../images/icon_close_camera_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}
		.icon-turn-camera {
			width: 60px;
			height: 60px;
			background: url("../images/icon_turn_camera.png") no-repeat;
			background-size: 100% 100%;
			&.is-front-camera {
				background: url("../images/icon_turn_camera_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}
	}
	.hungup {
		position: absolute;
		bottom: 10%;
		width: 100%;
		display: flex;
		justify-content: center;
		.icon-hung-up {
			display: inline-block;
			width: 100px;
			height: 100px;
			background: url("../images/icon_hung_up.png") no-repeat;
			background-size: 100% 100%;
		}
	}
}