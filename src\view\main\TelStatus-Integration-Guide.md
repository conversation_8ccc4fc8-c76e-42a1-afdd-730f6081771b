# 号码状态集成指南

## 功能概述

将 `initTelStatus` 函数更新的号码数据存储在 `MainContext` 中，提供全局访问的号码状态管理功能。

## 实现架构

### 1. 数据存储层 (MainContext)

```javascript
// 在 MainContext 中添加号码状态
const [telStatusList, setTelStatusList] = useState({});

// 提供给子组件
<MainContext.Provider value={{
  telStatusList,
  setTelStatusList,
  initTelStatus,
  // ... 其他属性
}}>
```

### 2. 数据更新层 (initTelStatus)

```javascript
const initTelStatus = () => {
  // 获取初始号码状态
  dispatch_web_api.listCallStatus().then(data => {
    const telStatusMap = {};
    
    (data || []).forEach(item => {
      if (item.tel) {
        telStatusMap[item.tel] = {
          ...item,
          status: item.onlineStatus === 'callst_offline' ? 'callst_offline' : item.status,
          lastUpdateTime: new Date().toISOString()
        };
      }
    });
    
    setTelStatusList(telStatusMap);
    updateTelStatus(); // 启动定时更新
  });
};
```

### 3. 工具函数层 (telStatusUtils.js)

提供号码状态相关的工具函数：

```javascript
import { getTelStatus, isTelOnline, canCallTel } from '../util/telStatusUtils';

// 获取号码状态
const statusInfo = getTelStatus(tel, telStatusList);

// 检查是否在线
const isOnline = isTelOnline(tel, telStatusList);

// 检查是否可拨打
const canCall = canCallTel(tel, telStatusList);
```

### 4. Hook 层 (useTelStatus.js)

提供 React Hook 封装：

```javascript
import { useTelStatus, useSingleTelStatus } from '../hooks/useTelStatus';

// 使用所有号码状态
const { telStatusList, isOnline, canCall } = useTelStatus();

// 使用单个号码状态
const singleStatus = useSingleTelStatus('13800138001');
```

## 数据结构

### telStatusList 结构

```javascript
{
  "13800138001": {
    tel: "13800138001",
    status: "callst_idle",           // 当前状态
    onlineStatus: "online",          // 在线状态
    timestamp: 1706428800000,        // 时间戳
    lastUpdateTime: "2025-01-28T10:00:00.000Z", // 最后更新时间
    // ... 其他 API 返回的字段
  },
  "13800138002": {
    // ...
  }
}
```

### 状态信息对象

```javascript
{
  status: "callst_idle",           // 原始状态
  onlineStatus: "online",          // 在线状态
  text: "空闲",                    // 状态文本
  color: "#52c41a",               // 状态颜色
  isOnline: true,                 // 是否在线
  canCall: true,                  // 是否可拨打
  timestamp: 1706428800000,       // 时间戳
  lastUpdateTime: "2025-01-28T10:00:00.000Z", // 最后更新时间
  raw: { /* 原始数据 */ }         // 原始数据
}
```

## 使用方法

### 1. 在组件中使用 Hook

```jsx
import React from 'react';
import { useTelStatus } from '../hooks/useTelStatus';

function MyComponent() {
  const { 
    telStatusList, 
    getTelStatusInfo, 
    isOnline, 
    canCall,
    statusStats 
  } = useTelStatus();

  const handleCall = (tel) => {
    if (canCall(tel)) {
      // 执行拨打逻辑
      console.log(`拨打 ${tel}`);
    } else {
      console.log(`${tel} 不可拨打`);
    }
  };

  return (
    <div>
      <p>总号码数: {statusStats.total}</p>
      <p>在线数量: {statusStats.online}</p>
      {/* 渲染号码列表 */}
    </div>
  );
}
```

### 2. 使用单个号码状态

```jsx
import { useSingleTelStatus } from '../hooks/useTelStatus';

function ContactItem({ tel }) {
  const statusInfo = useSingleTelStatus(tel);

  return (
    <div>
      <span>{tel}</span>
      <span style={{ color: statusInfo.color }}>
        {statusInfo.text}
      </span>
      {statusInfo.canCall && (
        <button onClick={() => handleCall(tel)}>
          拨打
        </button>
      )}
    </div>
  );
}
```

### 3. 使用多个号码状态

```jsx
import { useMultipleTelStatus } from '../hooks/useTelStatus';

function ContactList({ tels }) {
  const { statusInfos, onlineCount, callableCount } = useMultipleTelStatus(tels);

  return (
    <div>
      <p>在线: {onlineCount} / 可拨打: {callableCount}</p>
      {tels.map(tel => (
        <div key={tel}>
          {tel} - {statusInfos[tel]?.text}
        </div>
      ))}
    </div>
  );
}
```

### 4. 直接使用工具函数

```jsx
import React, { useContext } from 'react';
import { MainContext } from '../context';
import { getTelStatus, isTelOnline } from '../util/telStatusUtils';

function MyComponent() {
  const { telStatusList } = useContext(MainContext);

  const checkTelStatus = (tel) => {
    const statusInfo = getTelStatus(tel, telStatusList);
    const isOnline = isTelOnline(tel, telStatusList);
    
    console.log(`${tel} 状态:`, statusInfo.text, '在线:', isOnline);
  };

  return (
    // JSX 内容
  );
}
```

## 状态类型

### 支持的状态类型

| 状态值 | 中文描述 | 颜色 | 说明 |
|--------|----------|------|------|
| callst_offline | 离线 | #999999 | 设备离线 |
| callst_idle | 空闲 | #52c41a | 可以拨打 |
| callst_doubletalk | 通话中 | #ff4d4f | 正在通话 |
| callst_meet | 会议中 | #13c2c2 | 正在会议 |
| callst_hold | 保持 | #faad14 | 通话保持 |
| callst_ring | 振铃 | #1890ff | 正在振铃 |

### 状态判断逻辑

```javascript
// 是否在线
isOnline = onlineStatus !== 'callst_offline'

// 是否可拨打
canCall = isOnline && (status === 'callst_idle' || status === 'callst_answer')

// 是否通话中
isInCall = ['callst_doubletalk', 'callst_meet', 'callst_breakin'].includes(status)
```

## 定时更新机制

### 更新频率
- **初始化**: 组件挂载时立即获取
- **定时更新**: 每60秒更新一次
- **增量更新**: 只更新有变化的号码

### 更新逻辑
```javascript
// 每60秒执行一次
setInterval(() => {
  dispatch_web_api.listCallStatus({ timeInterval: 240 }).then(res => {
    // 只更新有变化的号码状态
    setTelStatusList(prev => {
      const updated = { ...prev };
      res.forEach(item => {
        if (hasStatusChanged(updated[item.tel], item)) {
          updated[item.tel] = { ...updated[item.tel], ...item };
        }
      });
      return updated;
    });
  });
}, 60000);
```

## 性能优化

### 1. 使用 useMemo 缓存计算结果
```javascript
const statusStats = useMemo(() => {
  return getTelStatusStats(telStatusList);
}, [telStatusList]);
```

### 2. 避免不必要的重渲染
```javascript
const getTelStatusInfo = useMemo(() => (tel) => {
  return getTelStatus(tel, telStatusList);
}, [telStatusList]);
```

### 3. 批量更新状态
```javascript
const batchUpdateTelStatus = (updates) => {
  setTelStatusList(prev => {
    const updated = { ...prev };
    Object.keys(updates).forEach(tel => {
      updated[tel] = { ...updated[tel], ...updates[tel] };
    });
    return updated;
  });
};
```

## 错误处理

### 1. API 调用失败
```javascript
dispatch_web_api.listCallStatus().catch(error => {
  console.error('❌ [号码状态] 获取失败:', error);
  // 可以设置默认状态或重试逻辑
});
```

### 2. 数据格式错误
```javascript
const getTelStatus = (tel, telStatusList = {}) => {
  if (!tel || !telStatusList) {
    return defaultStatus; // 返回默认状态
  }
  // 正常处理逻辑
};
```

### 3. 组件卸载清理
```javascript
useEffect(() => {
  return () => {
    if (window.updateTelStatusInterval) {
      clearInterval(window.updateTelStatusInterval);
    }
  };
}, []);
```

## 测试验证

使用 `TelStatusTest` 组件进行功能测试：

```jsx
import TelStatusTest from './components/TelStatusTest';

// 在开发环境中使用
<TelStatusTest />
```

测试功能包括：
- 状态统计显示
- 单个号码状态查询
- 多个号码状态查询
- 状态更新和刷新
- 搜索和过滤

## 最佳实践

1. **合理使用 Hook**: 根据需求选择合适的 Hook
2. **避免频繁更新**: 使用防抖或节流控制更新频率
3. **错误边界**: 添加错误处理和默认值
4. **性能监控**: 监控大量号码时的性能表现
5. **日志记录**: 记录关键操作和错误信息

通过这套完整的号码状态管理系统，可以在整个应用中方便地获取和使用号码状态信息，提供更好的用户体验。
