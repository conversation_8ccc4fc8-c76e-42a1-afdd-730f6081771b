/**
 * @Create: 全体禁言防抖功能测试组件
 * @Date: 2025/01/28
 * @desc: 测试全体禁言的防抖节流功能
 */

import React, { useState, useCallback, useMemo } from 'react';
import { Button, Space, Card, message, List, Tag } from 'antd';
import { debounce } from 'lodash';

function MuteAllDebounceTest() {
  const [isMuteAll, setIsMuteAll] = useState(false);
  const [isMuteAllProcessing, setIsMuteAllProcessing] = useState(false);
  const [clickLog, setClickLog] = useState([]);
  const [executionLog, setExecutionLog] = useState([]);

  // 模拟参与者数据
  const participants = [
    { id: '001', name: '张三', tel: '13800138001' },
    { id: '002', name: '李四', tel: '13800138002' },
    { id: '003', name: '王五', tel: '13800138003' },
    { id: '004', name: '赵六', tel: '13800138004' }
  ];

  // 模拟 API 调用
  const mockChangeMemberLevel = (params) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log(`模拟设置 ${params.tel} 的发言状态为 ${params.level}`);
        resolve({ success: true });
      }, Math.random() * 500 + 200); // 200-700ms 随机延迟
    });
  };

  // 记录点击事件
  const logClick = () => {
    const timestamp = new Date().toLocaleTimeString();
    setClickLog(prev => [...prev, `${timestamp}: 用户点击全体禁言按钮`]);
  };

  // 记录执行事件
  const logExecution = (action) => {
    const timestamp = new Date().toLocaleTimeString();
    setExecutionLog(prev => [...prev, `${timestamp}: ${action}`]);
  };

  // 实际的全体禁言执行函数
  const executeHandleMuteAll = useCallback(async () => {
    // 如果正在处理中，直接返回
    if (isMuteAllProcessing) {
      logExecution('操作被跳过 - 正在处理中');
      return;
    }

    setIsMuteAllProcessing(true);
    const newMuteState = !isMuteAll;
    logExecution(`开始执行全体${newMuteState ? '禁言' : '取消禁言'}操作`);

    try {
      // 批量处理所有参与者的禁言状态
      const promises = participants.map(participant =>
        mockChangeMemberLevel({
          tel: participant.tel,
          level: newMuteState ? 'audience' : 'speak'
        })
      );

      await Promise.all(promises);

      // 更新状态
      setIsMuteAll(newMuteState);
      logExecution(`全体${newMuteState ? '禁言' : '取消禁言'}操作完成`);
      message.success(newMuteState ? '已开启全体禁言' : '已取消全体禁言');
    } catch (error) {
      logExecution('操作失败: ' + error.message);
      message.error('操作失败，请重试');
    } finally {
      setIsMuteAllProcessing(false);
    }
  }, [isMuteAllProcessing, isMuteAll, participants]);

  // 创建防抖版本的处理函数
  const debouncedHandleMuteAll = useMemo(
    () => debounce(executeHandleMuteAll, 1000, {
      leading: true,  // 立即执行第一次
      trailing: false // 不执行最后一次
    }),
    [executeHandleMuteAll]
  );

  // 处理点击事件
  const handleClick = () => {
    logClick();
    debouncedHandleMuteAll();
  };

  // 清空日志
  const clearLogs = () => {
    setClickLog([]);
    setExecutionLog([]);
  };

  // 快速连续点击测试
  const rapidClickTest = () => {
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        handleClick();
      }, i * 100); // 每100ms点击一次
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
      <h2>全体禁言防抖功能测试</h2>
      
      <Card title="测试控制面板" style={{ marginBottom: '20px' }}>
        <Space wrap>
          <Button 
            type="primary" 
            onClick={handleClick}
            loading={isMuteAllProcessing}
            style={{
              backgroundColor: isMuteAll ? '#ff4d4f' : '#1890ff',
              borderColor: isMuteAll ? '#ff4d4f' : '#1890ff'
            }}
          >
            {isMuteAllProcessing 
              ? '处理中...' 
              : isMuteAll 
                ? '取消全体禁言' 
                : '全体禁言'
            }
          </Button>
          
          <Button onClick={rapidClickTest}>
            快速连续点击测试 (5次)
          </Button>
          
          <Button onClick={clearLogs}>
            清空日志
          </Button>
        </Space>
      </Card>

      <div style={{ display: 'flex', gap: '20px' }}>
        <Card title="当前状态" style={{ flex: 1 }}>
          <div>
            <p><strong>全体禁言状态:</strong> 
              <Tag color={isMuteAll ? 'red' : 'green'}>
                {isMuteAll ? '已禁言' : '未禁言'}
              </Tag>
            </p>
            <p><strong>处理状态:</strong> 
              <Tag color={isMuteAllProcessing ? 'orange' : 'blue'}>
                {isMuteAllProcessing ? '处理中' : '空闲'}
              </Tag>
            </p>
            <p><strong>参与者数量:</strong> {participants.length}</p>
          </div>
          
          <h4>参与者列表:</h4>
          <List
            size="small"
            dataSource={participants}
            renderItem={item => (
              <List.Item>
                {item.name} ({item.tel})
              </List.Item>
            )}
          />
        </Card>

        <Card title="点击日志" style={{ flex: 1 }}>
          <div style={{ maxHeight: '300px', overflow: 'auto' }}>
            {clickLog.length === 0 ? (
              <p style={{ color: '#999' }}>暂无点击记录</p>
            ) : (
              clickLog.map((log, index) => (
                <div key={index} style={{ marginBottom: '4px', fontSize: '12px' }}>
                  {log}
                </div>
              ))
            )}
          </div>
        </Card>

        <Card title="执行日志" style={{ flex: 1 }}>
          <div style={{ maxHeight: '300px', overflow: 'auto' }}>
            {executionLog.length === 0 ? (
              <p style={{ color: '#999' }}>暂无执行记录</p>
            ) : (
              executionLog.map((log, index) => (
                <div key={index} style={{ marginBottom: '4px', fontSize: '12px' }}>
                  {log}
                </div>
              ))
            )}
          </div>
        </Card>
      </div>

      <Card title="测试说明" style={{ marginTop: '20px' }}>
        <div>
          <h4>防抖配置:</h4>
          <ul>
            <li><strong>延迟时间:</strong> 1000ms (1秒)</li>
            <li><strong>leading: true</strong> - 立即执行第一次点击</li>
            <li><strong>trailing: false</strong> - 不执行最后一次点击</li>
          </ul>
          
          <h4>预期行为:</h4>
          <ul>
            <li>第一次点击立即执行</li>
            <li>1秒内的后续点击被忽略</li>
            <li>处理中状态阻止重复执行</li>
            <li>快速连续点击只执行一次</li>
          </ul>
          
          <h4>测试步骤:</h4>
          <ol>
            <li>点击"全体禁言"按钮，观察立即执行</li>
            <li>在处理过程中快速点击，观察被忽略</li>
            <li>使用"快速连续点击测试"验证防抖效果</li>
            <li>查看日志对比点击次数和执行次数</li>
          </ol>
        </div>
      </Card>
    </div>
  );
}

export default MuteAllDebounceTest;
