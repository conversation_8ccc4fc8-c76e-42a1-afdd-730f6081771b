/**
 * @Create: 参与者列表组件
 * @Date: 2025/01/28
 * @desc: 会议参与者列表弹窗
 */

import React, { useState } from 'react';
import { Popup, List, Avatar, Badge } from 'antd-mobile';
import {
  AudioOutlined,
  AudioMutedOutlined,
  VideoCameraOutlined,
  EyeInvisibleOutlined,
  CrownOutlined,
  UserAddOutlined,
  SoundOutlined,
  SoundFilled
} from '@ant-design/icons';
import InviteParticipants from './InviteParticipants';
import style from '../style/sass/participants-list.module.scss';

function ParticipantsList({
  visible,
  participants = [],
  onClose,
  isHost = false,
  isMuteAll = false,
  isMuteAllProcessing = false,
  onInvite,
  onMuteAll,
  meetingDuration = '00:00:00',
  meetingData = {}
}) {
  const [showInviteModal, setShowInviteModal] = useState(false);

  // 处理邀请按钮点击
  const handleInviteClick = () => {
    setShowInviteModal(true);
  };

  // 邀请成功回调
  const handleInviteSuccess = (invitedUsers) => {
    console.log('邀请成功:', invitedUsers);
    setShowInviteModal(false);
    // 可以在这里更新参与者列表或触发其他操作
    onInvite && onInvite(invitedUsers);
  };

  // 渲染参与者项
  const renderParticipantItem = (participant) => {
    return (
      <List.Item
        key={participant.id}
        className={style['participant-item']}
        prefix={
          <div className={style['participant-avatar']}>
            <Avatar 
              size={40}
              style={{ 
                backgroundColor: participant.isHost ? '#1890ff' : '#52c41a',
                color: '#ffffff'
              }}
            >
              {participant.name.charAt(0).toUpperCase()}
            </Avatar>
            {participant.isHost && (
              <div className={style['host-crown']}>
                <CrownOutlined />
              </div>
            )}
          </div>
        }
        extra={
          <div className={style['participant-controls']}>
            <div className={style['status-indicators']}>
              <span 
                className={`${style['audio-status']} ${participant.isAudioOn ? style.active : style.muted}`}
              >
                {participant.isAudioOn ? <AudioOutlined /> : <AudioMutedOutlined />}
              </span>
              <span 
                className={`${style['video-status']} ${participant.isVideoOn ? style.active : style.disabled}`}
              >
                {participant.isVideoOn ? <VideoCameraOutlined /> : <EyeInvisibleOutlined />}
              </span>
            </div>
          </div>
        }
      >
        <div className={style['participant-info']}>
          <div className={style['participant-name']}>
            {participant.name}
            {participant.isHost && (
              <span className={style['host-badge']}>主持人</span>
            )}
          </div>
          <div className={style['participant-status']}>
            {participant.isAudioOn && participant.isVideoOn && '音视频正常'}
            {participant.isAudioOn && !participant.isVideoOn && '仅音频'}
            {!participant.isAudioOn && participant.isVideoOn && '仅视频'}
            {!participant.isAudioOn && !participant.isVideoOn && '音视频关闭'}
          </div>
        </div>
      </List.Item>
    );
  };

  // 按主持人和普通参与者分组
  const hosts = participants.filter(p => p.isHost);
  const members = participants.filter(p => !p.isHost);

  return (
    <>
      <Popup
      visible={visible}
      onMaskClick={onClose}
      position="right"
      bodyStyle={{
        width: '80vw',
        maxWidth: '320px',
        height: '100vh',
        padding: 0
      }}
    >
      <div className={style['participants-container']}>
        {/* 头部 */}
        <div className={style['participants-header']}>
          <h3 className={style['header-title']}>
            参与者 ({participants.length})
          </h3>
          <button 
            className={style['close-button']}
            onClick={onClose}
          >
            ✕
          </button>
        </div>

        {/* 参与者列表 */}
        <div className={style['participants-content']}>
          {/* 主持人列表 */}
          {hosts.length > 0 && (
            <div className={style['participants-section']}>
              <div className={style['section-title']}>主持人</div>
              <List className={style['participants-list']}>
                {hosts.map(renderParticipantItem)}
              </List>
            </div>
          )}

          {/* 普通参与者列表 */}
          {members.length > 0 && (
            <div className={style['participants-section']}>
              <div className={style['section-title']}>参与者</div>
              <List className={style['participants-list']}>
                {members.map(renderParticipantItem)}
              </List>
            </div>
          )}

          {/* 空状态 */}
          {participants.length === 0 && (
            <div className={style['empty-state']}>
              <p>暂无参与者</p>
            </div>
          )}
        </div>

        {/* 底部操作区 */}
        <div className={style['participants-footer']}>
          {/* 会议信息 */}
          <div className={style['meeting-info']}>
            <div className={style['info-item']}>
              <span className={style['info-label']}>会议时长:</span>
              <span className={style['info-value']}>{meetingDuration}</span>
            </div>
            <div className={style['info-item']}>
              <span className={style['info-label']}>参与人数:</span>
              <span className={style['info-value']}>{participants.length}人</span>
            </div>
          </div>

          {/* 主持人操作按钮 */}
          {isHost && (
            <div className={style['host-actions']}>
              <div
                className={style['action-button']}
                onClick={handleInviteClick}
              >
                <UserAddOutlined className={style['action-icon']} />
                <span className={style['action-text']}>邀请</span>
              </div>

              <div
                className={`${style['action-button']} ${isMuteAll ? style['active'] : ''} ${isMuteAllProcessing ? style['processing'] : ''}`}
                onClick={isMuteAllProcessing ? undefined : onMuteAll}
                style={{
                  opacity: isMuteAllProcessing ? 0.6 : 1,
                  cursor: isMuteAllProcessing ? 'not-allowed' : 'pointer'
                }}
              >
                {isMuteAllProcessing ? (
                  <div className={style['loading-icon']}>⏳</div>
                ) : isMuteAll ? (
                  <SoundFilled className={style['action-icon']} />
                ) : (
                  <SoundOutlined className={style['action-icon']} />
                )}
                <span className={style['action-text']}>
                  {isMuteAllProcessing
                    ? '处理中...'
                    : isMuteAll
                      ? '取消全体禁言'
                      : '全体禁言'
                  }
                </span>
              </div>
            </div>
          )}
        </div>
      </div>
    </Popup>

    {/* 邀请参会人弹窗 */}
    <InviteParticipants
      visible={showInviteModal}
      onClose={() => setShowInviteModal(false)}
      meetingData={meetingData}
      onInviteSuccess={handleInviteSuccess}
    />
    </>
  );
}

export default ParticipantsList;
