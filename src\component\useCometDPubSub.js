/*
 * @File: CometD PubSub React Hook
 * @Author: liulian
 * @Date: 2025-01-21 00:00:00
 * @version: V1.0.0
 * @Description: 用于 React 组件的 CometD PubSub Hook
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { message } from 'antd';
import cometdPubSub from './cometd-pubsub';

/**
 * CometD PubSub React Hook
 * @param {Object} options 配置选项
 * @returns {Object} Hook 返回值
 */
export const useCometDPubSub = (options = {}) => {
    const [isConnected, setIsConnected] = useState(false);
    const [isConnecting, setIsConnecting] = useState(false);
    const [connectionError, setConnectionError] = useState(null);
    const [stats, setStats] = useState({});
    
    const subscriptionsRef = useRef(new Map());
    const isInitializedRef = useRef(false);

    /**
     * 初始化连接
     */
    const initialize = useCallback(async (config = {}) => {
        if (isInitializedRef.current) {
            console.warn('CometD 已经初始化，跳过重复初始化');
            return;
        }

        setIsConnecting(true);
        setConnectionError(null);

        try {
            await cometdPubSub.initialize({
                ...options,
                ...config
            });
            
            isInitializedRef.current = true;
            setIsConnected(true);
            setConnectionError(null);
            
            if (options.showNotifications !== false) {
                message.success('已连接到服务器');
            }
            
        } catch (error) {
            console.error('CometD 初始化失败:', error);
            setConnectionError(error);
            
            if (options.showNotifications !== false) {
                message.error('连接服务器失败');
            }
        } finally {
            setIsConnecting(false);
        }
    }, [options]);

    /**
     * 订阅主题
     */
    const subscribe = useCallback((topic, callback, subscriptionKey) => {
        if (!isInitializedRef.current) {
            console.warn('CometD 未初始化，无法订阅');
            return null;
        }

        try {
            const token = cometdPubSub.subscribe(topic, callback);
            const key = subscriptionKey || `${topic}_${Date.now()}`;
            
            subscriptionsRef.current.set(key, {
                token,
                topic,
                callback,
                createdAt: new Date()
            });

            console.log(`已订阅主题: ${topic} (key: ${key})`);
            return key;
            
        } catch (error) {
            console.error(`订阅主题失败: ${topic}`, error);
            return null;
        }
    }, []);

    /**
     * 取消订阅
     */
    const unsubscribe = useCallback((subscriptionKey) => {
        const subscription = subscriptionsRef.current.get(subscriptionKey);
        if (subscription) {
            cometdPubSub.unsubscribe(subscription.token);
            subscriptionsRef.current.delete(subscriptionKey);
            console.log(`已取消订阅: ${subscriptionKey}`);
            return true;
        }
        return false;
    }, []);

    /**
     * 发布消息
     */
    const publish = useCallback((topic, data) => {
        if (!isInitializedRef.current) {
            console.warn('CometD 未初始化，无法发布消息');
            return false;
        }

        try {
            cometdPubSub.publish(topic, data);
            return true;
        } catch (error) {
            console.error(`发布消息失败: ${topic}`, error);
            return false;
        }
    }, []);

    /**
     * 订阅频道
     */
    const subscribeChannel = useCallback((channel, topic, channelOptions = {}) => {
        if (!isInitializedRef.current) {
            console.warn('CometD 未初始化，无法订阅频道');
            return null;
        }

        try {
            return cometdPubSub.subscribeChannel(channel, topic, channelOptions);
        } catch (error) {
            console.error(`订阅频道失败: ${channel}`, error);
            return null;
        }
    }, []);

    /**
     * 取消订阅频道
     */
    const unsubscribeChannel = useCallback((channel) => {
        if (!isInitializedRef.current) {
            console.warn('CometD 未初始化，无法取消订阅频道');
            return false;
        }

        return cometdPubSub.unsubscribeChannel(channel);
    }, []);

    /**
     * 获取连接统计
     */
    const getStats = useCallback(() => {
        if (!isInitializedRef.current) {
            return {};
        }
        return cometdPubSub.getSubscriptionStats();
    }, []);

    /**
     * 重新连接
     */
    const reconnect = useCallback(async () => {
        if (isConnecting) {
            console.warn('正在连接中，请稍候...');
            return;
        }

        console.log('手动重新连接...');
        
        // 先断开现有连接
        if (isInitializedRef.current) {
            cometdPubSub.disconnect();
            isInitializedRef.current = false;
        }

        // 重新初始化
        await initialize();
    }, [initialize, isConnecting]);

    /**
     * 断开连接
     */
    const disconnect = useCallback(() => {
        if (isInitializedRef.current) {
            cometdPubSub.disconnect();
            isInitializedRef.current = false;
            setIsConnected(false);
            
            // 清理本地订阅记录
            subscriptionsRef.current.clear();
            
            console.log('已断开 CometD 连接');
        }
    }, []);

    // 设置连接状态监听
    useEffect(() => {
        if (!isInitializedRef.current) return;

        const connectionToken = cometdPubSub.subscribe(
            cometdPubSub.TOPICS.CONNECTION_ESTABLISHED,
            () => {
                setIsConnected(true);
                setConnectionError(null);
            }
        );

        const lostToken = cometdPubSub.subscribe(
            cometdPubSub.TOPICS.CONNECTION_LOST,
            () => {
                setIsConnected(false);
                if (options.showNotifications !== false) {
                    message.warning('与服务器连接中断');
                }
            }
        );

        const failedToken = cometdPubSub.subscribe(
            cometdPubSub.TOPICS.CONNECTION_FAILED,
            (topic, data) => {
                setIsConnected(false);
                setConnectionError(data.error || new Error('连接失败'));
                if (options.showNotifications !== false) {
                    message.error('连接服务器失败');
                }
            }
        );

        return () => {
            cometdPubSub.unsubscribe(connectionToken);
            cometdPubSub.unsubscribe(lostToken);
            cometdPubSub.unsubscribe(failedToken);
        };
    }, [options.showNotifications]);

    // 定期更新统计信息
    useEffect(() => {
        if (!isInitializedRef.current) return;

        const interval = setInterval(() => {
            setStats(getStats());
        }, options.statsUpdateInterval || 5000);

        return () => clearInterval(interval);
    }, [getStats, options.statsUpdateInterval]);

    // 组件卸载时清理
    useEffect(() => {
        return () => {
            // 清理所有订阅
            subscriptionsRef.current.forEach((subscription, key) => {
                cometdPubSub.unsubscribe(subscription.token);
            });
            subscriptionsRef.current.clear();

            // 如果是最后一个使用者，断开连接
            if (options.autoDisconnect !== false) {
                disconnect();
            }
        };
    }, [disconnect, options.autoDisconnect]);

    return {
        // 状态
        isConnected,
        isConnecting,
        connectionError,
        stats,
        
        // 方法
        initialize,
        subscribe,
        unsubscribe,
        publish,
        subscribeChannel,
        unsubscribeChannel,
        reconnect,
        disconnect,
        getStats,
        
        // 主题常量
        TOPICS: cometdPubSub.TOPICS
    };
};

/**
 * 会议相关的 CometD Hook
 * 预设了会议相关的订阅
 */
export const useMeetingCometD = (options = {}) => {
    const cometd = useCometDPubSub(options);
    const [meetingData, setMeetingData] = useState({
        members: [],
        status: 'idle',
        splitScreen: null,
        recording: false
    });

    // 订阅会议相关事件
    useEffect(() => {
        if (!cometd.isConnected) return;

        const subscriptions = [];

        // 会议状态变化
        subscriptions.push(
            cometd.subscribe(cometd.TOPICS.MEET_STATUS_CHANGE, (topic, data) => {
                setMeetingData(prev => ({
                    ...prev,
                    status: data.data.status
                }));
            }, 'meetStatus')
        );

        // 成员加入
        subscriptions.push(
            cometd.subscribe(cometd.TOPICS.MEET_JOIN, (topic, data) => {
                setMeetingData(prev => ({
                    ...prev,
                    members: [...prev.members, data.data]
                }));
            }, 'meetJoin')
        );

        // 成员离开
        subscriptions.push(
            cometd.subscribe(cometd.TOPICS.MEET_LEAVE, (topic, data) => {
                setMeetingData(prev => ({
                    ...prev,
                    members: prev.members.filter(m => m.tel !== data.data.tel)
                }));
            }, 'meetLeave')
        );

        // 分屏变化
        subscriptions.push(
            cometd.subscribe(cometd.TOPICS.MEET_SPLIT_SCREEN_SET, (topic, data) => {
                setMeetingData(prev => ({
                    ...prev,
                    splitScreen: data.data
                }));
            }, 'splitScreen')
        );

        return () => {
            subscriptions.forEach(key => {
                if (key) cometd.unsubscribe(key);
            });
        };
    }, [cometd]);

    return {
        ...cometd,
        meetingData,
        setMeetingData
    };
};

/**
 * 简化的 CometD Hook，只处理连接状态
 */
export const useCometDConnection = (config = {}) => {
    const [isConnected, setIsConnected] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        let mounted = true;

        const initConnection = async () => {
            try {
                await cometdPubSub.initialize(config);
                if (mounted) setIsConnected(true);
            } catch (err) {
                if (mounted) setError(err);
            }
        };

        initConnection();

        const connectionToken = cometdPubSub.subscribe(
            cometdPubSub.TOPICS.CONNECTION_LOST,
            () => {
                if (mounted) setIsConnected(false);
            }
        );

        const establishedToken = cometdPubSub.subscribe(
            cometdPubSub.TOPICS.CONNECTION_ESTABLISHED,
            () => {
                if (mounted) {
                    setIsConnected(true);
                    setError(null);
                }
            }
        );

        return () => {
            mounted = false;
            cometdPubSub.unsubscribe(connectionToken);
            cometdPubSub.unsubscribe(establishedToken);
        };
    }, []);

    return { isConnected, error };
};

export default useCometDPubSub;
