/*
 * @File: 配置信息文件
 * @Author: liulian
 * @Date: 2020-10-29 10:08:20
 * @version: V0.0.0.1
 * @LastEditTime: 2025-01-20 20:08:48
 */
import qs from 'qs';

export const ipUrl = '***************:9998'; //本地ip地址

export const HOME_PAGE = "/main/meet";     //登录之后默认跳转菜单

export const devmode = window.scooper?.configs?.devmode||false; //是否开发模式

export const platUrl = window.location.origin.includes('localhost')?'http://'+ipUrl:window?.scooper?.configs?.platUrl;//平台地址
 
//url传来的参数
export const urlParam = qs.parse(window.location.href.replace('?','&'));

export const getToken = ()=>(window.auth?.token || sessionStorage.getItem("meetWebToken"));

export const getAccountName = ()=> (sessionStorage.getItem('meetWebName') || '');

export const curMainTel = () =>(window.scooper?.meetDispatchManager?.accountDetail ? window.scooper?.meetDispatchManager?.accountDetail.mainTel:'');
export const curViceTel = () => (window.scooper?.meetDispatchManager?.accountDetail ? window.scooper?.meetDispatchManager?.accountDetail.viceTel:'');

// 会议时长选项
export const meetLengthOptions = [
    { meetLength: 1, key: 1, val: '30分钟' },
    { meetLength: 2, key: 2, val: '1小时' },
    { meetLength: 3, key: 3, val: '1小时30分钟' },
    { meetLength: 4, key: 4, val: '2小时' },
    { meetLength: 5, key: 5, val: '2小时30分钟' },
    { meetLength: 6, key: 6, val: '3小时' },
    { meetLength: 7, key: 7, val: '3小时30分钟' },
    { meetLength: 8, key: 8, val: '4小时' },
    { meetLength: 9, key: 9, val: '4小时30分钟' },
    { meetLength: 10, key: 10, val: '5小时' },
    { meetLength: 11, key: 11, val: '5小时30分钟' },
    { meetLength: 12, key: 12, val: '6小时' },
    { meetLength: 13, key: 13, val: '6小时30分钟' },
    { meetLength: 14, key: 14, val: '7小时' },
    { meetLength: 15, key: 15, val: '7小时30分钟' },
    { meetLength: 16, key: 16, val: '8小时' },
    { meetLength: 17, key: 17, val: '8小时30分钟' },
    { meetLength: 18, key: 18, val: '9小时' },
    { meetLength: 19, key: 19, val: '9小时30分钟' },
    { meetLength: 20, key: 20, val: '10小时' },
    { meetLength: 21, key: 21, val: '10小时30分钟' },
    { meetLength: 22, key: 22, val: '11小时' },
    { meetLength: 23, key: 23, val: '11小时30分钟' },
    { meetLength: 24, key: 24, val: '12小时' },
]

export const fblOptions = [
    { fbl: 1, key: 1, val: '1920*1080' },
    { fbl: 2, key: 2, val: '1280*720' },
    { fbl: 3, key: 3, val: '864*480' },
]

/**
 * 号码状态对应中文
 */
export const TEL_STATUS_VAL = {
    "callst_offline": '离线',
    "callst_idle": '空闲',
    "callst_hold": '保持',
    "callst_waitring": '等待振铃',
    "callst_ring": '振铃',
    "callst_answer": '应答',
    "callst_doubletalk": '通话中',
    "callst_transfer": '转接成功',
    "callst_transfering": '转接中',
    "callst_turning": '轮询中',
    "callst_meet": '会议中',
    "callst_breakin": '强插通话',
    "callst_monitor": '监听通话',
    "callst_callinwaitanswer": '等待应答',
    "callst_monitorring": '监控振铃',
    "callst_monitoranswer": '监控通话',
    "callst_monitoroffhook": '监听摘机'
}

/**
 * 调度状态常量
 */
export const stsConst = {
    OFFLINE: "callst_offline",			            //离线
    IDLE: "callst_idle",				            //空闲
    WAITRING: "callst_waitring",			        //预振铃
    CALLRING: "callst_ring",				        //振铃中
    CALLANSWER: "callst_answer",				    //应答
    CALLHOLD: "callst_hold",				        //保持中
    CALLTRANSFING: "callst_transfering",		    //转接中
    CALLTRANSFER: "callst_transfer",			    //转接
    CALLTURNING: "callst_turning",			        //轮询中
    DOUBLETALK: "callst_doubletalk",			    //双方通话
    MEET: "callst_meet",				            //在会场中
    BREAKIN: "callst_breakin",			            //强插
    MONITOR: "callst_monitor",			            //监听通话
    CALLINWAITANSWER: "callst_callinwaitanswer",    //呼入未应答
    MONITORRING: "callst_monitorring",		        //监控振铃
    MONITORANSWER: "callst_monitoranswer",	        //监控通话
    MONITOROFFHOOK: "callst_monitoroffhook"	        //监听摘机
}

/**
 * 会议成员状态
 */
export const MEET_MEMBER_STATUS = {
    MEETING: 'meeting',
    IN_MEET: 'inMeet',
    CALLING: 'calling',
    QUIT: 'quit',
    UNRESPONSE: 'unresponse',
    REJECT: 'reject',
    OUT_MEET: 'outMeet'
}

/**
 * 媒体类型
 */
export const MEDIA_TYPE = {
    AUDIO: 'audio',
    VIDEO: 'video'
}

/**
 * 成员级别
 */
export const MEMBER_LEVEL = {
    SPEAK: 'speak',
    AUDIENCE: 'audience',
    SPEAK_START: 'speak_start'
}
