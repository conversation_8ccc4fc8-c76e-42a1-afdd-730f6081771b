# Meeting Mobile 使用指南

## 快速开始

### 1. 复制组件到项目

将 `meeting-mobile-standalone` 文件夹复制到你的项目中。

### 2. 安装依赖

确保你的项目已安装所需依赖：

```bash
npm install react react-dom react-redux redux antd react-swipeable-views moment pubsub-js qs copy-to-clipboard axios crypto-js
```

### 3. 基础使用

```jsx
import React from 'react';
import MeetingMobile, { initMeetingMobile } from './meeting-mobile-standalone';

// 初始化
initMeetingMobile({
  apiBaseUrl: 'https://your-api-server.com'
});

function App() {
  return (
    <MeetingMobile 
      onLeave={() => console.log('离开会议')}
    />
  );
}
```

## 配置说明

### API配置

组件需要配置API服务器地址：

```javascript
initMeetingMobile({
  apiBaseUrl: 'https://your-api-server.com',
  debug: true // 开启调试模式
});
```

### URL参数

组件通过URL参数获取会议信息：

- `meetId`: 会议ID
- `token`: 认证令牌
- `isAudio`: 是否开启音频 (true/false)
- `isVideo`: 是否开启视频 (true/false)
- `memJoinType`: 成员类型 (speaker/audience)

示例URL：
```
/meeting?meetId=123&token=abc&isAudio=true&isVideo=true&memJoinType=speaker
```

### 全局对象

如果你的环境中有以下全局对象，组件会使用它们：

```javascript
// 会议管理对象
window.scooper = {
  meetManager: { meetsObj: {...} },
  meetDispatchManager: { accountDetail: {...} },
  meetvideoController: {...}
};

// 硬件控制对象
window.newShandleUtil = {
  getSHandle: () => ({...}),
  enableCamera: (enabled) => {},
  audioClose: (muted) => {},
  // ...其他方法
};

// 消息订阅对象
window.newCometd = {
  subscribe: (channel, callback) => subscriptionId,
  unsubscribe: (subscriptionId) => {}
};
```

如果没有这些对象，组件会使用模拟版本。

## 功能说明

### 音视频控制

- **麦克风开关**: 点击底部麦克风按钮
- **摄像头开关**: 点击底部摄像头按钮
- **扬声器切换**: 点击底部扬声器按钮（听筒/扬声器）

### 参会人员

- **查看参会人**: 点击底部"参会人"按钮
- **人员状态**: 显示在线/离线状态
- **音频状态**: 显示发言/禁言状态

### 视频显示

- **多屏显示**: 支持1/4/9/16分屏
- **全屏播放**: 点击视频窗口可全屏
- **自动布局**: 根据参会人数自动调整布局

## 样式定制

### CSS变量

```css
:root {
  --vh: 1vh; /* 视口高度单位 */
}
```

### 主要样式类

```css
.meetingMobile { /* 主容器 */ }
.meetingMobile-top { /* 顶部标题栏 */ }
.meetingMobile-bottom { /* 底部控制栏 */ }
.meeting-play { /* 视频播放区域 */ }
```

### 自定义样式

```css
/* 修改主题色 */
.meetingMobile-bottom div i.on {
  background-color: #your-color;
}

/* 修改背景 */
.meetingMobile-top {
  background-color: rgba(your-color);
}
```

## 事件处理

### 离开会议

```jsx
<MeetingMobile 
  onLeave={() => {
    // 清理数据
    sessionStorage.clear();
    // 跳转页面
    window.location.href = '/home';
  }}
/>
```

### 错误处理

```jsx
<MeetingMobile 
  onError={(error) => {
    console.error('会议错误:', error);
    // 显示错误信息
  }}
/>
```

## 移动端适配

### 视口设置

```html
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
```

### 高度适配

组件会自动处理移动端浏览器地址栏的高度变化：

```javascript
// 自动设置CSS变量
const vh = window.innerHeight * 0.01;
document.documentElement.style.setProperty('--vh', `${vh}px`);
```

### 权限处理

移动端需要用户手动授权摄像头和麦克风：

```javascript
// 检查权限
navigator.mediaDevices.getUserMedia({ video: true, audio: true })
  .then(stream => {
    // 权限获取成功
  })
  .catch(error => {
    // 权限被拒绝
  });
```

## 常见问题

### 1. 视频无法显示

- 检查HTTPS环境
- 确认摄像头权限
- 查看控制台错误

### 2. 音频无法工作

- 检查麦克风权限
- 确认设备支持
- 检查浏览器兼容性

### 3. 样式显示异常

- 确认Less文件正确加载
- 检查CSS变量支持
- 验证响应式设计

### 4. API请求失败

- 检查网络连接
- 确认API地址正确
- 查看CORS配置

## 调试技巧

### 开启调试模式

```javascript
initMeetingMobile({
  debug: true
});
```

### 查看Redux状态

```javascript
// 在浏览器控制台
console.log(store.getState());
```

### 模拟数据

```javascript
// 设置模拟会议数据
window.MEETING_MOBILE_CONFIG = {
  mockData: true,
  meetId: 'test123'
};
```

## 性能优化

### 减少重渲染

- 使用React.memo包装组件
- 优化Redux状态结构
- 避免不必要的状态更新

### 资源优化

- 按需加载组件
- 压缩图片资源
- 使用CDN加载依赖

### 内存管理

- 及时清理定时器
- 取消网络请求
- 移除事件监听器
