/*
 * @File: 视频终端 reducer
 * @Author: liulian
 * @Date: 2020-10-29 10:08:20
 * @version: V0.0.0.1
 * @LastEditTime: 2025-01-21 11:22:03
 */
const MONITOR_TREE_DATA = 'MONITOR_TREE_DATA'
const MONITORSELECTKEYS = 'MONITORSELECTKEYS'
const MONITOREXPANDKEYS = 'MONITOREXPANDKEYS'
const BARDIRECTION = 'BARDIRECTION'
const VIDEOISLOOP = 'VIDEOISLOOP'
const SCREENLIST = 'SCREENLIST'
const MEETSCREENINFO = 'MEETSCREENINFO'
const CURSELECTSCREENVAL = 'CURSELECTSCREENVAL'
const TEMPSCREEN = 'TEMPSCREEN'
const TEMPSCREENLIST = 'TEMPSCREENLIST'
const SCREENSELECTMEM = 'SCREENSELECTMEM'
const ISMULTISCREEN = 'ISMULTISCREEN'
const JOINTYPE = 'JOINTYPE'


const initState = {
    isMultiScreen: false,  //是否是多屏
    monitorTreeData: [],  //视频监控目录树
    monitorSelectkeys:[], //选中的树节点
    monitorExpandKeys:[], //展开的树节点
    barDirection: true,   //bar朝向  true:向右初始态   false:向左
    videoIsLoop: false,  //是否在轮询视频中
    curSelectScreenVal:'screen-1',  //当前分屏模式 screen-1
    tempScreen:1,
    meetScreenInfo:{},   //当前分屏信息 分屏模式 混屏方式
    screenSelectMem:{},  //分屏左侧会议成员选中的
    screenList: [
        { id: 1, screen: 1, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false},
        { id: 2, screen: 2, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 3, screen: 3, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 4, screen: 4, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 5, screen: 5, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 6, screen: 6, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 7, screen: 7, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 8, screen: 8, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 9, screen: 9, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 10, screen: 10, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 11, screen: 11, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 12, screen: 12, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 13, screen: 13, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 14, screen: 14, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 15, screen: 15, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false },
        { id: 16, screen: 16, videoName: '请选择视频源', meetId: '', hasVideo: false, devId: '', selected: '',isMonitor:false }
    ],
    tempScreenList:[],
    joinType:1  //1 主持人 2 普通成员 3 听众
};

export function screenManageReducer(state = initState, action) {
    switch (action.type) {
        case MONITOR_TREE_DATA: return { ...state, monitorTreeData: action.data };
        case MONITORSELECTKEYS:return {...state,monitorSelectkeys:action.data};
        case MONITOREXPANDKEYS:return {...state,monitorExpandKeys:action.data};
        case BARDIRECTION: return { ...state, barDirection: action.data };
        case VIDEOISLOOP: return { ...state, videoIsLoop: action.data };
        case SCREENLIST: return { ...state, screenList: action.data };
        case MEETSCREENINFO:return {...state,meetScreenInfo:action.data};
        case CURSELECTSCREENVAL: return {...state,curSelectScreenVal:action.data};
        case TEMPSCREEN:return {...state,tempScreen:action.data};
        case TEMPSCREENLIST:return {...state,tempScreenList:action.data};
        case SCREENSELECTMEM:return {...state,screenSelectMem:action.data};
        case ISMULTISCREEN:return {...state,isMultiScreen:action.data};
        case JOINTYPE:return {...state,joinType:action.data};
        default: return state;
    }
}

/**
 * 设置监控目录树的数据
 */
export function setMonitorTreeData(data) {
    return { type: MONITOR_TREE_DATA, data: data }
}
/**
 * 设置选中的树节点信息
 */
export function setMonitorSelectKeys(data){
    return {type:MONITORSELECTKEYS,data:data}
}
/**
 * 设置展开树节点的信息
 */
export function setMonitorExpandKeys(data){
    return {type:MONITOREXPANDKEYS,data:data}
}
/**
 * 设置bar的朝向
 */
export function setBarDirection(data) {
    return { type: BARDIRECTION, data: data }
}
/**
 * 设置当前是否在轮询视频状态中
 */
export function setVideoIsLoop(data) {
    return { type: VIDEOISLOOP, data: data }
}
/**
 * 更新分屏管理列表
 */
export function setScreenList(data) {
    return { type: SCREENLIST, data: data }
}
export function setTempScreenList(data){
    return {type:TEMPSCREENLIST,data:data}
}
/**
 * 设置当前分屏信息
 */
export function setMeetScreenInfo(data){
    return {type:MEETSCREENINFO,data:data}
}
/**
 * 设置当前选中的分屏模式
 */
export function setCurSelectScreen(data){
    return {type:CURSELECTSCREENVAL,data:data}
}
export function setTempScreen(data){
    return {type:TEMPSCREEN,data:data}
}
/**
 * 设置分屏左侧会议成员选中的
 */
export function setScreenSelectMem(data){
    return {type:SCREENSELECTMEM,data:data}
}
/**
 * 设置是否是多屏
 */
export function setIsMultiScreen(data){
    return {type:ISMULTISCREEN,data:data}
}
/**
 * 设置入会类型
 */
export function setJoinType(data){
    return {type:JOINTYPE,data:data}
}
