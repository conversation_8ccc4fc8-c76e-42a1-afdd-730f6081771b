/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 视频呼出
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext, useEffect, useRef, useState} from "react";
import style from '../style/sass/video.module.scss';
import {MainContext} from "../context";
import {message} from "antd";

function VideoIng () {

	const { nonOwnerInformation, allMemberInfo } = useContext(MainContext);

	const [isMute, setIsMute] = useState(false);
	const [isSpeaker, setIsSpeaker] = useState(true);
	const [isCloseCamera, setIsCloseCamera] = useState(true);
	const [isFrontCamera, setIsFrontCamera] = useState(true);
	const [duringTime, setDuringTime] = useState('');

	const duringTimeRef = useRef(0);


	/**
	 * @desc 获取本地视频
	 */
	useEffect(() => {
		let shandle = window.shandleUtil.getSHandle();
		shandle.setVideoContainer({
			localVideo: document.getElementById('other-video-content'),
			remoteVideo: document.getElementById('my-video-content')
		});
		window.shandleUtil.updateVoiceSpeaker(true);
		setTimeout(() => {
			shandle.deviceControl.updateFrontCamera(true);
		}, 500)
	}, []);

	/**
	 * @desc 定时器
	 */
	useEffect(() => {
		duringTimeRef.current = 1;
		setDuringTime('00:01');
		setInterval(() => {
			duringTimeRef.current = duringTimeRef.current + 1;
			let hours = Math.floor(duringTimeRef.current / 3600);
			let minutes =  Math.floor((duringTimeRef.current - (hours * 3600)) / 60);
			let seconds = duringTimeRef.current % 60;
			if(!hours) {
				setDuringTime(`${String(minutes).padStart(2, '0')} : ${String(seconds).padStart(2, '0')}`);
			}else {
				setDuringTime(`${String(hours).padStart(2, '0')} : ${String(minutes).padStart(2, '0')} : ${String(seconds).padStart(2, '0')}`);
			}
		}, 1000)
	}, []);

	/**
	 * @desc 挂断电话
	 */
	function hangupCall() {
		window.shandleUtil.hungUp();
	}

	/**
	 * @desc 切换是否静音
	 */
	function setDeviceMute() {
		window.shandleUtil.audioClose(!isMute);
		setIsMute(!isMute);
	}

	/**
	 * @desc 切换扬声器
	 */
	function changeSpeaker() {
		if (!window.shandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
			message.error("当前设备不支持切换");
			return
		}
		window.shandleUtil.updateVoiceSpeaker(!isSpeaker);
		setIsSpeaker(!isSpeaker);
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice().then(() => {
		// }).catch(error => {
		// 	message.error(error);
		// });
	}

	/**
	 * @desc 关闭摄像头
	 */
	function closeCamera() {
		window.shandleUtil.enableCamera(!isCloseCamera);
		setIsCloseCamera(!isCloseCamera);
	}

	/**
	 * @desc 转换前后摄像头
	 */
	function turnFrontCamera() {
		let shandle = window.shandleUtil.getSHandle();
		// shandle.deviceControl.updateFrontCamera(!isFrontCamera);
		console.log(shandle);
		shandle.deviceControl.updateFrontCamera(!isFrontCamera);
		window.shandleUtil.audioClose(isMute);
		setIsFrontCamera(!isFrontCamera);
	}

	return(
		<div className={style.videoCall}>
			<div className={style.myVideoContent} id='my-video-content'/>
			<div className={style.otherVideoContent} id='other-video-content'></div>
			<div className={style.videoHeader}>
				<span className={style.memName}>{nonOwnerInformation.memName || allMemberInfo.current[nonOwnerInformation.telNumber]?.memName || nonOwnerInformation.telNumber}</span>
				<span className={style.defaultTip}>{duringTime}</span>
			</div>
			<div className={style.actionsContent}>
				<span className={`${style.iconMute} ${isMute? style.isMute: ''}`} onClick={() => setDeviceMute()}></span>
				<span className={`${style.iconSpeaker} ${!isSpeaker? style.isSpeaker: ''}`} onClick={() => changeSpeaker()}></span>
				<span className={`${style.iconCloseCamera} ${!isCloseCamera? style.isCloseCamera: ''}`} onClick={() => closeCamera()}></span>
				<span className={`${style.iconTurnCamera} ${!isFrontCamera? style.isFrontCamera: ''}`} onClick={() => turnFrontCamera()}></span>
			</div>
			<div className={style.hungup}>
				<span className={style.iconHungUp} onClick={() => hangupCall()}></span>
			</div>
		</div>
	)
}

export default VideoIng;