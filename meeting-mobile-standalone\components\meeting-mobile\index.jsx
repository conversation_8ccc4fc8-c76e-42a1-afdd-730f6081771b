/*
 * @Author: yangyu<PERSON> <EMAIL>
 * @Date: 2023-05-05 16:38:26
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-11-28 10:13:47
 * @FilePath: meeting-mobile-standalone\components\meeting-mobile\index.jsx
 * @Description: 独立的移动端会议组件
 */

import React, { useEffect, useState, useRef } from 'react';
import {message } from 'antd';
import { Button, Modal } from 'antd';

import { connect } from 'react-redux';
import LeftVideo from '../left-video';
import '../../styles/meetingMobile.less'
import SwipeableViews from 'react-swipeable-views';
import {getUrlParmse,getMeAudioTel,getUserMediaType,loadMyMeetList,fillMyMeetList,initMyMeeListRecord,decryptByDES,findMainFrame} from '../../utils/method'
import { curMainTel, meetLengthOptions } from '../../config/constants';
import { setCurMeetDetail,setMyMeetList } from '../../reducers/create-meet-reducer';
import { setVideoIsLoop, setCurSelectScreen, setMeetScreenInfo, } from '../../reducers/screen-manage-reducer';
import { setMemAudience,setIntoMeetDetail, setCurOpDetail, setCanSelectMediaInfo,setPlayExpandFlag,setIsFirstUpdateMix } from '../../reducers/meet-detail-reducer';
import { setAllConfig,setIsVisitor,setIsLeaveMeeting } from '../../reducers/loading-reducer';
import MeetVideoManage from '../../utils/videoManager';
import dispatchManager from "../../utils/dispatch-manager";
import meetManager from '../../utils/meet-manager';
import { apis } from '../../utils/api';
import BottomModal from './mobileBottomModal.jsx'

const styles = {
  slide: {
    // height: '52rem',
    color: '#fff',
    position: 'relative',
    background: '#252525',
  },
  slide1: {
    
  },
  slide2: {
    display:'none'
  },
  slide3: {
  },
  slide4: {
  },
};

const { confirm } = Modal;

const MeetingMobile = (props) => {
  const [useSpeaker, setUseSpeaker] = useState(true);
  const [isActive, setIsActive] = useState(0);
  const [fullScreen, setFullScreen] = useState(false);
  const [metting,setMetting] = useState({
    isAudio: true,
    isVideo: true,
    isListion: true,
    isPeopleList: false,  // 人员列表的弹窗
    isModalOpen: false, //离开的弹窗
    meetName:''
  })

  // 使用 useRef 存储订阅 ID
  const subscriptionIdRef = useRef(null);

  useEffect(() => {
    const setHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    // 在窗口大小变化时重新计算高度
    window.addEventListener('resize', setHeight);
    setHeight();
    const {meetId,token,isAudio,isVideo,linkName,memJoinType} = getUrlParmse()
    if(token){
      sessionStorage.setItem("meetWebToken", token);
      sessionStorage.setItem('curParam',JSON.stringify({meetId}))
    }
    if(memJoinType==='speaker'){
      // props.setIsVisitor(2) // 设置游客入会
      props.setIsVisitor(2) // 设置正常入会
    }else if(memJoinType==='audience'){
      // props.setIsVisitor(3) // 设置听众入会
      props.setIsVisitor(2) // 设置正常入会
    }
     setMetting((metting) => {
      const newMetting ={
        ...metting,
        isVideo:isVideo==='true',
        isAudio:isAudio==='true',
       }
       loadMeetInfo(meetId,newMetting);
       return newMetting
     })
    getMeetScreen(meetId);   //得到当前分屏信息
    loadAllConfig();   //获取相关配置
    setMetting({
      ...metting,
      isVideo:isVideo==='true',
      isAudio:isAudio==='true',
     })
  },[]);
  
  useEffect(()=>{
    // 首次渲染页面
    if (props.createVideoUI) {
      const videoList = document.querySelectorAll('#video-main-web-rtc .screen')
      const videoBox = document.querySelectorAll('#video-main-web-rtc .video-box')
      for (let index = 0; index < videoBox.length; index++) {
        videoBox[index].setAttribute('playsinline', '');
        videoBox[index].setAttribute('webkit-playsinline', '');
        videoBox[index].setAttribute('x5-video-player-type', 'h5-page');
      }
      const videoLists = document.querySelectorAll('.screen');
      console.log('videoLists: ', videoLists);
      videoLists.forEach((element,index) => {
        element.addEventListener('click', (e) => {
          if (e.isTrusted) {
            fullScreenEvent(element)
          }
        });
      });
      const {isVideo} = getUrlParmse()
      if (isVideo==='false') {
        if (window.newShandleUtil && window.newShandleUtil.getSHandle()) {
            window.newShandleUtil.enableCamera(false)
        }else if(window.scooper && !window.scooper.util.isTopFrame()){
            window.shandleUtil = findMainFrame().shandleUtil;
            window.shandleUtil.enableCamera(false)
        }
       }
    }
  },[props.createVideoUI])

  useEffect(()=>{
    console.log('props.intoMeetDetail: ', props.intoMeetDetail);
  },[props.intoMeetDetail])

  useEffect(()=>{
    return ()=>{
      // 在组件卸载时安全地清理订阅
      if (subscriptionIdRef.current && window.newCometd) {
        try {
          window.newCometd.unsubscribe(subscriptionIdRef.current);
          console.log('已取消会议成员信息订阅:', subscriptionIdRef.current);
        } catch (error) {
          console.error('取消会议成员信息订阅失败:', error);
        } finally {
          subscriptionIdRef.current = null;
        }
      }
    }
  },[])


  const showConfirm = ()=> {
    confirm({
      title: '是否离开会议?',
      // content: 'Some descriptions',
      onOk() {
        const {meetId} = getUrlParmse()
        props.setIsLeaveMeeting(true)
        try {
          // window.scooper.meetManager.meetsObj.kickMember(meetId, sessionStorage.getItem('meetWebMaintel'));
          // 添加删除账号接口
          if (window.newShandleUtil) {
            window.newShandleUtil.deregister();
          }
        } catch (error) {
          
        }
        if (props.history) {
          props.history.push('/videoPartingClose')
        } else if (props.onLeave) {
          props.onLeave();
        }
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }
  
  const loadAllConfig = async () => {
    try {
      const el = await apis.disp.kiv();
      const data = await apis.disp.config(el);
      if (data) {
        const res = decryptByDES(data,el.key)
        props.setAllConfig(JSON.parse(res))
      }
    } catch (error) {
      console.error('加载配置失败:', error);
    }
  }

  /**
     * 根据meetId查询会场详情
     */
const loadMeetInfo = async (meetId,newMetting) => {
  if (meetId) {
      let data = await apis.conference.getMeetAdd({ meetId: meetId });
      props.setIsLeaveMeeting(false)
      if (data && data.meetId) {
          if(!data.meetMixScreen){
              // message.error("当前会议不支持混屏服务！");
          }
          props.setMemAudience(data.meetMuted)
          setMetting({
            ...newMetting,
            meetName:data.meetName
          })
          let curScreen = 'screen-1';
          if (data.videoScreen && data.videoScreen != -1) {
              curScreen = 'screen-' + data.videoScreen
          }
            data.meetMem = data.members||[];
            console.log("合并会议属性前:setIntoMeetDetail", data)
            props.setIntoMeetDetail({...data});
            props.setCurMeetDetail({...data});
            fillMyMeetList([...props.myMeetList,...[data]])
            getDispOperDetail(data);   //获取操作员详情
            setTimeout(() => {
                initMyMeeListRecord()
            })
            setTimeout(() => {
              props.setMyMeetList([data])
              newCometdSubscribe(meetId); // 订阅会议成员变更事件
            }, 4000)
            // props.setMyMeetList([data])
          updateInfo(curScreen);
      }
  }
}

 /**
     * 订阅会议成员信息变更
     * @param {string} meetId 会议ID
     * @returns {string} 订阅ID，用于后续取消订阅
     */
 const newCometdSubscribe = (meetId) => {
   if (!window.newCometd || !meetId) {
       console.warn('CometD实例或会议ID无效，无法订阅会议成员信息');
       return null;
   }

   try {
       // 存储订阅ID到 ref 中，而不是 this
       subscriptionIdRef.current = window.newCometd.subscribe(
           `/meetId/${meetId}/meet/memInfo`,
           handleMemberInfoUpdate
       );

       console.log(`已订阅会议成员信息: /meetId/${meetId}/meet/memInfo`, subscriptionIdRef.current);
       return subscriptionIdRef.current;
   } catch (error) {
       console.error('订阅会议成员信息失败:', error);
       return null;
   }
 }

/**
* 处理会议成员信息更新
* @param {Object} data 成员信息数据
*/
const handleMemberInfoUpdate = (info) => {
  let data = info.data;
  data.tel = data.callNumber;
  console.log("新会议成员信息通知",data)
  if (!data || !data.tel) {
      console.warn('收到无效的成员信息数据');
      return;
  }

  try {
      // 获取当前会议详情的副本
      let {intoMeetDetail} = props;
      const memberList = [...(intoMeetDetail.meetMem || [])];

      // 查找是否存在相同电话的成员
      const existingMemberIndex = memberList.findIndex(member =>
          member.tel === data.tel
      );

      if (existingMemberIndex >= 0) {

          const statusMap = {
              "ring": "calling",
              "join": "meeting",
              "quit": "outMeet",
          }
          const parseData = {
              memberLevel: data.memberLevel,
              memName: data.name?data.name:memberList[existingMemberIndex].memName,
              tel: data.tel,
              type: data.mediaType,
              status: statusMap[data.meetStatus],
              speakLevel: data.speakLevel,
              meetId: data.meetId,
          }

          // 更新现有成员信息
          const updatedMember = {
              ...memberList[existingMemberIndex],
              ...parseData // 合并新数据
          };

          // 创建新数组，替换对应成员
          const updatedMembers = [
              ...memberList.slice(0, existingMemberIndex),
              updatedMember,
              ...memberList.slice(existingMemberIndex + 1)
          ];

          console.log("更新会议成员信息:setIntoMeetDetail", intoMeetDetail)
          // 更新状态
           props.setIntoMeetDetail({
              ...intoMeetDetail,
              meetMem: updatedMembers
          });

          console.log(`更新会议成员信息,`,updatedMembers);
      } else {
          // 添加新成员
          console.log("添加新成员:setIntoMeetDetail", intoMeetDetail)
          props.setIntoMeetDetail({
              ...intoMeetDetail,
              meetMem: [...memberList, data]
          });

          console.log(`添加会议成员: ${data.tel}`);
      }
  } catch (error) {
      console.error('处理会议成员更新失败:', error);
  }
}

  /**
     * 得到当前分屏信息
     */
  const getMeetScreen = async (meetId) => {
    if (meetId) {
        let data = await apis.conference.getMeetScreenType({ meetId });
        if (data && data.code == 0) {
            let param = {
                meetId: meetId,
                meetScreenType: data.data.meetScreenType,
                member: data.data.member
            }
            props.setMeetScreenInfo(param);
            props.setIsFirstUpdateMix(false)
            const datas = await apis.conference.setMeetScreenType(param);
            console.log(datas);
        }
    }
  }

  /**
     * 更新当前的分屏信息
     */
  const updateInfo = (curScreen) => {
    let { curSelectScreenVal } = props;

    if (curSelectScreenVal != curScreen) {
        props.setCurSelectScreen(curScreen);
    }
  }

  /**
   * 获取调度操作员详情
   * @param {*} accId 账号ID
   */
  const getDispOperDetail = async (datas) => {
    let {intoMeetDetail} = props;
    let accId = JSON.parse(sessionStorage.getItem('sc-auth'))?.auth?.accountId||sessionStorage.getItem('meetWebAccId');
    let data = await apis.core.getDispOperDetail({ accId });
    if (data) {
        let shareTel = data.mainTel || '';
        props.setCurOpDetail(data);
        let audioNum = '';
        if(datas.audioInto == 1){
            audioNum = data.pcTel;
        }else if(datas.audioInto == 2){
            audioNum = data.digConferenceTel
        }else if(datas.audioInto == 3){
            audioNum = data.mainTel || curMainTel()
        }
        let videoNum = '';
        if(datas.videoInto == 1){
            videoNum = data.pcTel;
        }else if(datas.videoInto == 2){
            videoNum = data.ipcTel
        }else if(datas.videoInto == 3){
            videoNum = data.desktopTerminalTel || curMainTel()
        }
        datas.audioNum = audioNum;
        datas.videoNum = videoNum;
        datas.videoScreen = 4;
        console.log("更新调度操作员详情:setIntoMeetDetail", datas)
        props.setIntoMeetDetail({...datas})
    }
  }

  /**
   * 点击事件
   * @param {*} accId 账号ID
   */
  const handleClick = ()=>{
    return{
      audio: async()=>{
        let {intoMeetDetail} = props;
        const myTel = curMainTel()
        if (window.scooper && window.scooper.meetManager) {
          window.scooper.meetManager.meetsObj.changeMemberLevel(intoMeetDetail.meetId, myTel, metting.isAudio?'audience':'speak');
          window.scooper.meetManager.meetsObj.changeMemberLevel(intoMeetDetail.meetId, myTel+'desk', metting.isAudio?'audience':'speak');
        }
        if (window.newShandleUtil) {
          window.newShandleUtil.audioClose(metting.isAudio);
        }
       setMetting({
        ...metting,
        isAudio:!metting.isAudio
       })
      },
      video:()=>{
        if (window.newShandleUtil && window.newShandleUtil.getSHandle()) {
            window.newShandleUtil.enableCamera(!metting.isVideo)
        }else if(window.scooper && !window.scooper.util.isTopFrame()){
            window.shandleUtil = findMainFrame().shandleUtil;
            window.shandleUtil.enableCamera(!metting.isVideo)
        }
        setMetting({
          ...metting,
          isVideo:!metting.isVideo
         })
      },
      desktop:()=>{
        if (window.newShandleUtil && !window.newShandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
          message.error("当前设备不支持切换");
          return
        }
        if (window.newShandleUtil) {
          window.newShandleUtil.updateVoiceSpeaker(!useSpeaker)
        }
        setUseSpeaker(!useSpeaker);
      },
      meetPeople:() =>{
        setMetting({
          ...metting,
          isPeopleList:true
         })
      }
    }

  }

  const handleChangeIndex = (newIndex,indexLatest)=>{
    console.log(newIndex,indexLatest);
    setIsActive(newIndex)
  }

  const handleClickMoveIndex = (index)=>{
    return()=>{
      setIsActive(index)
    }
  }

  const closeBottom = ()=>{
    setMetting({
      ...metting,
      isPeopleList:false
     })
  }

 const fullScreenEvent= (videoElement)=> {
  if (videoElement.classList.contains('fullScreen')) {
    videoElement.classList.remove('fullScreen');
    console.log('元素不具有 myClass 类名');
    videoElement.style.position = 'relative'
    videoElement.style.width = '50%'
    videoElement.style.height = '49.7%'
    setFullScreen(false)
    videoElement.parentNode.querySelectorAll('.screen').forEach((item,index) => {
      if (index<4) {
        item.style.display = 'block'
      }
    });
  } else {
    videoElement.classList.add('fullScreen');
    videoElement.style.position = 'fixed'
    videoElement.style.width = '99.4%'
    videoElement.style.height = '75%'
    setFullScreen(true)
    videoElement.parentNode.querySelectorAll('.screen').forEach((item,index) => {
      if (index<4) {
        item.style.display = 'none'
      }
    });
    videoElement.style.display = 'block'
  }
}

  const meetId = getUrlParmse()?.meetId;
  const activeMeets = props.myMeetList.filter((item) =>item.meetId===meetId)[0];
  const activeMeet = props.intoMeetDetail || activeMeets;

  // "inMeet",
  const meetMemings = activeMeet?.meetMem?.filter((item) => ["meeting","inMeet"].includes(item.status))
  const peopleNumber = '('+(meetMemings?.length||0)+')'

  return (
    <div className='meetingMobile'>
      <div className='meetingMobile-top'>
          <div className='meetingMobile-textBox'>
            <div className='meetingMobile-name'>{metting.meetName+peopleNumber}</div>
            <div className='meetingMobile-time'>{activeMeet?.curMeetLength}</div>
          </div>
          <div className='meetingMobile-right-icon' onClick={showConfirm}>
          </div>
      </div>
      <div className='meeting-play meetingMobile-moveVideo'>
        <LeftVideo isShowColl = {fullScreen}/>
      </div>

      <MeetVideoManage></MeetVideoManage>
      <div className='meetingMobile-bottom'>
        <div onClick={handleClick().audio}>
        <i className={metting.isAudio?'on':'off'}></i>
        <p>麦克风</p>
        </div>
        <div onClick={handleClick().video}>
          <i className={metting.isVideo?'on':'off'}></i>
          <p>摄像头</p>
        </div>
        <div onClick={handleClick().desktop}>
          <i className={!useSpeaker?'on':'off'}></i>
          <p>{!useSpeaker?'听筒':'扬声器'}</p>
        </div>
        <div onClick={handleClick().meetPeople}>
          <i className='on'></i>
          <p>参会人</p>
        </div>
      </div>
      <BottomModal className='MeetingMembers' visible={metting.isPeopleList} onClose={closeBottom} data={activeMeet?.meetMem}></BottomModal>
    </div>
  )};

let meetingMobiles = connect(
  state => state.meetDetail,
  { setMemAudience,setIntoMeetDetail, setCurOpDetail, setCanSelectMediaInfo,setPlayExpandFlag,setIsFirstUpdateMix }
)(MeetingMobile)

meetingMobiles = connect(
  state => state.screenManage,
  { setVideoIsLoop, setCurSelectScreen, setMeetScreenInfo }
)(meetingMobiles)

meetingMobiles = connect(
  state => state.createMeet,
  { setCurMeetDetail,setMyMeetList }
)(meetingMobiles)

meetingMobiles = connect(
  state => state.createMeet
)(meetingMobiles)

meetingMobiles = connect(
  state => state.loading,
  {setAllConfig,setIsVisitor,setIsLeaveMeeting}
)(meetingMobiles)

export default meetingMobiles;
