.call-out {
	width: 100%;
	height: 100%;
	.call-out-header {
		position: absolute;
		top: 10%;
		width: 100%;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		flex-direction: column;
		.mem-name {
			font-size: 32px;
		}
		.default-tip {
			font-size: 18px;
			margin-top: 12px;
		}
	}
	.call-out-action-content {
		position: absolute;
		bottom: 10%;
		height: 40%;
		width: 100%;
		display: flex;
		align-items: flex-end;
		justify-content: space-around;
		.icon-mute {
			width: 60px;
			height: 60px;
			background: url("../images/icon_mute.png") no-repeat;
			background-size: 100% 100%;
			&.is-mute {
				background: url("../images/icon_mute_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}
		.icon-hung-up {
			width: 100px;
			height: 100px;
			background: url("../images/icon_hung_up.png") no-repeat;
			background-size: 100% 100%;
		}
		.icon-speaker {
			width: 60px;
			height: 60px;
			background: url("../images/icon_speaker.png") no-repeat;
			background-size: 100% 100%;
			&.is-speaker {
				background: url("../images/icon_speaker_sel.png") no-repeat;
				background-size: 100% 100%;
			}
		}
	}
}