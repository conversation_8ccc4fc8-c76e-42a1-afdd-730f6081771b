/**
 * @Create: 周颖仁
 * @Date: 2023/11/1
 * @desc: 会议
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/1
 */

import React, { useState, useEffect, useContext } from "react";
import { useNavigate } from "react-router-dom";
import style from './style/sass/index.module.scss';
import { List, Empty } from "antd-mobile";
import {
  VideoCameraOutlined,
  PlusOutlined,
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { MainContext } from "../context";
import JoinMeeting from "./join-meeting";
import StartMeeting from "./start-meeting";
import { dispatch_web_api } from "../../../util/api";
function Meeting() {
  const navigate = useNavigate();
  const { showType, setShowType } = useContext(MainContext);
  const [meetingList, setMeetingList] = useState([]);
  const [ongoingMeetings, setOngoingMeetings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [ongoingLoading, setOngoingLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState('main'); // 'main' | 'join' | 'start'

  // 获取会议列表
  useEffect(() => {
    // 获取历史会议列表
    const fetchHistoryMeetings = () => {
      setLoading(true);
      dispatch_web_api.listMeetsHistory({
        pageNum: 1,
        pageSize: 10
      }).then(data => {
        console.log('历史会议列表:', data);
        setMeetingList(data.list || []);
        setLoading(false);
      }).catch(error => {
        console.error('获取历史会议失败:', error);
        setLoading(false);
      });
    };

    // 获取正在进行中的会议列表
    const fetchOngoingMeetings = () => {
      setOngoingLoading(true);
      dispatch_web_api.listMyMeets({
        pageNum: 1,
        pageSize: 20
      }).then(data => {
        console.log('正在进行中的会议列表:', data);
        setOngoingMeetings(data.data || []);
        setOngoingLoading(false);
      }).catch(error => {
        console.error('获取正在进行中的会议失败:', error);
        setOngoingMeetings([]);
        setOngoingLoading(false);
      });
    };

    // 同时获取两个列表
    fetchHistoryMeetings();
    fetchOngoingMeetings();
  }, []);

  // 刷新正在进行中的会议列表
  const refreshOngoingMeetings = () => {
    setOngoingLoading(true);
    dispatch_web_api.listMyMeets({
      pageNum: 1,
      pageSize: 20
    }).then(data => {
      console.log('刷新正在进行中的会议列表:', data);
      setOngoingMeetings(data.data || []);
      setOngoingLoading(false);
    }).catch(error => {
      console.error('刷新正在进行中的会议失败:', error);
      setOngoingLoading(false);
    });
  };

  // 发起会议
  const handleStartMeeting = () => {
    console.log('发起会议');
    setCurrentPage('start');
    setShowType('full');
  };

  // 加入会议
  const handleJoinMeeting = () => {
    console.log('加入会议');
    setCurrentPage('join');
    setShowType('full');
  };

  // 会议项点击 - 跳转到详情页面
  const handleMeetingClick = (meeting) => {
    console.log('点击会议:', meeting);
    // 跳转到会议详情页面，并传递会议数据
    navigate('/main/meeting/detail', {
      state: {
        meetingData: meeting
      }
    });
  };

  // 正在进行中的会议点击 - 直接加入会议
  const handleOngoingMeetingClick = async(meeting) => {
    console.log('点击正在进行中的会议:', meeting);

    // 获取用户信息
    const ownerInformation = JSON.parse(localStorage.getItem('scooperOwnerInformation') || '{}');

    // 构造会议数据
    const meetingInfo = {
      ...meeting,
      isHost: meeting.ctrlTel === ownerInformation.mainTel,
      userRole: 'participant'
    };

    // 保存会议数据到 sessionStorage
    sessionStorage.setItem('curParam', JSON.stringify(meetingInfo));
    // 直接跳转到会议室
    navigate('/main/meeting/room', {
      state: {
        meetingData: meetingInfo,
        isHost: meetingInfo.isHost
      }
    });
     const joinRes = await dispatch_web_api.joinVideoMember({
      id: meeting.meetId,
      tel: ownerInformation.mainTel,
      level: 'speak',
      businessId: '',
      autoAnswer: '',
      meetAccess: meeting.meetAccess
    });
    console.log('加入会议结果:', joinRes);


    
  };

  // 渲染页面内容
  const renderPageContent = () => {
    switch (currentPage) {
      case 'join':
        return <JoinMeeting onBack={() => {
          setCurrentPage('main');
          setShowType('normal');
        }} />;
      case 'start':
        return <StartMeeting onBack={() => {
          setCurrentPage('main');
          setShowType('normal');
        }} />;
      case 'main':
      default:
        return renderMainContent();
    }
  };

  // 渲染主页面内容
  const renderMainContent = () => (
    <>
      {/* 功能按钮区域 */}
      <div className={style['action-buttons']}>
        <div className={style['button-row']}>
          <div className={style['action-button']} onClick={handleStartMeeting}>
            <div className={style['button-icon']}>
              <VideoCameraOutlined />
            </div>
            <span className={style['button-text']}>发起会议</span>
          </div>
          <div className={style['action-button']} onClick={handleJoinMeeting}>
            <div className={style['button-icon']}>
              <PlusOutlined />
            </div>
            <span className={style['button-text']}>加入会议</span>
          </div>
          {/* <div className={style['action-button']} onClick={handleScheduleMeeting}>
            <div className={style['button-icon']}>
              <CalendarOutlined />
            </div>
            <span className={style['button-text']}>预约会议</span>
          </div>
          <div className={style['action-button']} onClick={handleRecordFiles}>
            <div className={style['button-icon']}>
              <FileTextOutlined />
            </div>
            <span className={style['button-text']}>录制文件</span>
          </div> */}
        </div>
      </div>

      {/* 正在进行中的会议列表 */}
      {ongoingMeetings.length > 0 && (
        <div className={style['meeting-list-container']}>
          <div className={style['list-header']}>
            <div>
              <span>正在进行中</span>
              <span className={style['meeting-count']}>({ongoingMeetings.length})</span>
            </div>
            <div
              className={style['refresh-button']}
              onClick={refreshOngoingMeetings}
              style={{ opacity: ongoingLoading ? 0.5 : 1 }}
            >
            🔄
            </div>
          </div>

          <div className={style['meeting-list']}>
            <List>
              {ongoingMeetings.map((meeting, index) => {
                // 格式化时间显示
                const formatTime = (timeBegin) => {
                  if (!timeBegin) return '';
                  const start = timeBegin.substring(5, 16); // 提取 MM-dd HH:mm
                  return start;
                };

                // 获取参与人数
                const getParticipantCount = (members) => {
                  if (Array.isArray(members)) {
                    return members.length;
                  }
                  return 1; // 默认至少有主持人
                };

                return (
                  <List.Item
                    key={meeting.id || index}
                    onClick={() => handleOngoingMeetingClick(meeting)}
                    arrow={false}
                    className={`${style['meeting-item']} ${style['ongoing-meeting']}`}
                    style={{
                      '--border-inner': 'none',
                      '--padding-left': '0',
                      '--padding-right': '0'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <div className={style['meeting-icon']}>
                        {meeting.meetMediaType === 'video' ? (
                          <div className={style['video-icon']}>📹</div>
                        ) : (
                          <div className={style['audio-icon']}>🎤</div>
                        )}
                      </div>
                      <div className={style['meeting-info']}>
                        <div className={style['meeting-title']}>
                          {meeting.meetName || '未命名会议'}
                        </div>
                        <div className={style['meeting-time']}>
                          {formatTime(meeting.timeBegin)} | {getParticipantCount(meeting.members)}人
                        </div>
                      </div>
                      <div className={style['join-button']}>
                        <span>加入</span>
                      </div>
                    </div>
                  </List.Item>
                );
              })}
            </List>
          </div>
        </div>
      )}

      {/* 历史会议列表区域 */}
      <div className={style['meeting-list-container']}>
        <div className={style['list-header']}>
          <span>历史会议</span>
          {meetingList.length > 0 && (
            <span className={style['meeting-count']}>({meetingList.length})</span>
          )}
        </div>
        
        {meetingList.length === 0 ? (
          <div className={style['empty-container']}>
            <Empty
              description="暂无会议"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        ) : (
          <div className={style['meeting-list']}>
            <List>
              {meetingList.map((meeting, index) => {
                // 格式化时间显示
                const formatTime = (timeBegin, timeEnd) => {
                  if (!timeBegin) return '';
                  const start = timeBegin.substring(5, 16); // 提取 MM-dd HH:mm
                  const end = timeEnd ? timeEnd.substring(11, 16) : ''; // 提取 HH:mm
                  return end ? `${start}-${end}` : start;
                };

                // 获取参与人数
                const getParticipantCount = (members) => {
                  if (Array.isArray(members)) {
                    return members.length;
                  }
                  return 1; // 默认至少有主持人
                };

                return (
                  <List.Item
                    key={meeting.id || index}
                    onClick={() => handleMeetingClick(meeting)}
                    arrow={false}
                    className={style['meeting-item']}
                    style={{
                      '--border-inner': 'none',
                      '--padding-left': '0',
                      '--padding-right': '0'
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <div className={style['meeting-icon']}>
                        {meeting.meetMediaType === 'video' ? (
                          <div className={style['video-icon']}>📹</div>
                        ) : (
                          <div className={style['audio-icon']}>🎤</div>
                        )}
                      </div>
                      <div className={style['meeting-info']}>
                        <div className={style['meeting-title']}>
                          {meeting.meetName || '未命名会议'}
                        </div>
                        <div className={style['meeting-time']}>
                          {meeting.timeEnd} | {getParticipantCount(meeting.members)}人
                        </div>
                      </div>
                    </div>
                  </List.Item>
                );
              })}
            </List>
          </div>
        )}
      </div>
    </>
  );

  return (
    <div className={style.meeting}>
      {renderPageContent()}
    </div>
  );
}

export default Meeting;
