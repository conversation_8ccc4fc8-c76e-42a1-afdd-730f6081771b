.participants-container {
  height: 100%;
  background: #ffffff;
  display: flex;
  flex-direction: column;

  // 头部
  .participants-header {
    padding: 20px 16px 16px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    position: sticky;
    top: 0;
    z-index: 10;

    .header-title {
      font-size: 18px;
      font-weight: 600;
      color: #333333;
      margin: 0;
    }

    .close-button {
      width: 32px;
      height: 32px;
      border: none;
      background: #f5f5f5;
      border-radius: 50%;
      color: #666666;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: #e6f7ff;
        color: #1890ff;
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // 内容区域
  .participants-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;

    .participants-section {
      margin-bottom: 16px;

      .section-title {
        padding: 12px 16px 8px;
        font-size: 14px;
        font-weight: 600;
        color: #666666;
        background: #fafafa;
        border-bottom: 1px solid #f0f0f0;
      }

      .participants-list {
        background: #ffffff;

        :global(.adm-list-body) {
          border: none;
        }

        :global(.adm-list-item) {
          padding: 0;
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }

    .empty-state {
      padding: 40px 20px;
      text-align: center;
      color: #999999;

      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }

  // 参与者项
  .participant-item {
    padding: 12px 16px !important;
    background: #ffffff;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f8f9fa;
    }

    .participant-avatar {
      position: relative;
      margin-right: 12px;

      .host-crown {
        position: absolute;
        top: -4px;
        right: -4px;
        width: 16px;
        height: 16px;
        background: #1890ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8px;
        color: #ffffff;
        border: 2px solid #ffffff;
      }
    }

    .participant-info {
      flex: 1;
      min-width: 0;

      .participant-name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        gap: 8px;

        .host-badge {
          background: #1890ff;
          color: #ffffff;
          font-size: 10px;
          padding: 2px 6px;
          border-radius: 4px;
          font-weight: 500;
        }
      }

      .participant-status {
        font-size: 12px;
        color: #666666;
      }
    }

    .participant-controls {
      display: flex;
      align-items: center;

      .status-indicators {
        display: flex;
        gap: 8px;

        .audio-status,
        .video-status {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          transition: all 0.2s ease;

          &.active {
            background: #f6ffed;
            color: #52c41a;
          }

          &.muted {
            background: #fff2e8;
            color: #fa8c16;
          }

          &.disabled {
            background: #f5f5f5;
            color: #d9d9d9;
          }
        }
      }
    }
  }

  // 底部信息区域
  .participants-footer {
    padding: 16px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;

    .meeting-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 16px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .info-label {
          font-size: 14px;
          color: #666666;
        }

        .info-value {
          font-size: 14px;
          color: #333333;
          font-weight: 500;
        }
      }
    }

    // 主持人操作按钮区域
    .host-actions {
      display: flex;
      gap: 12px;
      justify-content: center;

      .action-button {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px 8px;
        background: #ffffff;
        border: 1px solid #d9d9d9;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        min-height: 60px;

        &:hover {
          background: #f5f5f5;
          border-color: #1890ff;
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        &.active {
          background: #ff4d4f;
          border-color: #ff4d4f;
          color: #ffffff;

          .action-icon {
            color: #ffffff;
          }

          .action-text {
            color: #ffffff;
          }

          &:hover {
            background: #ff7875;
            border-color: #ff7875;
          }
        }

        &.processing {
          background: #f0f0f0;
          border-color: #d9d9d9;
          color: #999999;
          cursor: not-allowed;
          opacity: 0.6;

          .action-icon {
            color: #999999;
          }

          .action-text {
            color: #999999;
          }

          &:hover {
            background: #f0f0f0;
            border-color: #d9d9d9;
            transform: none;
          }
        }

        .action-icon {
          font-size: 20px;
          color: #1890ff;
          margin-bottom: 4px;
          transition: color 0.3s ease;
        }

        .action-text {
          font-size: 12px;
          color: #333333;
          font-weight: 500;
          text-align: center;
          line-height: 1.2;
          transition: color 0.3s ease;
        }

        .loading-icon {
          font-size: 20px;
          margin-bottom: 4px;
          animation: spin 1s linear infinite;
        }

        // 邀请按钮特殊样式
        &:first-child {
          .action-icon {
            color: #52c41a;
          }

          &:hover .action-icon {
            color: #73d13d;
          }
        }

        // 全体禁言按钮特殊样式
        &:last-child:not(.active) {
          .action-icon {
            color: #faad14;
          }

          &:hover .action-icon {
            color: #ffc53d;
          }
        }
      }
    }
  }

  // 移动端适配
  @media (max-width: 768px) {
    .participants-header {
      padding: 16px 12px 12px;

      .header-title {
        font-size: 16px;
      }

      .close-button {
        width: 28px;
        height: 28px;
        font-size: 14px;
      }
    }

    .participant-item {
      padding: 10px 12px !important;

      .participant-info {
        .participant-name {
          font-size: 15px;
        }

        .participant-status {
          font-size: 11px;
        }
      }

      .participant-controls .status-indicators {
        .audio-status,
        .video-status {
          width: 20px;
          height: 20px;
          font-size: 10px;
        }
      }
    }

    .participants-footer {
      padding: 12px;

      .meeting-info {
        margin-bottom: 12px;

        .info-item {
          .info-label,
          .info-value {
            font-size: 13px;
          }
        }
      }

      .host-actions {
        gap: 8px;

        .action-button {
          padding: 10px 6px;
          min-height: 50px;

          .action-icon {
            font-size: 18px;
            margin-bottom: 3px;
          }

          .action-text {
            font-size: 11px;
          }
        }
      }
    }
  }

  // 暗色模式适配
  @media (prefers-color-scheme: dark) {
    background: #1a1a1a;

    .participants-header {
      background: #1a1a1a;
      border-bottom-color: #333333;

      .header-title {
        color: #ffffff;
      }

      .close-button {
        background: #333333;
        color: #cccccc;

        &:hover {
          background: #1890ff;
          color: #ffffff;
        }
      }
    }

    .participants-content {
      .participants-section {
        .section-title {
          background: #262626;
          color: #cccccc;
          border-bottom-color: #333333;
        }

        .participants-list {
          background: #1a1a1a;

          :global(.adm-list-item) {
            border-bottom-color: #333333;
          }
        }
      }

      .empty-state {
        color: #666666;
      }
    }

    .participant-item {
      background: #1a1a1a !important;

      &:hover {
        background: #262626 !important;
      }

      .participant-info {
        .participant-name {
          color: #ffffff;
        }

        .participant-status {
          color: #cccccc;
        }
      }
    }

    .participants-footer {
      background: #262626;
      border-top-color: #333333;

      .meeting-info .info-item {
        .info-label {
          color: #cccccc;
        }

        .info-value {
          color: #ffffff;
        }
      }

      .host-actions {
        .action-button {
          background: #333333;
          border-color: #555555;
          color: #ffffff;

          &:hover {
            background: #404040;
            border-color: #1890ff;
          }

          &.active {
            background: #ff4d4f;
            border-color: #ff4d4f;

            &:hover {
              background: #ff7875;
              border-color: #ff7875;
            }
          }

          .action-icon {
            color: #ffffff;
          }

          .action-text {
            color: #ffffff;
          }

          // 邀请按钮暗色模式
          &:first-child {
            .action-icon {
              color: #73d13d;
            }
          }

          // 全体禁言按钮暗色模式
          &:last-child:not(.active) {
            .action-icon {
              color: #ffc53d;
            }
          }
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
