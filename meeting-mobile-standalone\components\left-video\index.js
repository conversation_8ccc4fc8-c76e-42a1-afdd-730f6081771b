/*
 * @File: 视频部分
 * @Author: liulian
 * @Date: 2021-06-02 19:23:23
 * @version: V0.0.0.1
 * @LastEditTime: 2024-12-04 19:04:49
 */
import React, { Component } from 'react';
import { connect } from 'react-redux';
import ReactDOM from 'react-dom';
import { Dropdown, Menu, Modal } from 'antd';
import { setBarDirection } from '../../reducers/screen-manage-reducer';
import '../../styles/videoType.less';
import {
  getUrlParmse,
  uniqueByParam,
  getTelCallType,
} from '../../utils/method';

// 合并多个connect装饰器为一个，减少嵌套
@connect(
  state => ({
    ...state.screenManage,
    ...state.meetDetail,
    ...state.createMeet,
    ...state.loading
  }),
  { setBarDirection }
)
class LeftVideo extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isActive: 0,
      playArray: [],
      curInfo: {},
      // 缓存会议成员的当前状态，避免重复计算
      _activeMembersCache: null,
      _lastMeetMemUpdateTime: null,
    };
    
    // 绑定方法到实例
    this.handleVideoNavigation = this.handleVideoNavigation.bind(this);
    this.leftVideo = this.leftVideo.bind(this);
    this.rightVideo = this.rightVideo.bind(this);
    this.handleCloseVideoEvent = this.handleCloseVideoEvent.bind(this);
    this.addMoreAntd = this.addMoreAntd.bind(this);
  }

  // 获取changeNumber值的辅助方法，避免重复代码
  getChangeNumber() {
    return window.location.hash.indexOf('#/main/videoPartingMobile') > -1 ? 4 : 16;
  }

  // 获取会议的活跃成员
  getMeetMem() {
    const { intoMeetDetail } = this.props;
    return intoMeetDetail?.meetMem?.filter(item => ["meeting", "inMeet"].includes(item.status)) || [];
  }

  // 合并leftVideo和rightVideo的公共逻辑
  handleVideoNavigation(direction) {
    const { intoMeetDetail } = this.props;
    const { isActive } = this.state;
    const changeNumber = this.getChangeNumber();
    
    // 计算新的活跃页索引
    let newActive;
    if (direction === 'left') {
      newActive = isActive > 0 ? isActive - 1 : 0;
    } else {
      const unarray = this.getMeetMem();
      const maxPage = Math.ceil(unarray?.length / changeNumber) - 1;
      newActive = isActive < maxPage ? isActive + 1 : isActive;
    }
    
    // 如果没有变化，不执行操作
    if (newActive === isActive) return;

    if (window.scooper && window.scooper.meetvideoController) {
      window.scooper.meetvideoController.closeAll();
      window.scooper.meetvideoController.recoverDraggble(); // 恢复拖拽
    }
    
    setTimeout(() => {
      const unarray = this.getMeetMem();
      let num = 0;
      let playArray = [];
      
      unarray.forEach((item, index) => {
        // 计算当前页的会议成员范围
        if (index >= newActive * changeNumber && index < (newActive + 1) * changeNumber) {
          const tel = item.tel || item.memTel;
          if (window.scooper && window.scooper.meetvideoController) {
            window.scooper.meetvideoController.play(
              tel,
              num,
              item.memName || tel,
              getTelCallType(tel),
              item.status
            );
          }
          playArray.push(tel);
          num++;
        }
      });
      
      this.setState({ 
        isActive: newActive,
        playArray 
      });
    }, 100);
  }

  leftVideo() {
    this.handleVideoNavigation('left');
  }

  rightVideo() {
    this.handleVideoNavigation('right');
  }

  // 处理关闭视频事件
  handleCloseVideoEvent() {
    if (window.scooper && window.scooper.meetvideoController) {
      window.scooper.meetvideoController.closeAll();
    }
  }

  // 添加更多操作的下拉菜单
  addMoreAntd() {
    const { curInfo } = this.state;
    
    if (!curInfo.tel) return;

    const menu = (
      <Menu>
        <Menu.Item key="1" onClick={() => this.handleMenuAction('action1')}>
          操作1
        </Menu.Item>
        <Menu.Item key="2" onClick={() => this.handleMenuAction('action2')}>
          操作2
        </Menu.Item>
      </Menu>
    );

    return (
      <Dropdown overlay={menu} trigger={['click']}>
        <a className="ant-dropdown-link" onClick={e => e.preventDefault()}>
          更多操作
        </a>
      </Dropdown>
    );
  }

  // 处理菜单操作
  handleMenuAction(action) {
    const { curInfo } = this.state;
    console.log(`执行操作: ${action}, 目标: ${curInfo.tel}`);
    // 这里可以添加具体的操作逻辑
  }

  // 处理右键菜单点击
  handleContextMenu = (e) => {
    e.preventDefault();
    
    try {
      const target = e.target.closest('.screen');
      if (!target) return;
      
      // 获取视频ID的辅助函数
      const getVideoId = (element) => {
        return element.getAttribute('data-tel') || 
               element.querySelector('[data-tel]')?.getAttribute('data-tel');
      };
      
      // 获取索引的辅助函数
      const getIndex = (element) => {
        return parseInt(element.getAttribute('data-index')) || 0;
      };
      
      const videoId = getVideoId(target);
      if (!videoId) return;
      
      const videoIndex = getIndex(target);
      const { intoMeetDetail } = this.props;
      const memberInfo = intoMeetDetail?.meetMem?.find(item => item.tel === videoId) || {};
      
      this.setState({
        curInfo: { ...memberInfo, index: videoIndex, tel: videoId }
      }, () => {
        this.addMoreAntd();
      });
    } catch (error) {
      console.error('处理菜单点击出错:', error);
    }
  };

  // 检测是否是移动设备
  isMobileDevice = () => {
    return (typeof window.orientation !== "undefined") || 
           (navigator.userAgent.indexOf('IEMobile') !== -1);
  };

  render() {
    const {
      barDirection,
      isShareing,
      isShowColl,
      intoMeetDetail,
      isMultiScreen,
      isShowSpeakName
    } = this.props;
    
    const { isActive } = this.state;
    const changeNumber = this.getChangeNumber();
    const { isMain } = getUrlParmse();

    // 处理会议成员
    const member = intoMeetDetail?.meetMem ? 
                  uniqueByParam(intoMeetDetail.meetMem, 'tel') : 
                  [];
    const meetMemings = member.filter(item => 
      ["meeting", "inMeet"].includes(item.status)
    );
    
    // 计算总页数
    const numbers = Array.from({
      length: Math.ceil(meetMemings?.length / changeNumber)
    }, (_, i) => i);
    
    // 是否为移动设备
    const isMobile = this.isMobileDevice();

    return (
      <div className="left-video-container">
        <div className="video-part">
          <div className='ocx-video hide'>
            <div className="camera-content video-area">
            </div>
          </div>
          <div className='web-rtc-video hide'>
            <div className='web-rtc-camera-content'>
              <div id="video-main-web-rtc" className="video-main" onContextMenu={this.handleContextMenu}>
                {/* 视频内容将由videoController动态生成 */}
              </div>
            </div>
          </div>
        </div>
        
        {/* 导航按钮 */}
        {!isMobile && numbers.length > 1 && (
          <div className="video-navigation">
            <button onClick={this.leftVideo} disabled={isActive === 0}>
              上一页
            </button>
            <span>{isActive + 1} / {numbers.length}</span>
            <button onClick={this.rightVideo} disabled={isActive === numbers.length - 1}>
              下一页
            </button>
          </div>
        )}
      </div>
    );
  }
}

export default LeftVideo;
