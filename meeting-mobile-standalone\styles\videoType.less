// 视频相关样式
.video-part {
    width: 100%;
    height: 100%;
    position: relative;
    
    .ocx-video {
        width: 100%;
        height: 100%;
        
        &.hide {
            display: none;
        }
        
        .camera-content {
            width: 100%;
            height: 100%;
            
            &.video-area {
                background: #000;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                font-size: 1.2rem;
            }
        }
    }
    
    .web-rtc-video {
        width: 100%;
        height: 100%;
        
        &.hide {
            display: none;
        }
        
        .web-rtc-camera-content {
            width: 100%;
            height: 100%;
            position: relative;
            
            #video-main-web-rtc {
                width: 100%;
                height: 100%;
                background: #000;
                position: relative;
                
                .screen {
                    position: absolute;
                    background: #1a1a1a;
                    border: 1px solid #333;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    font-size: 0.9rem;
                    
                    &[data-tel] {
                        cursor: pointer;
                    }
                    
                    .video-box {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        background: #000;
                    }
                    
                    .stream-loading {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: #fff;
                        font-size: 0.8rem;
                        text-align: center;
                    }
                    
                    .frame-decoded {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                    }
                    
                    .operate-btn {
                        position: absolute;
                        top: 0.5rem;
                        right: 0.5rem;
                        display: flex;
                        gap: 0.3rem;
                        
                        .recv-audio-btn,
                        .unrecv-audio-btn {
                            width: 2rem;
                            height: 2rem;
                            border-radius: 50%;
                            background: rgba(0, 0, 0, 0.7);
                            border: none;
                            color: #fff;
                            cursor: pointer;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 0.8rem;
                            
                            &:hover {
                                background: rgba(0, 0, 0, 0.9);
                            }
                        }
                        
                        .recv-audio-btn {
                            background: #1890ff;
                        }
                        
                        .unrecv-audio-btn {
                            background: #ff4d4f;
                        }
                    }
                    
                    // 全屏状态
                    &.fullScreen {
                        position: fixed !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: 100vw !important;
                        height: 100vh !important;
                        z-index: 9999;
                        border: none;
                    }
                }
            }
        }
    }
}

// 视频网格布局
.video-grid-1 .screen {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.video-grid-2 .screen {
    width: 50%;
    height: 100%;
    
    &:nth-child(1) { top: 0; left: 0; }
    &:nth-child(2) { top: 0; left: 50%; }
}

.video-grid-4 .screen {
    width: 50%;
    height: 50%;
    
    &:nth-child(1) { top: 0; left: 0; }
    &:nth-child(2) { top: 0; left: 50%; }
    &:nth-child(3) { top: 50%; left: 0; }
    &:nth-child(4) { top: 50%; left: 50%; }
}

.video-grid-6 .screen {
    width: 33.33%;
    height: 50%;
    
    &:nth-child(1) { top: 0; left: 0; }
    &:nth-child(2) { top: 0; left: 33.33%; }
    &:nth-child(3) { top: 0; left: 66.66%; }
    &:nth-child(4) { top: 50%; left: 0; }
    &:nth-child(5) { top: 50%; left: 33.33%; }
    &:nth-child(6) { top: 50%; left: 66.66%; }
}

.video-grid-9 .screen {
    width: 33.33%;
    height: 33.33%;
    
    &:nth-child(1) { top: 0; left: 0; }
    &:nth-child(2) { top: 0; left: 33.33%; }
    &:nth-child(3) { top: 0; left: 66.66%; }
    &:nth-child(4) { top: 33.33%; left: 0; }
    &:nth-child(5) { top: 33.33%; left: 33.33%; }
    &:nth-child(6) { top: 33.33%; left: 66.66%; }
    &:nth-child(7) { top: 66.66%; left: 0; }
    &:nth-child(8) { top: 66.66%; left: 33.33%; }
    &:nth-child(9) { top: 66.66%; left: 66.66%; }
}

.video-grid-16 .screen {
    width: 25%;
    height: 25%;
    
    &:nth-child(1) { top: 0; left: 0; }
    &:nth-child(2) { top: 0; left: 25%; }
    &:nth-child(3) { top: 0; left: 50%; }
    &:nth-child(4) { top: 0; left: 75%; }
    &:nth-child(5) { top: 25%; left: 0; }
    &:nth-child(6) { top: 25%; left: 25%; }
    &:nth-child(7) { top: 25%; left: 50%; }
    &:nth-child(8) { top: 25%; left: 75%; }
    &:nth-child(9) { top: 50%; left: 0; }
    &:nth-child(10) { top: 50%; left: 25%; }
    &:nth-child(11) { top: 50%; left: 50%; }
    &:nth-child(12) { top: 50%; left: 75%; }
    &:nth-child(13) { top: 75%; left: 0; }
    &:nth-child(14) { top: 75%; left: 25%; }
    &:nth-child(15) { top: 75%; left: 50%; }
    &:nth-child(16) { top: 75%; left: 75%; }
}
