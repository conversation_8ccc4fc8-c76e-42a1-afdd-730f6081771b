/**
 * @Create: 号码状态测试组件
 * @Date: 2025/01/28
 * @desc: 测试号码状态功能的组件
 */

import React, { useState } from 'react';
import { Card, List, Tag, Button, Space, Input, message, Statistic, Row, Col } from 'antd';
import { 
    PhoneOutlined, 
    UserOutlined, 
    ReloadOutlined,
    ClearOutlined,
    SearchOutlined
} from '@ant-design/icons';
import { useTelStatus, useSingleTelStatus, useMultipleTelStatus } from '../../../hooks/useTelStatus';

function TelStatusTest() {
    const [testTel, setTestTel] = useState('13800138001');
    const [searchTel, setSearchTel] = useState('');
    
    // 使用号码状态 Hook
    const {
        telStatusList,
        onlineTels,
        callableTels,
        busyTels,
        statusStats,
        getTelStatusInfo,
        getStatusText,
        getStatusColor,
        isOnline,
        canCall,
        isInCall,
        updateTelStatus,
        batchUpdateTelStatus,
        refreshTelStatus,
        clearAllTelStatus
    } = useTelStatus();

    // 使用单个号码状态
    const singleTelStatus = useSingleTelStatus(testTel);

    // 使用多个号码状态
    const testTels = ['13800138001', '13800138002', '13800138003'];
    const multipleTelStatus = useMultipleTelStatus(testTels);

    // 模拟更新号码状态
    const handleUpdateStatus = () => {
        const mockStatuses = ['callst_idle', 'callst_doubletalk', 'callst_meet', 'callst_offline'];
        const randomStatus = mockStatuses[Math.floor(Math.random() * mockStatuses.length)];
        
        updateTelStatus(testTel, {
            status: randomStatus,
            onlineStatus: randomStatus === 'callst_offline' ? 'callst_offline' : 'online',
            timestamp: Date.now()
        });
        
        message.success(`已更新 ${testTel} 状态为 ${randomStatus}`);
    };

    // 批量更新状态
    const handleBatchUpdate = () => {
        const updates = {};
        testTels.forEach(tel => {
            const mockStatuses = ['callst_idle', 'callst_doubletalk', 'callst_meet'];
            const randomStatus = mockStatuses[Math.floor(Math.random() * mockStatuses.length)];
            updates[tel] = {
                status: randomStatus,
                onlineStatus: 'online',
                timestamp: Date.now()
            };
        });
        
        batchUpdateTelStatus(updates);
        message.success(`已批量更新 ${testTels.length} 个号码状态`);
    };

    // 过滤号码列表
    const filteredTels = Object.keys(telStatusList).filter(tel => 
        !searchTel || tel.includes(searchTel)
    );

    return (
        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
            <h2>号码状态测试</h2>
            
            {/* 统计信息 */}
            <Card title="状态统计" style={{ marginBottom: '20px' }}>
                <Row gutter={16}>
                    <Col span={6}>
                        <Statistic 
                            title="总号码数" 
                            value={statusStats.total} 
                            prefix={<PhoneOutlined />}
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="在线数量" 
                            value={statusStats.online} 
                            valueStyle={{ color: '#52c41a' }}
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="可拨打" 
                            value={statusStats.callable} 
                            valueStyle={{ color: '#1890ff' }}
                        />
                    </Col>
                    <Col span={6}>
                        <Statistic 
                            title="通话中" 
                            value={statusStats.busy} 
                            valueStyle={{ color: '#ff4d4f' }}
                        />
                    </Col>
                </Row>
                <div style={{ marginTop: '16px' }}>
                    <Tag color="green">在线率: {statusStats.onlineRate}%</Tag>
                </div>
            </Card>

            {/* 控制面板 */}
            <Card title="控制面板" style={{ marginBottom: '20px' }}>
                <Space wrap>
                    <Input
                        placeholder="测试号码"
                        value={testTel}
                        onChange={(e) => setTestTel(e.target.value)}
                        style={{ width: 150 }}
                    />
                    <Button onClick={handleUpdateStatus}>
                        随机更新状态
                    </Button>
                    <Button onClick={handleBatchUpdate}>
                        批量更新状态
                    </Button>
                    <Button icon={<ReloadOutlined />} onClick={refreshTelStatus}>
                        刷新状态
                    </Button>
                    <Button icon={<ClearOutlined />} onClick={clearAllTelStatus} danger>
                        清空状态
                    </Button>
                </Space>
            </Card>

            {/* 单个号码状态测试 */}
            <Card title={`单个号码状态测试 (${testTel})`} style={{ marginBottom: '20px' }}>
                <div>
                    <p><strong>状态:</strong> 
                        <Tag color={singleTelStatus.color}>
                            {singleTelStatus.text}
                        </Tag>
                    </p>
                    <p><strong>是否在线:</strong> 
                        <Tag color={singleTelStatus.isOnline ? 'green' : 'red'}>
                            {singleTelStatus.isOnline ? '在线' : '离线'}
                        </Tag>
                    </p>
                    <p><strong>可否拨打:</strong> 
                        <Tag color={singleTelStatus.canCall ? 'blue' : 'orange'}>
                            {singleTelStatus.canCall ? '可拨打' : '不可拨打'}
                        </Tag>
                    </p>
                    <p><strong>最后更新:</strong> {singleTelStatus.lastUpdateTime || '未知'}</p>
                </div>
            </Card>

            {/* 多个号码状态测试 */}
            <Card title="多个号码状态测试" style={{ marginBottom: '20px' }}>
                <div style={{ marginBottom: '16px' }}>
                    <p>测试号码: {testTels.join(', ')}</p>
                    <p>
                        总数: {multipleTelStatus.total} | 
                        在线: {multipleTelStatus.onlineCount} | 
                        可拨打: {multipleTelStatus.callableCount} | 
                        通话中: {multipleTelStatus.busyCount}
                    </p>
                </div>
                <List
                    size="small"
                    dataSource={testTels}
                    renderItem={tel => {
                        const status = multipleTelStatus.statusInfos[tel];
                        return (
                            <List.Item>
                                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <UserOutlined />
                                    <span>{tel}</span>
                                    <Tag color={status?.color}>
                                        {status?.text || '未知'}
                                    </Tag>
                                    {status?.isOnline && <Tag color="green">在线</Tag>}
                                    {status?.canCall && <Tag color="blue">可拨打</Tag>}
                                </div>
                            </List.Item>
                        );
                    }}
                />
            </Card>

            {/* 所有号码状态列表 */}
            <Card 
                title="所有号码状态" 
                extra={
                    <Input
                        placeholder="搜索号码"
                        value={searchTel}
                        onChange={(e) => setSearchTel(e.target.value)}
                        prefix={<SearchOutlined />}
                        style={{ width: 200 }}
                    />
                }
            >
                <List
                    dataSource={filteredTels}
                    pagination={{
                        pageSize: 10,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total) => `共 ${total} 个号码`
                    }}
                    renderItem={tel => {
                        const statusInfo = getTelStatusInfo(tel);
                        return (
                            <List.Item>
                                <div style={{ 
                                    display: 'flex', 
                                    justifyContent: 'space-between', 
                                    alignItems: 'center',
                                    width: '100%'
                                }}>
                                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                        <PhoneOutlined />
                                        <span style={{ fontWeight: 'bold' }}>{tel}</span>
                                        <Tag color={statusInfo.color}>
                                            {statusInfo.text}
                                        </Tag>
                                    </div>
                                    <div style={{ display: 'flex', gap: '8px' }}>
                                        {statusInfo.isOnline && <Tag color="green">在线</Tag>}
                                        {statusInfo.canCall && <Tag color="blue">可拨打</Tag>}
                                        {isInCall(tel) && <Tag color="red">通话中</Tag>}
                                    </div>
                                </div>
                            </List.Item>
                        );
                    }}
                />
            </Card>

            {/* 使用说明 */}
            <Card title="使用说明" style={{ marginTop: '20px' }}>
                <div>
                    <h4>Hook 使用方法:</h4>
                    <pre style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
{`// 使用所有号码状态
const { telStatusList, getTelStatusInfo, isOnline, canCall } = useTelStatus();

// 使用单个号码状态
const singleStatus = useSingleTelStatus('13800138001');

// 使用多个号码状态
const multipleStatus = useMultipleTelStatus(['13800138001', '13800138002']);`}
                    </pre>
                    
                    <h4>工具函数:</h4>
                    <ul>
                        <li><code>getTelStatus(tel, telStatusList)</code> - 获取号码状态信息</li>
                        <li><code>isTelOnline(tel, telStatusList)</code> - 检查号码是否在线</li>
                        <li><code>canCallTel(tel, telStatusList)</code> - 检查号码是否可拨打</li>
                        <li><code>isTelInCall(tel, telStatusList)</code> - 检查号码是否通话中</li>
                    </ul>
                </div>
            </Card>
        </div>
    );
}

export default TelStatusTest;
