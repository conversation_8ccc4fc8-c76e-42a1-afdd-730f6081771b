/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 路由相关功能包括跳转拦截等
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

import React, {Suspense} from 'react';
import {Navigate, Route, Routes, useLocation} from 'react-router-dom'
import {Spin} from "antd";
import PageLoading from "../component/page-loading";

// 主页面
const Main = React.lazy(() => import('../view/main'));
// 第三方登录页面
const ThirdLogin = React.lazy(() => import('../view/third-login'));
// 会议详情页面
const MeetingDetail = React.lazy(() => import('../view/main/meeting/meeting-detail'));

function WebRouter() {

	const location = useLocation();

	return(
		<div className='page-content'>
			<Suspense fallback={
				<Spin className='component-loading' spinning={true} tip='页面加载中...'>
					<div style={{ minHeight: '100px' }}></div>
				</Spin>
			}>
			<PageLoading />
				<Routes location={location}>
					<Route exact path={'/'} element={<Navigate to={'/thirdLogin'} /> } />
					<Route path={'/thirdLogin'} element={<ThirdLogin />} />
					<Route path={'/main/*'} element={<Main />} />
					<Route path={'/main/meeting/detail'} element={<MeetingDetail />} />
				</Routes>
			</Suspense>
		</div>
	)
}

export default WebRouter;