/**
 * @Create: VideoContainer 去重功能测试
 * @Date: 2025/01/28
 * @desc: 测试参与者去重功能
 */

import React, { useState } from 'react';
import { Button, Space } from 'antd';
import VideoContainer from './VideoContainer';

function VideoContainerTest() {
  // 模拟包含重复参与者的数据
  const [participants, setParticipants] = useState([
    {
      id: 'user_001',
      name: '张三',
      tel: '13800138001',
      isHost: true,
      audioEnabled: true,
      videoEnabled: true
    },
    {
      id: 'user_002', 
      name: '李四',
      tel: '13800138002',
      isHost: false,
      audioEnabled: true,
      videoEnabled: false
    },
    {
      id: 'user_001', // 重复的张三
      name: '张三',
      tel: '13800138001',
      isHost: true,
      audioEnabled: false,
      videoEnabled: true
    },
    {
      id: 'user_003',
      name: '王五',
      tel: '13800138003',
      isHost: false,
      audioEnabled: true,
      videoEnabled: true
    },
    {
      id: 'user_002', // 重复的李四
      name: '李四',
      tel: '13800138002',
      isHost: false,
      audioEnabled: false,
      videoEnabled: true
    }
  ]);

  const [isFullScreen, setIsFullScreen] = useState(false);

  // 添加重复参与者
  const addDuplicateParticipants = () => {
    setParticipants(prev => [
      ...prev,
      {
        id: 'user_001', // 再次添加张三
        name: '张三',
        tel: '13800138001',
        isHost: true,
        audioEnabled: true,
        videoEnabled: false
      },
      {
        id: 'user_004',
        name: '赵六',
        tel: '13800138004',
        isHost: false,
        audioEnabled: true,
        videoEnabled: true
      }
    ]);
  };

  // 清空参与者
  const clearParticipants = () => {
    setParticipants([]);
  };

  // 重置为初始状态
  const resetParticipants = () => {
    setParticipants([
      {
        id: 'user_001',
        name: '张三',
        tel: '13800138001',
        isHost: true,
        audioEnabled: true,
        videoEnabled: true
      },
      {
        id: 'user_002', 
        name: '李四',
        tel: '13800138002',
        isHost: false,
        audioEnabled: true,
        videoEnabled: false
      },
      {
        id: 'user_001', // 重复的张三
        name: '张三',
        tel: '13800138001',
        isHost: true,
        audioEnabled: false,
        videoEnabled: true
      }
    ]);
  };

  const meetingData = {
    meetId: 'test_meeting_123',
    meetAccess: 'test_access_token',
    meetingName: '去重测试会议'
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>VideoContainer 去重功能测试</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>控制面板</h3>
        <Space>
          <Button onClick={addDuplicateParticipants}>
            添加重复参与者
          </Button>
          <Button onClick={resetParticipants}>
            重置为初始状态
          </Button>
          <Button onClick={clearParticipants}>
            清空参与者
          </Button>
          <Button 
            type={isFullScreen ? 'primary' : 'default'}
            onClick={() => setIsFullScreen(!isFullScreen)}
          >
            {isFullScreen ? '退出全屏' : '全屏模式'}
          </Button>
        </Space>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>参与者信息</h3>
        <p><strong>原始参与者数量:</strong> {participants.length}</p>
        <p><strong>参与者列表:</strong></p>
        <ul>
          {participants.map((participant, index) => (
            <li key={index}>
              ID: {participant.id} | 姓名: {participant.name} | 
              音频: {participant.audioEnabled ? '开启' : '关闭'} | 
              视频: {participant.videoEnabled ? '开启' : '关闭'}
              {participant.isHost && ' (主持人)'}
            </li>
          ))}
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>去重说明</h3>
        <p>
          • 组件会自动根据参与者的 <code>id</code> 进行去重<br/>
          • 重复的参与者只会保留第一个出现的<br/>
          • 去重过程会在控制台输出详细日志<br/>
          • 请打开浏览器控制台查看去重日志
        </p>
      </div>

      <div style={{ 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        height: '400px',
        position: 'relative'
      }}>
        <VideoContainer
          participants={participants}
          isFullScreen={isFullScreen}
          onToggleFullScreen={() => setIsFullScreen(!isFullScreen)}
          meetingData={meetingData}
          isHost={true}
        />
      </div>

      <div style={{ marginTop: '20px' }}>
        <h3>测试步骤</h3>
        <ol>
          <li>查看初始状态：有3个参与者，其中张三(user_001)重复了</li>
          <li>打开浏览器控制台，查看去重日志</li>
          <li>点击"添加重复参与者"按钮，观察去重效果</li>
          <li>点击"重置为初始状态"按钮，恢复测试环境</li>
          <li>点击"清空参与者"按钮，测试空状态</li>
        </ol>
      </div>
    </div>
  );
}

export default VideoContainerTest;
