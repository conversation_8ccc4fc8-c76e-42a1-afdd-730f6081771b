.meetingMobile{
    overflow: hidden;
    height: calc(var(--vh, 1vh) * 100);;
    position: relative;
    
    .meeting-play{
        width: 100%;
        height: calc(var(--vh, 1vh) * 100 - 14.5rem);
        position: absolute;
        overflow: auto;
        top: 7rem;
        .video-part{
            width: 100%;
            height: 100%;
            position: absolute;
            left:0px;
            .web-rtc-video,.web-rtc-camera-content,{
                width: 100%;
                height: calc(100% - 1rem);
                position: relative;
                .video-main{
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0px; 
                    left: 0px;
                    right: 0px; 
                    bottom: 0px;
                    border: 1px solid #999;
                    .fullscreen{
                        position: absolute !important;
                        width: 100% !important;
                        height: 100% !important;
                    }
                }
            }
        }
    }
    
    .meetingMobile-top{
        background-color: rgba(0, 0, 0, 1);
        width: 100%;
        height: 7rem;
        position: absolute;
        top: 0px;
        left: 0px;
        z-index: 1000;
        text-align: center;
        .meetingMobile-textBox{
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            
            .meetingMobile-name{
                font-size: 1.4rem;
                white-space: nowrap;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #FFFFFF;
            }
            .meetingMobile-time{
                font-size: 1.2rem;
                white-space: nowrap;
                font-family: Roboto;
                font-weight: 400;
                color: #FFFFFF;
            }
        }
        .meetingMobile-right-icon{
            width: 2.6rem;
            height: 2.6rem;
            position: absolute;
            right: 0.6rem;
            top: 50%;
            transform: translateY(-50%);
            background-size: 100% 100%;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');
            cursor: pointer;
        }
        
    }
    .meetingMobile-moveVideo{
        position: absolute;
        top: 7rem;
        width: 100%;
        z-index: 100;
        
        .video-main{
            height: calc(var(--vh, 1vh) * 100 - 14.5rem);
            position: absolute;
            top: 0px; 
            left: 0px;
            right: 0px; 
            bottom: 0px;
            border: 1px solid #999;
            .fullscreen{
                position: absolute !important;
                width: 100% !important;
                height: 100% !important;
            }
        }
        .sel{
            border:1px solid #999
        }
        .video-main li.fullscreen{
            position: absolute !important;
            width: 100% !important;
            height: 100% !important;
        }
        .screen{
            float: left;
            width: 50%;
            height: 49.7%;
            border: 1px solid #999;
            position: relative;
            box-sizing: border-box;
            pointer-events: none;
            .videoPlays{
                width: 3rem;
                height: 3rem;
                position: absolute;
                left: 50%;
                top: 50%;
                background-color: blue;
            }
            .frame-decoded{
                top: 0px !important;
                left: 0px !important;
            }
            .stream-loading{
                width: 70%;
                font-size: 1rem;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%) !important;
                margin: 0px;
            }
            .operate-btn{
                visibility: collapse;
            }
            .video-box{
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .screen.fullScreen{
            position: fixed !important;
            width: 99.4% !important;
            height: 75% !important;
            z-index: 9999;
            top: 7rem;
            left: 0;
        }
    }
    
    .meetingMobile-bottom{
        position: absolute;
        bottom: 0px;
        width: 100%;
        height: 7.5rem;
        background-color: rgba(0, 0, 0, 1);
        display: flex;
        justify-content: space-around;
        align-items: center;
        z-index: 1000;
        
        div{
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 0.5rem;
            
            i{
                width: 4rem;
                height: 4rem;
                border-radius: 50%;
                margin-bottom: 0.5rem;
                background-size: 50% 50%;
                background-repeat: no-repeat;
                background-position: center;
                
                &.on{
                    background-color: #1890ff;
                }
                &.off{
                    background-color: #ff4d4f;
                }
            }
            
            p{
                color: #FFFFFF;
                font-size: 1.2rem;
                margin: 0;
                text-align: center;
            }
        }
        
        // 麦克风图标
        div:nth-child(1) i{
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJDMTAuOSAyIDEwIDIuOSAxMCA0VjEyQzEwIDEzLjEgMTAuOSAxNCAxMiAxNEMxMy4xIDE0IDE0IDEzLjEgMTQgMTJWNEMxNCAyLjkgMTMuMSAyIDEyIDJaTTE5IDEyQzE5IDE1LjUzIDE2LjM5IDE4LjQ0IDEzIDE4LjkzVjIySDExVjE4LjkzQzcuNjEgMTguNDQgNSAxNS41MyA1IDEySDdDNyAxNC43NiA5LjI0IDE3IDEyIDE3UzE3IDE0Ljc2IDE3IDEySDEyWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+');
        }
        
        // 摄像头图标
        div:nth-child(2) i{
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDZIMTlMMTcuNSA0SDE0LjVMMTMgNkg5QzguNDUgNiA4IDYuNDUgOCA3VjE5QzggMTkuNTUgOC40NSAyMCA5IDIwSDE5QzE5LjU1IDIwIDIwIDE5LjU1IDIwIDE5VjhDMjAgNy40NSAxOS41NSA3IDE5IDdIMjFWNlpNMTQgMTdDMTEuMjQgMTcgOSAxNC43NiA5IDEyUzkuMjQgNyAxMiA3UzE1IDkuMjQgMTUgMTJTMTIuNzYgMTcgMTAgMTdaTTEyIDlDMTAuMzQgOSA5IDEwLjM0IDkgMTJTMTAuMzQgMTUgMTIgMTVTMTUgMTMuNjYgMTUgMTJTMTMuNjYgOSAxMiA5WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+');
        }
        
        // 扬声器图标
        div:nth-child(3) i{
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgOVYxNUg3TDEyIDIwVjRMNyA5SDNaTTE2LjUgMTJDMTYuNSAxMC4yMyAxNS40OCA4LjcxIDEzLjk3IDcuOTdWMTYuMDNDMTUuNDggMTUuMjkgMTYuNSAxMy43NyAxNi41IDEyWk0xNCA3LjQ3VjguODNDMTQuODkgOS4yNiAxNS41IDEwLjExIDE1LjUgMTFIMTdDMTcgOS44MyAxNi4xNSA4Ljc3IDE0IDcuNDdaTTE0IDE1LjE3VjE2LjUzQzE2LjE1IDE1LjIzIDE3IDE0LjE3IDE3IDEzSDE1LjVDMTUuNSAxMy44OSAxNC44OSAxNC43NCAxNCAxNS4xN1oiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPg==');
        }
        
        // 参会人图标
        div:nth-child(4) i{
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDRDMTguMjEgNCAyMCA1Ljc5IDIwIDhTMTguMjEgMTIgMTYgMTJTMTIgMTAuMjEgMTIgOFMxMy43OSA0IDE2IDRaTTggNEM5LjEgNCA5LjkgNC45IDkuOSA2UzkuMSA4IDggOFM2LjEgNy4xIDYuMSA2UzYuOSA0IDggNFpNOCAxMEM5LjEgMTAgMTAgMTAuOSAxMCAxMlM5LjEgMTQgOCAxNFM2IDEzLjEgNiAxMlM2LjkgMTAgOCAxMFpNMTYgMTRDMTQuOSAxNCAxNCAxNC45IDE0IDE2UzE0LjkgMTggMTYgMThTMTggMTcuMSAxOCAxNlMxNy4xIDE0IDE2IDE0WiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+');
        }
    }
    
    .meeting-radice{
        position: absolute;
        bottom: 8rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        
        li{
            width: 0.8rem;
            height: 0.8rem;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.5);
            margin: 0 0.3rem;
            cursor: pointer;
            
            &.opacity{
                background-color: rgba(255, 255, 255, 1);
            }
        }
    }
}

// 左视频容器样式
.left-video-container {
    width: 100%;
    height: 100%;
    position: relative;
    
    .video-navigation {
        position: absolute;
        bottom: 1rem;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        gap: 1rem;
        background: rgba(0, 0, 0, 0.7);
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        z-index: 1001;
        
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            cursor: pointer;
            
            &:disabled {
                background: #ccc;
                cursor: not-allowed;
            }
        }
        
        span {
            color: white;
            font-size: 1rem;
        }
    }
}
