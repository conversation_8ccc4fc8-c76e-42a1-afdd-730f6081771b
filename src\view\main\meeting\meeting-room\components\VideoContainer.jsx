/**
 * @Create: 视频容器组件
 * @Date: 2025/01/28
 * @desc: 会议视频显示容器
 */

import React, { useState, useRef, useEffect, useContext } from 'react';
import {
  FullscreenOutlined,
  FullscreenExitOutlined,
  CloseOutlined,
  AudioOutlined,
  AudioMutedOutlined,
  LeftOutlined,
  RightOutlined
} from '@ant-design/icons';
import { MainContext } from "../../../context";
import { createVideoController, checkDependencies } from '../../../../../util/videoWebRtcLoader';
import { dispatch_web_api } from '../../../../../util/api';
import { message } from 'antd';
import style from '../style/sass/video-container.module.scss';

function VideoContainer({
  participants = [],
  isFullScreen,
  onToggleFullScreen,
  meetingData = {},
  isHost = false
}) {
  // 对参与者列表进行去重处理，确保同一个 ID 只出现一次
  const uniqueParticipants = React.useMemo(() => {
    const seen = new Set();
    const unique = [];

    participants.forEach(participant => {
      // 使用 id 作为唯一标识符进行去重
      if (participant && participant.id && !seen.has(participant.id)) {
        seen.add(participant.id);
        unique.push(participant);
        console.log('✅ [去重] 添加参与者:', participant.id, participant.name);
      } else if (participant && participant.id && seen.has(participant.id)) {
        console.log('⚠️ [去重] 跳过重复参与者:', participant.id, participant.name);
      }
    });

    console.log('🔄 [去重] 原始参与者数量:', participants.length, '去重后数量:', unique.length);
    return unique;
  }, [participants]);

  const [activeVideoIndex, setActiveVideoIndex] = useState(0);
  const videoContainerRef = useRef(null);
  const participantsRef = useRef([]); // 使用 ref 保存最新的参与者列表

  // 4分屏分页相关状态
  const [currentPage, setCurrentPage] = useState(0); // 当前页码
  const videosPerPage = 4; // 每页显示4个视频

  // 从 MainContext 获取软手柄注册状态
  const { registerFailed } = useContext(MainContext);

  // 视频注册状态
  const [videoRegistered, setVideoRegistered] = useState(false);
  const [pendingParticipants, setPendingParticipants] = useState([]); // 等待播放的参与者
  const ownerInfoRef = useRef(null); // 当前用户信息

  // 软手柄注册状态：registerFailed 为 false 表示注册成功
  const shandleRegistered = !registerFailed;

  // 处理视频点击切换
  const handleVideoClick = (index) => {
    setActiveVideoIndex(index);
  };

  // 计算分页相关数据
  const totalPages = Math.ceil(uniqueParticipants.length / videosPerPage);
  const startIndex = currentPage * videosPerPage;
  const endIndex = Math.min(startIndex + videosPerPage, uniqueParticipants.length);
  const currentPageParticipants = uniqueParticipants.slice(startIndex, endIndex);

  // 处理分页切换
  const handlePrevPage = () => {
    if (currentPage > 0) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      console.log('🔄 切换到上一页:', newPage, '显示参与者:', uniqueParticipants.slice(newPage * videosPerPage, (newPage + 1) * videosPerPage).map(p => p.name));
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      console.log('🔄 切换到下一页:', newPage, '显示参与者:', uniqueParticipants.slice(newPage * videosPerPage, (newPage + 1) * videosPerPage).map(p => p.name));
    }
  };

  // 确保 activeVideoIndex 不会超出去重后的参与者列表范围
  useEffect(() => {
    if (activeVideoIndex >= uniqueParticipants.length && uniqueParticipants.length > 0) {
      console.log('🔄 [去重] 调整 activeVideoIndex:', activeVideoIndex, '->', 0);
      setActiveVideoIndex(0);
    }
  }, [uniqueParticipants.length, activeVideoIndex]);

  // 当参与者数量变化时，重置页码
  useEffect(() => {
    const newTotalPages = Math.ceil(uniqueParticipants.length / videosPerPage);
    if (currentPage >= newTotalPages && newTotalPages > 0) {
      console.log('🔄 [分页] 调整页码:', currentPage, '->', newTotalPages - 1);
      setCurrentPage(newTotalPages - 1);
    }
    console.log('📊 [分页] 参与者数量:', uniqueParticipants.length, '总页数:', newTotalPages, '当前页:', currentPage);
  }, [uniqueParticipants.length, currentPage, videosPerPage]);

  // 获取当前用户信息
  const getCurrentUserTel = () => {
    try {
      return JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel;
    } catch (error) {
      console.error('获取当前用户信息失败:', error);
      return null;
    }
  };

  // 检查是否为主持人
  const checkIsHost = () => {
    const currentUserTel = getCurrentUserTel();
    return currentUserTel && meetingData.ctrlTel === currentUserTel;
  };

  // 踢出用户
  const handleKickUser = async (participant) => {
    try {
      console.log('踢出用户:', participant);

      // 调用踢出用户的API
      const result = await dispatch_web_api.kickMeetingMember({
        meetId: meetingData.meetId,
        tel: participant.tel || participant.id,
        reason: '主持人移除'
      });

      if (result.code === 0) {
        message.success(`已将 ${participant.name} 移出会议`);
      } else {
        message.error('踢出用户失败');
      }
    } catch (error) {
      console.error('踢出用户失败:', error);
      message.error('踢出用户失败');
    }
  };

  // 禁言/取消禁言用户
  const handleMuteUser = async (participant) => {
    try {
      const isMuted = !participant.isAudioOn;
      console.log('切换用户禁言状态:', participant, '目标状态:', !isMuted ? '禁言' : '取消禁言');

      // 调用禁言/取消禁言的API
      const result = await dispatch_web_api.changeMemberLevel({
        meetId: meetingData.meetId,
        tel: participant.tel || participant.id,
        level: !isMuted ? 'audience' : 'speak'
      });

      if (result.code === 0) {
        // 更新用户状态
        message.success(`已${!isMuted ? '禁言' : '取消禁言'} ${participant.name}`);
      } else {
        message.error(`${!isMuted ? '禁言' : '取消禁言'}失败`);
      }
    } catch (error) {
      console.error('切换禁言状态失败:', error);
      message.error('操作失败');
    }
  };

  // 初始化用户信息
  useEffect(() => {
    const ownerInfo = JSON.parse(localStorage.getItem('scooperOwnerInformation') || '{}');
    ownerInfoRef.current = ownerInfo;
    console.log('🔍 [调试] 当前用户信息:', ownerInfo);
    console.log('🔍 [调试] 用户ID:', ownerInfo.id, '主号码:', ownerInfo.mainTel);
  }, []);

  // 监听软手柄注册状态变化
  useEffect(() => {
    console.log('🔍 [调试] 软手柄注册状态变化:', {
      registerFailed,
      shandleRegistered,
      message: registerFailed ? '软手柄注册失败' : '软手柄注册成功'
    });
  }, [registerFailed, shandleRegistered]);

  useEffect(()=>{
    const initVideoController = async () => {
      try {
        console.log('🔧 开始初始化视频控制器...');

        // 检查依赖状态
        checkDependencies();

        let videoOpts = {
          //初始化时的界面显示的分屏树
          windows: 1,
          windowsNum:16,
          windowsArr:[4],
          draggable: true,
          // videoWebServer:'https://192.168.108.44:9999',
          // windowsBeginIndex: 0,
          showVideoInfo:0,
          conf:{token:localStorage.getItem('scooperCoreToken')},
          showVideoName: false, // 显示视频名称
          isScooperMeet: true, // 显示scooper-meet的界面
          freeWindow:true,
          // objectFit: 'true'
          // isWsPlay:true
        }

        const video = document.querySelector('#video-containers');
        if (!video) {
          console.error('❌ 找不到 #video-containers 元素');
          return;
        }

        // 使用加载器安全地创建 VideoController
        window.videoController = await createVideoController(video, videoOpts);

        // 添加事件监听器
        handleVideoAdd();

      } catch (error) {
        console.error('❌ VideoController 初始化失败:', error);

        // 重试机制
        setTimeout(() => {
          console.log('🔄 重试初始化 VideoController...');
          initVideoController();
        }, 2000);
      }
    };

    // 开始初始化
    initVideoController();
  },[])

  useEffect(()=>{
    // 更新 ref 以保存最新的参与者列表
    participantsRef.current = uniqueParticipants;
    console.log('参与者列表已更新:', uniqueParticipants);
    // 当参与者列表更新且视频已注册时，处理播放逻辑
    if (videoRegistered && uniqueParticipants.length > 0) {
      const ownerInfo = ownerInfoRef.current;

      // 分离自己和其他参与者
      const otherParticipants = [];
      let ownParticipant = null;

      uniqueParticipants.forEach(participant => {
        console.log('🔍 [调试] 检查参与者:', participant.id, participant.name);
        console.log('🔍 [调试] 用户信息匹配检查:', {
          participantId: participant.id,
          ownerId: ownerInfo?.id,
          ownerMainTel: ownerInfo?.mainTel,
          isMatch: ownerInfo && (participant.id === ownerInfo.id || participant.id === ownerInfo.mainTel)
        });

        if (ownerInfo && (participant.id === ownerInfo.id || participant.id === ownerInfo.mainTel)) {
          ownParticipant = participant;
          console.log('✅ [调试] 找到自己的参与者:', ownParticipant);
        } else {
          otherParticipants.push(participant);
          console.log('👥 [调试] 其他参与者:', participant.id);
        }
      });

      // 优先播放其他参与者
      otherParticipants.forEach((participant, index) => {
        setTimeout(() => {
          console.log('播放其他参与者视频:', participant);
          if(window.videoController.isPlaying(participant.id) > -1){
            return;
          }
          const videoWeb = document.querySelector('#videoWeb-'+participant.id);
          if(videoWeb && window.videoController){
            window.videoController.initVideoNum({
              $dom: videoWeb,
              video: participant.id,
              opts: {name: participant.name}
            }).then(res => {
              console.log('播放其他参与者视频成功:', res);
            }).catch(err => {
              console.log('播放其他参与者视频失败:', err);
            });
          }
        }, index * 200);
      });
      // 处理自己的视频 - 统一通过待播放列表机制处理
      if (ownParticipant) {
        console.log('🔍 [调试] 处理自己的视频:', ownParticipant);
        console.log('🔍 [调试] 软手柄注册状态:', shandleRegistered);

        // 无论软手柄是否注册，都加入待播放列表，由专门的 useEffect 统一处理
        console.log('📝 [调试] 将自己的视频加入待播放列表');
        setPendingParticipants(prev => {
          const exists = prev.find(p => p.id === ownParticipant.id);
          if (!exists) {
            console.log('✅ [调试] 添加到待播放列表:', ownParticipant);
            return [...prev, ownParticipant];
          } else {
            console.log('⚠️ [调试] 已存在于待播放列表，跳过添加');
            return prev;
          }
        });
      } else {
        console.log('⚠️ [调试] 未找到自己的参与者');
      }
    }
  },[uniqueParticipants, videoRegistered, shandleRegistered])

  // 当软手柄注册成功时，播放待播放的自己的视频
  useEffect(() => {
    console.log('🔍 [调试] 软手柄注册状态变化:', {
      shandleRegistered,
      pendingParticipantsLength: pendingParticipants.length,
      pendingParticipants
    });

    if (shandleRegistered && pendingParticipants.length > 0) {
      console.log('🎯 [调试] 软手柄注册成功，开始播放待播放的自己的视频:', pendingParticipants);

      pendingParticipants.forEach((participant, index) => {
        if(window.videoController.isPlaying(participant.id) > -1){
          return;
        }
        setTimeout(() => {
          console.log('🎥 [调试] 播放待播放的自己的视频:', participant);
          // 直接调用播放函数
          const videoWeb = document.querySelector('#videoWeb-'+participant.id);
          console.log('🔍 [调试] 查找视频元素:', {
            selector: '#videoWeb-'+participant.id,
            found: !!videoWeb,
            videoController: !!window.videoController
          });

          if(videoWeb && window.videoController){
            window.videoController.initVideoNum({
              $dom: videoWeb,
              video: participant.id,
              opts: {name: participant.name}
            }).then(res => {
              console.log('✅ [调试] 播放待播放视频成功:', res);
              if( participant.id === JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel){
                // 如果是自己的视频，则设置音频为开启
                // 如果是音频会议，在会议加载成功后关闭摄像头
                      if (meetingData.isVideo === false) {
                        setTimeout(() => {
                          console.log('🎤 音频会议播放成功，关闭摄像头');
                          if (window.shandleUtil && window.shandleUtil.enableCamera) {
                          window.shandleUtil.enableCamera(false);
                          console.log('📷 摄像头已关闭');
                    } else {
                      console.warn('⚠️ shandleUtil.enableCamera 方法不可用');
                    }
                  }, 700); // 延迟1秒确保播放成功
                }
              }
            }).catch(err => {
              console.log('❌ [调试] 播放待播放视频失败:', err);
            });
          } else {
            console.log('❌ [调试] 无法播放视频，缺少必要元素');
          }
        }, index * 200);
      });

      // 清空待播放列表
      console.log('🧹 [调试] 清空待播放列表');
      setPendingParticipants([]);
    } else {
      console.log('⏳ [调试] 条件不满足，不播放视频:', {
        shandleRegistered,
        hasPendingParticipants: pendingParticipants.length > 0
      });
    }
  }, [shandleRegistered, pendingParticipants]);

 const handleVideoAdd = () => {
    // 检查 videoController 是否存在
    if (!window.videoController) {
      console.error('❌ videoController 不存在，无法添加监听器');
      return;
    }

    try {
      console.log('🔧 开始添加视频事件监听器...');

      // 初始化完成
      window.videoController.addListener('initsucc', function (e) {
        console.log('✅ 视频初始化成功', e);
      });
    // 播放成功
    window.videoController.addListener('playsuccessByRealIndex', function (e) {      //播放成功  -- 更新分屏信息给python
        console.log("播放成功", e);
    })
    window.videoController.addListener('afterclose', function (e) {
        console.log("播放失败", e);
    })
    // 分屏
    window.videoController.addListener('screenchange', function (e) {   //分屏切换成功  -- 更新分屏信息给python
        console.log("分屏切换成功", e.windowNums);
        
    })
    // 拖拽
    window.videoController.addListener('dragEnd', function (e) {         //拖动成功之后的通知  --更新分屏信息给python
        console.log("拖拽成功通知：",e);
       
    })
    window.videoController.addListener('changeVideocodeType', function (data) {
        const {changeCodeType,opts,playseq,videoId} = data
        opts.videoCodeType = changeCodeType;
        setTimeout(() => {
            window.videoController.play(playseq, videoId,videoId,opts);
        },1000)
    })
    window.videoController.addListener('setRegistered', function () {
        console.log('scooper.video 注册完成');
        setVideoRegistered(true);

        // 获取最新的参与者列表
        const currentParticipants = participantsRef.current;
        console.log('视频注册完成，当前参与者列表:', currentParticipants);

        // 注册完成后，参与者列表的处理会由 useEffect 自动触发
        console.log('视频控制器注册完成，等待参与者列表更新触发播放逻辑');
    })
      // 断流
      window.videoController.addListener('necessaryTips', function (data) {
          const {playseq,opts,index} = data
          const numWin =!isNaN(playseq)?playseq+1:index+1;
          console.error('窗口:'+ numWin +', 终端:'+opts.name+'无流');
      });

      console.log('✅ 视频事件监听器添加完成');
    } catch (error) {
      console.error('❌ 添加视频事件监听器失败:', error);
    }
  }

  // 渲染单个视频窗口
  const renderVideoWindow = (participant, index, isMain = false) => {
    const isActive = index === activeVideoIndex;

    return (
      <div
        key={participant.id}
        className={`${style['video-window']} ${isMain ? style['main-video'] : style['thumbnail-video']} ${isActive ? style.active : ''}`}
        onClick={() => !isMain && handleVideoClick(index)}
        style={isMain ? { width: '100%', height: '100%' } : {}}
      >
        {/* 视频元素 */}
        <div className={style['video-element']}>
          {participant.isVideoOn ? (
            <div className={style['video']} id={'videoWeb-'+participant.id}>
               {/* 视频放置内容 */}
            </div>
          ) : (
            <div className={style['video-placeholder']}>
              <div className={style['avatar-placeholder']}>
                {participant.name.charAt(0).toUpperCase()}
              </div>
            </div>
          )}
        </div>

        {/* 参与者信息覆盖层 */}
        <div className={style['participant-overlay']}>
          <div className={style['participant-info']}>
            <span className={style['participant-name']}>
              {participant.name}
              {participant.isHost && <span className={style['host-badge']}>主持人</span>}
            </span>
            <div className={style['participant-status']}>
              {!participant.isAudioOn && (
                <span className={style['muted-indicator']}>🔇</span>
              )}
              {!participant.isVideoOn && (
                <span className={style['video-off-indicator']}>📹</span>
              )}
            </div>
          </div>
        </div>

        {/* 主持人控制按钮（右上角） */}
        {checkIsHost() && !participant.isHost && (
          <div className={style['host-controls']}>
            <button
              className={style['control-btn']}
              onClick={(e) => {
                e.stopPropagation();
                handleMuteUser(participant);
              }}
              title={participant.isAudioOn ? '禁言' : '取消禁言'}
            >
              {participant.isAudioOn ? <AudioMutedOutlined /> : <AudioOutlined />}
            </button>
            <button
              className={style['control-btn']}
              onClick={(e) => {
                e.stopPropagation();
                handleKickUser(participant);
              }}
              title="踢出"
            >
              <CloseOutlined />
            </button>
          </div>
        )}

        {/* 全屏按钮（仅主视频显示） */}
        {isMain && (
          <button
            className={style['fullscreen-btn']}
            onClick={(e) => {
              e.stopPropagation();
              onToggleFullScreen();
            }}
          >
            {isFullScreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
          </button>
        )}
      </div>
    );
  };

  // 渲染网格布局（多人会议）- 支持4分屏分页
  const renderGridLayout = () => {
    // 始终使用4分屏布局，最多显示4个视频
    const gridClass = currentPageParticipants.length <= 2 ? style['grid-2'] : style['grid-4'];

    console.log('📊 [分页渲染] 当前页参与者:', currentPageParticipants.length, '总参与者:', uniqueParticipants.length);

    return (
      <div className={style['grid-layout-container']}>
        <div className={`${style['grid-container']} ${gridClass}`}>
          {currentPageParticipants.map((participant, index) => {
            // 计算在原始列表中的索引
            const originalIndex = startIndex + index;
            return renderVideoWindow(participant, originalIndex, false);
          })}
        </div>

        {/* 分页控制按钮 */}
        {totalPages > 1 && (
          <div className={style['pagination-controls']}>
            <button
              className={`${style['page-btn']} ${style['prev-btn']}`}
              onClick={handlePrevPage}
              disabled={currentPage === 0}
            >
              <LeftOutlined />
            </button>

            <div className={style['page-info']}>
              <span>{currentPage + 1} / {totalPages}</span>
            </div>

            <button
              className={`${style['page-btn']} ${style['next-btn']}`}
              onClick={handleNextPage}
              disabled={currentPage === totalPages - 1}
            >
              <RightOutlined />
            </button>
          </div>
        )}
      </div>
    );
  };

  // 渲染演讲者布局（主视频+缩略图）
  const renderSpeakerLayout = () => {
    const mainParticipant = uniqueParticipants[activeVideoIndex] || uniqueParticipants[0];
    const thumbnailParticipants = uniqueParticipants.filter((_, index) => index !== activeVideoIndex);

    console.log('🔍 [调试] 演讲模式渲染:', {
      participantsLength: uniqueParticipants.length,
      activeVideoIndex,
      mainParticipant,
      thumbnailParticipantsLength: thumbnailParticipants.length
    });

    return (
      <div className={style['speaker-container']}>
        {/* 主视频 */}
        <div className={style['main-video-area']}>
          {mainParticipant ? (
            renderVideoWindow(mainParticipant, activeVideoIndex, true)
          ) : (
            <div className={style['empty-container']}>
              <div className={style['empty-message']}>
                <h3>等待主讲人...</h3>
                <p>演讲模式已准备就绪</p>
              </div>
            </div>
          )}
        </div>

        {/* 缩略图视频列表 */}
        {thumbnailParticipants.length > 0 && (
          <div className={style['thumbnail-area']}>
            <div className={style['thumbnail-list']}>
              {thumbnailParticipants.map((participant) => {
                const originalIndex = uniqueParticipants.findIndex(p => p.id === participant.id);
                return renderVideoWindow(participant, originalIndex, false);
              })}
            </div>
          </div>
        )}
      </div>
    );
  };

  // 根据参与者数量选择布局
  const renderLayout = () => {
    if (uniqueParticipants.length <= 1) {
      return renderSpeakerLayout();
    } else {
      // 超过1个参与者时，都使用支持分页的网格布局
      return renderGridLayout();
    }
  };

  return (
    <div 
      ref={videoContainerRef}
      className={`${style['video-container']} ${isFullScreen ? style['fullscreen'] : ''}`}
      id='video-containers'
    >
      {uniqueParticipants.length > 0 ? (
        renderLayout()
      ) : (
        <div className={style['empty-container']}>
          <div className={style['empty-message']}>
            <h3>等待其他参与者加入...</h3>
            <p>会议已准备就绪</p>
          </div>
        </div>
      )}

      {/* 网络状态指示器 */}
      <div className={style['network-indicator']}>
        <div className={style['signal-bars']}>
          <div className={`${style.bar} ${style.active}`}></div>
          <div className={`${style.bar} ${style.active}`}></div>
          <div className={`${style.bar} ${style.active}`}></div>
          <div className={style.bar}></div>
        </div>
      </div>
    </div>
  );
}

export default VideoContainer;
