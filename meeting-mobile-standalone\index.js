/*
 * @File: meeting-mobile-standalone 入口文件
 * @Author: liulian
 * @Date: 2025-01-01 00:00:00
 * @version: V1.0.0
 * @Description: 独立的移动端会议组件包
 */

import React from 'react';
import { Provider } from 'react-redux';
import MeetingMobile from './components/meeting-mobile';
import store from './store';
import './styles/meetingMobile.less';
import './styles/mettingPeople.less';
import './styles/videoType.less';

/**
 * 会议移动端组件包装器
 * @param {Object} props - 组件属性
 * @param {Object} props.config - 配置选项
 * @param {string} props.config.apiBaseUrl - API基础URL
 * @param {Object} props.config.globalObjects - 全局对象配置
 * @param {Function} props.onLeave - 离开会议回调
 * @param {Object} props.history - 路由历史对象（可选）
 */
const MeetingMobileWrapper = ({ config = {}, onLeave, history, ...otherProps }) => {
  // 设置全局配置
  React.useEffect(() => {
    if (config.apiBaseUrl) {
      // 设置API基础URL
      window.MEETING_MOBILE_CONFIG = {
        ...window.MEETING_MOBILE_CONFIG,
        apiBaseUrl: config.apiBaseUrl
      };
    }

    // 设置全局对象（如果提供的话）
    if (config.globalObjects) {
      Object.keys(config.globalObjects).forEach(key => {
        window[key] = config.globalObjects[key];
      });
    }

    // 设置默认的全局对象（如果不存在的话）
    if (!window.scooper) {
      window.scooper = {
        meetManager: { meetsObj: {} },
        meetDispatchManager: { accountDetail: {} },
        meetvideoController: {},
        util: { isTopFrame: () => true }
      };
    }

    if (!window.newShandleUtil) {
      window.newShandleUtil = {
        getSHandle: () => ({ deviceControl: { hasMultiVoiceDevice: true } }),
        enableCamera: () => {},
        audioClose: () => {},
        updateVoiceSpeaker: () => {},
        deregister: () => {},
        reRegister: () => {}
      };
    }

    if (!window.newCometd) {
      window.newCometd = {
        subscribe: () => 'mock-subscription-id',
        unsubscribe: () => {}
      };
    }
  }, [config]);

  return (
    <Provider store={store}>
      <MeetingMobile 
        {...otherProps}
        onLeave={onLeave}
        history={history}
      />
    </Provider>
  );
};

/**
 * 独立使用的会议移动端组件
 * @param {Object} props - 组件属性
 */
const StandaloneMeetingMobile = (props) => {
  return <MeetingMobileWrapper {...props} />;
};

// 导出组件和相关工具
export default StandaloneMeetingMobile;

// 导出其他有用的组件和工具
export { default as MeetingMobile } from './components/meeting-mobile';
export { default as BottomModal } from './components/meeting-mobile/mobileBottomModal';
export { default as LeftVideo } from './components/left-video';
export { default as store } from './store';

// 导出工具函数
export * from './utils/method';
export { apis } from './utils/api';

// 导出常量
export * from './config/constants';

// 导出Redux相关
export * from './reducers/create-meet-reducer';
export * from './reducers/screen-manage-reducer';
export * from './reducers/meet-detail-reducer';
export * from './reducers/loading-reducer';

/**
 * 初始化函数，用于设置全局配置
 * @param {Object} config - 配置选项
 * @param {string} config.apiBaseUrl - API基础URL
 * @param {Object} config.globalObjects - 全局对象配置
 * @param {boolean} config.debug - 是否开启调试模式
 */
export const initMeetingMobile = (config = {}) => {
  // 设置全局配置
  window.MEETING_MOBILE_CONFIG = {
    apiBaseUrl: config.apiBaseUrl || '',
    debug: config.debug || false,
    ...config
  };

  // 设置调试模式
  if (config.debug) {
    console.log('Meeting Mobile 调试模式已开启');
    window.MEETING_MOBILE_DEBUG = true;
  }

  // 设置全局对象
  if (config.globalObjects) {
    Object.keys(config.globalObjects).forEach(key => {
      window[key] = config.globalObjects[key];
    });
  }

  console.log('Meeting Mobile 组件已初始化');
};

/**
 * 获取当前配置
 */
export const getMeetingMobileConfig = () => {
  return window.MEETING_MOBILE_CONFIG || {};
};

/**
 * 检查组件是否已初始化
 */
export const isMeetingMobileInitialized = () => {
  return !!window.MEETING_MOBILE_CONFIG;
};
