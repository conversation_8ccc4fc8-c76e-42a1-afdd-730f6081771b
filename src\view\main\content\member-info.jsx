/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 人员信息
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext, useEffect, useState} from "react";
import style from '../style/sass/member-info.module.scss';
import {NavBar} from "antd-mobile";
import {MainContext} from "../context";
import {Avatar, message} from "antd";
import {List} from "antd-mobile";
import {UserOutlined} from "@ant-design/icons";
import {dispatch_web_api, ecis_access_api, scooper_core_rest_api} from "../../../util/api";

// 操作按钮列表
const ACTION_BUTTON_LIST = [
	{ title: '电话呼叫', key: 'tel', value: 'memMobile' },
	{ title: '语音电话', key: 'phone', value: 'memTel' },
	{ title: '视频电话', key: 'video', value: 'memTel' },
]

const MEM_INFO_LIST = [
	{ title: '部门', key: 'deptName' },
	{ title: '默认号码', key: 'memTel' },
	{ title: '手机号码', key: 'memMobile' },
]

function MemberInfo() {

	const {selectedMemId, setShowMemInfo, setOverType, setNonOwnerInformation, videoCallRef, ownerInformation } = useContext(MainContext);
	const [memInfo, setMemInfo] = useState({});

	/**
	 * @desc 获取人员详情
	 */
	useEffect(() => {
		let param = {
			id: selectedMemId,
		}
		scooper_core_rest_api.getOrgMemberDetail(param).then(data => {
			setMemInfo(data);
		}).catch(error => {
			console.log(`getOrgMemberDetail: ${error}`);
		})
	}, [selectedMemId]);

	/**
	 * @desc 操作点击
	 * @param key 操作点击类型
	 */
	function actionClick(key) {
		const myDispatch = window.scooper.dispatch;
		if(memInfo.accId === ownerInformation.id) {
			message.error('不能呼叫自己!');
			return ;
		}
		switch (key) {
			case 'tel': {
				if(!memInfo.memMobile) return message.warning('该用户未配置手机号!');
				window.location.href = `tel: ${memInfo.memMobile}`;
				break;
			}
			case 'phone': {
				if(!memInfo.memTel) return message.warning('该用户未配置短号!');
				setNonOwnerInformation(memInfo);
				let params = {
					tels: memInfo.memTel
				}
				dispatch_web_api.telsStatus(params).then(data => {
					let telInfo = data?.[0] || {};
					if(telInfo.status === 'callst_offline' || !telInfo.status) {
						let params = {
							accIds: memInfo.accId,
							pushAccId: ownerInformation.id
						}
						message.success('当前成员不在线,已发送邀请!');
						// ecis_access_api.publishMsg(params).then(() => {
						// 	message.success('当前成员不在线,已发送邀请!');
						// }).catch(error => {
						// 	console.log(`publishMsg: ${error}`);
						// })
					}else if(telInfo.status === 'callst_idle') {
						setOverType('callOut');
						myDispatch.calls.makeAudioCall(memInfo.memTel);
						videoCallRef.current = false;
					}else {
						message.warning('号码正在通话中请稍后再拨...');
					}
				}).catch(error => {
					console.log(`telsStatus: ${error}`);
				})
				break;
			}
			case 'video': {
				if(!memInfo.memTel) return message.warning('该用户未配置短号!');
				setNonOwnerInformation(memInfo);
				let params = {
					tels: memInfo.memTel
				}
				dispatch_web_api.telsStatus(params).then(data => {
					let telInfo = data?.[0] || {};
					if(telInfo.status === 'callst_offline' || !telInfo.status) {
						let params = {
							accIds: memInfo.accId,
							pushAccId: ownerInformation.id
						}
						message.success('当前成员不在线,已发送邀请!');
						// ecis_access_api.publishMsg(params).then(() => {
						// 	message.success('当前成员不在线,已发送邀请!');
						// }).catch(error => {
						// 	console.log(`publishMsg: ${error}`);
						// })
					}else if(telInfo.status === 'callst_idle') {
						setOverType('videoOut');
						myDispatch.calls.makeVideoCall(memInfo.memTel);
						videoCallRef.current = true;
					}else {
						message.warning('号码正在通话中请稍后再拨...');
					}
				}).catch(error => {
					console.log(`telsStatus: ${error}`);
				})
				break;
			}
			default: break;
		}
	}

	return(
		<div className={style.memberInfo}>
			<div className={style.navBarContent}>
				<NavBar back='返回' onBack={() => setShowMemInfo(false)} />
			</div>
			<div className={style.memActionContent}>
				<div className={style.avatarContent}>
					<Avatar size={64} icon={<UserOutlined />} />
				</div>
				<span className={style.memName}>{memInfo.memName}</span>
				<div className={style.actionContent}>
					{
						ACTION_BUTTON_LIST.map(item => {
							if(memInfo[item.value]) {
								return(
									<div className={style.unitAction} onClick={() => actionClick(item.key)}>
										<span className={`${item.key !== 'video'? style.iconTel: style.iconVideo}`}></span>
										<span className={style.actionName}>{item.title}</span>
									</div>
								)
							}else {
								return  null;
							}
						})
					}
				</div>
			</div>
			<div className={style.memInfoContent}>
				<List>
					{
						MEM_INFO_LIST.map(item => {
							return memInfo[item.key]? <List.Item title={item.title} key={item.key}>{memInfo[item.key]}</List.Item>: null;
						})
					}
				</List>
			</div>
		</div>
	)
}
export default MemberInfo;