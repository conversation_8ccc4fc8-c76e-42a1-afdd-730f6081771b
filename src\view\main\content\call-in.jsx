/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 电话呼入
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext} from "react";
import style from '../style/sass/call-in.module.scss';
import {MainContext} from "../context";

function CallIn () {

	const { nonOwnerInformation, allMemberInfo } = useContext(MainContext);

	/**
	 * @desc 挂断电话
	 */
	function hangupCall() {
		window.shandleUtil.hungUp();
	}

	/**
	 * @desc 接通电话
	 */
	function callConnect() {
		if(nonOwnerInformation.video) {
			window.shandleUtil.answer('true');
		}else {
			window.shandleUtil.answer();
		}
	}


	return(
		<div className={style.callIn}>
			<div className={style.callInHeader}>
				<span className={style.memName}>{allMemberInfo.current[nonOwnerInformation.telNumber]?.memName || nonOwnerInformation.telNumber}</span>
				{
					nonOwnerInformation.video && <span className={style.defaultTip}>邀请您进行视频通话...</span>
				}
			</div>
			<div className={style.callInActionContent}>
				<span className={style.iconHungUp} onClick={() => hangupCall()}></span>
				<span className={style.iconConnect} onClick={() => callConnect()}></span>
			</div>
		</div>
	)
}

export default CallIn;
