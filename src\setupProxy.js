/**
 * @Create: 周颖仁
 * @Date: 2023/5/4
 * @desc: 前端请求代理
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/5/4
 */

const { createProxyMiddleware } = require("http-proxy-middleware");


// const dev = 'https://192.168.107.20:9999';
// 182.129.202.9:9999
// const dev = 'https://182.129.202.9:9999';
// const dev = 'https://182.129.202.9:9999';

// 192.168.128.137:9998/
// const dev = 'https://192.168.128.137:9998';

const dev = 'https://192.168.108.44:9999';
function proxy(app, url) {
    app.use(createProxyMiddleware('/dispatch-web/cometd', { target: url, secure: false, changeOrigin: true, ws: true}));
    app.use(createProxyMiddleware('/dispatch-web', { target: url, secure: false, changeOrigin: true}));
    app.use(createProxyMiddleware('/scooper-core-rest', { target: url, secure: false, changeOrigin: true }));
    app.use(createProxyMiddleware('/scooper-video', { target: url, secure: false, changeOrigin: true }));
    app.use(createProxyMiddleware('/ecis-access', { target: url, secure: false, changeOrigin: true }));
    app.use(createProxyMiddleware('/snrhtx', { target: url, secure: false, changeOrigin: true }));
}

module.exports = function(app) {
    proxy(app, dev);
};
