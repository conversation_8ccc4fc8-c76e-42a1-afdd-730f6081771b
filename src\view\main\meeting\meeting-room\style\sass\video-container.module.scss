.video-container {
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
  background: #000000;
  overflow: hidden;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
  }

  // 网格布局容器
  .grid-layout-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .grid-container {
    flex: 1;
    width: 100%;
    display: grid;
    gap: 18px;
    padding: 12px;
    // 16:9
    // aspect-ratio: 16/9;
    // 一个窗口
    &.grid-1 {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr;
    }

    &.grid-2 {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr;

      @media (orientation: landscape) {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: 1fr;
      }
    }

    &.grid-4 {
      grid-template-columns: 1fr 1fr; // 4分屏
      grid-template-rows: 1fr 1fr; // 4分屏
    }

    &.grid-6 {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr 1fr;

      @media (orientation: landscape) {
        grid-template-columns: 1fr 1fr 1fr;
        grid-template-rows: 1fr 1fr;
      }
    }
  }

  // 分页控制按钮
  .pagination-controls {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 12px;
    padding: 12px 8px;
    backdrop-filter: blur(10px);

    .page-btn {
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      color: #ffffff;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover:not(:disabled) {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.1);
      }

      &:active:not(:disabled) {
        transform: scale(0.95);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      &.prev-btn:hover:not(:disabled) {
        background: rgba(24, 144, 255, 0.8);
      }

      &.next-btn:hover:not(:disabled) {
        background: rgba(24, 144, 255, 0.8);
      }
    }

    .page-info {
      color: #ffffff;
      font-size: 12px;
      font-weight: 500;
      text-align: center;
      min-width: 40px;

      span {
        display: block;
        line-height: 1.2;
      }
    }
  }

  // 演讲者布局容器
  .speaker-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;

    .main-video-area {
      flex: 1;
      position: relative;
      min-height: 100px;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .thumbnail-area {
      height: 120px;
      background: rgba(0, 0, 0, 0.5);
      padding: 8px;
      
      @media (orientation: landscape) {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 200px;
        height: auto;
        max-height: calc(100% - 32px);
        background: transparent;
        padding: 0;
      }

      .thumbnail-list {
        display: flex;
        gap: 8px;
        height: 100%;
        overflow-x: auto;
        
        @media (orientation: landscape) {
          flex-direction: column;
          overflow-y: auto;
          overflow-x: hidden;
        }
      }
    }
  }

  // 视频窗口
  .video-window {
    position: relative;
    background: #1a1a1a;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;


    &.main-video {
      width: 100%;
      height: 100%;
      cursor: default;
      border-radius: 0;
      display: block;
    }

    &.thumbnail-video {
      min-width: 100px;
      // aspect-ratio: 16/9;
      // box-sizing: border-box;
      
      @media (orientation: landscape) {
        width: 100%;
        min-width: unset;
        margin-bottom: 8px;
      }

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }

      &.active {
        border: 2px solid #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
      }
    }

    .video-element {
      width: 100%;
      height: 100%;
      position: relative;

      .video {
        width: 100%;
        height: 100%;
        object-fit: cover;
        background: #000000;
      }

      .video-placeholder {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

        .avatar-placeholder {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: #1890ff;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 32px;
          font-weight: 600;
          
          .thumbnail-video & {
            width: 40px;
            height: 40px;
            font-size: 16px;
          }
        }
      }
    }

    // 参与者信息覆盖层
    .participant-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(
        transparent 0%,
        rgba(0, 0, 0, 0.3) 50%,
        rgba(0, 0, 0, 0.7) 100%
      );
      padding: 16px 12px 8px;
      color: #ffffff;

      .thumbnail-video & {
        padding: 8px 6px 4px;
      }

      .participant-info {
        display: flex;
        justify-content: space-between;
        align-items: flex-end;

        .participant-name {
          font-size: 14px;
          font-weight: 500;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          
          .thumbnail-video & {
            font-size: 12px;
          }

          .host-badge {
            background: #1890ff;
            color: #ffffff;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 6px;
            
            .thumbnail-video & {
              font-size: 8px;
              padding: 1px 4px;
            }
          }
        }

        .participant-status {
          display: flex;
          gap: 4px;
          font-size: 16px;
          
          .thumbnail-video & {
            font-size: 12px;
          }

          .muted-indicator,
          .video-off-indicator {
            opacity: 0.8;
          }
        }
      }
    }

    // 主持人控制按钮
    .host-controls {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      gap: 4px;
      z-index: 15;

      .control-btn {
        width: 32px;
        height: 32px;
        background: rgba(0, 0, 0, 0.7);
        border: none;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.9);
          transform: scale(1.1);
        }

        &:active {
          transform: scale(0.95);
        }

        // 禁言按钮特殊样式
        &:first-child {
          &:hover {
            background: rgba(255, 193, 7, 0.9);
          }
        }

        // 踢出按钮特殊样式
        &:last-child {
          &:hover {
            background: rgba(220, 53, 69, 0.9);
          }
        }
      }
    }

    // 全屏按钮
    .fullscreen-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.5);
      color: #ffffff;
      font-size: 16px;
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(0, 0, 0, 0.7);
        transform: scale(1.1);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  // 空状态容器
  .empty-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-align: center;

    .empty-message {
      h3 {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #ffffff;
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.7);
        margin: 0;
      }
    }
  }

  // 网络状态指示器
  .network-indicator {
    position: absolute;
    top: 16px;
    left: 16px;
    z-index: 50;

    .signal-bars {
      display: flex;
      gap: 2px;
      align-items: flex-end;

      .bar {
        width: 3px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 1px;
        transition: background-color 0.3s ease;

        &:nth-child(1) { height: 8px; }
        &:nth-child(2) { height: 12px; }
        &:nth-child(3) { height: 16px; }
        &:nth-child(4) { height: 20px; }

        &.active {
          background: #52c41a;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .video-window {
    .host-controls {
      top: 4px;
      right: 4px;
      gap: 2px;

      .control-btn {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }

    .fullscreen-btn {
      top: 8px;
      right: 8px;
      width: 32px;
      height: 32px;
      font-size: 14px;
    }
  }

  // 移动端分页控制按钮适配
  .pagination-controls {
    right: 8px;
    padding: 8px 6px;
    gap: 8px;

    .page-btn {
      width: 32px;
      height: 32px;
      font-size: 14px;
    }

    .page-info {
      font-size: 10px;
      min-width: 32px;
    }
  }
}

// 小屏幕横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .video-window {
    .host-controls {
      top: 2px;
      right: 2px;
      gap: 1px;

      .control-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
      }
    }

    .fullscreen-btn {
      top: 4px;
      right: 4px;
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
  }
}
