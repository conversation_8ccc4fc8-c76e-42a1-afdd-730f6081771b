/**
 * @Create: 周颖仁
 * @Date: 2023/8/14
 * @desc: 项目接口存放
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/8/14
 */

import getServices from './axios-request';

export const scooper_core_rest_api = getServices('/scooper-core-rest', () => localStorage.getItem('scooperCoreToken'), {
	// 层级加载部门
	listDeptByParent: { url: '/data/contacts/orgDeptManage/listDeptByParent', type: 'get' },
	// 根据关键字查询部门成员
	listOrgMember: { url: '/data/contacts/orgMemberManage/listOrgMember', type: 'get' },
	// 根据人员id查询详情
	getOrgMemberDetail: { url: '/data/contacts/orgMemberManage/getOrgMemberDetail', type: 'get' },
	// 获取所有人员数据
	queryOrgMember: { url: '/data/contacts/orgMemberManage/queryOrgMember', type: 'get' },
})

export const dispatch_web_api = getServices('/dispatch-web', () => localStorage.getItem('scooperCoreToken'), {
	// 查询呼叫记录
	queryCallRecord: { url: '/data/serCallRecord/queryCallRecord', type: 'get' },
	// 查询呼入未接
	queryCallInMiss: { url: '/data/serCallRecord/queryCallInMiss', type: 'get' },
	// 调度登录
	login: { url: '/api/login/login', type: 'post' },
	// 查询指定号码状态
	telsStatus: { url: '/api/call/telsStatus', type: 'post' },
	// 查询历史会议
	listMeetsHistory: { url: '/api/meet/listMeetsHistory', type: 'get', getResp: true },

	// 查询我的会议列表（正在进行中的会议）
	listMyMeets: { url: '/api/meet/listMyMeets', type: 'get', getResp: true },

	createMeet:{url:'/api/meet/createMeet',type:'get',getResp:true}, // 创建会议

	destroyMeet:{url:'/api/meet/destroyMeet',type:'get',getResp:true}, // 销毁会议
 
	getMeetByMeetAccess:{url:'/api/meet/getMeetByMeetAccess',type:'get',getResp:true}, // 根据meetAccess查询会议

	joinVideoMember:{url:'/api/meet/joinVideoMember',type:'get',getResp:true}, // 加入会议

	getMeetAdd:{url:'/api/meet/getMeetAdd',type:'get',getResp:true},   

	kickMember:{url:'/api/meet/kickMember',type:'get',getResp:true}, // 踢出成员

	changeMemberLevel:{url:'/api/meet/changeMemberLevel',type:'get',getResp:true}, // 设置成员入会时是否禁言

	listCallStatus: { url: '/api/call/listCallStatus', type: 'post' }, //获取设备状态

})

export const ecis_access_api = getServices('/ecis-access', () => localStorage.getItem('scooperCoreToken'), {
	// 根据openId获取用户信息
	getAccountByOpenId: { url: '/data/syncAddress/getAccountByOpenId', type: 'get' },
	// 发送消息
	publishMsg: { url: '/data/syncAddress/publishMsg', type: 'post' },
})

export const scooper_video_api = getServices('/scooper-video', () => localStorage.getItem('scooperCoreToken'), {
	// scooper-video/data/login/loginTo
	login: { url: '/data/login/loginTo', type: 'post' },	
})


// /snrhtx/gw/sso/loginH5?code=xxxxx
export const snrhtx_gw_sso_api = getServices('/snrhtx/gw/sso', () => localStorage.getItem('scooperCoreToken'), {
	// 登录
	loginH5: { url: '/loginH5', type: 'get' },
})

