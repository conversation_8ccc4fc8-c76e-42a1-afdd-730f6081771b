/**
 * @Create: 会议室组件
 * @Date: 2025/01/28
 * @desc: 移动端会议室主组件，基于meeting-mobile-standalone改写
 */

import React, { useEffect, useState, useRef, useContext, useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { message, Modal } from 'antd';
import { debounce } from 'lodash';
import style from './style/sass/index.module.scss';
import VideoContainer from './components/VideoContainer';
import ParticipantsList from './components/ParticipantsList';
import BottomControls from './components/BottomControls';
import { dispatch_web_api } from "../../../../util/api";
import { useCometDPubSub } from "../../../../component/useCometDPubSub";
import PubSub from 'pubsub-js';
import { MainContext } from '../../context';
import { initRequireErrorHandler, cleanupRequireErrorHandler } from '../../../../util/requireErrorHandler';


const { confirm } = Modal;

function MeetingRoom() {
  const navigate = useNavigate();
  const location = useLocation();
  const meetingData = location.state?.meetingData || {};
  const cometd = useCometDPubSub();
  const { isConnected, subscribe, TOPICS } = cometd;
  const { allMemberInfo,registerFailed } = useContext(MainContext);
 
  
  // 会议状态
  const [meetingState, setMeetingState] = useState({
    isAudio: true,
    isVideo: true,
    isSpeaker: true,
    isConnected: isConnected,
    meetName: meetingData.meetName || '会议室',
    meetId: meetingData.meetId || '',
    participants: [],
    timeBegin: null,
    isMuteAll: false
  });

  // 会议时长状态
  const [meetingDuration, setMeetingDuration] = useState('00:00:00');

  // UI状态
  const [uiState, setUiState] = useState({
    showParticipants: false,
    isFullScreen: false,
    showControls: true
  });

  // 全体禁言操作状态
  const [isMuteAllProcessing, setIsMuteAllProcessing] = useState(false);

  // 定时器引用
  const controlsTimerRef = useRef(null);
  const connectionRef = useRef(null);
  const durationTimerRef = useRef(null);

  // 错误处理和初始化
  useEffect(() => {
    // 初始化 RequireJS 错误处理
    initRequireErrorHandler();

    // 初始化会议
    initializeMeeting();
    setupViewportHeight();

    // 清理函数
    return () => {
      cleanupRequireErrorHandler();
      cleanupMeeting();
    };
  }, []);

  // 计算会议时长
  const calculateMeetingDuration = (timeBegin) => {
    if (!timeBegin) return '00:00:00';

    const startTime = new Date(timeBegin);
    const currentTime = new Date();
    const diffMs = currentTime - startTime;

    if (diffMs < 0) return '00:00:00';

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // 启动会议时长计时器
  const startDurationTimer = (timeBegin) => {
    if (durationTimerRef.current) {
      clearInterval(durationTimerRef.current);
    }

    if (!timeBegin) return;

    // 立即更新一次
    setMeetingDuration(calculateMeetingDuration(timeBegin));

    // 每秒更新一次
    durationTimerRef.current = setInterval(() => {
      setMeetingDuration(calculateMeetingDuration(timeBegin));
    }, 1000);
  };

  // 停止会议时长计时器
  const stopDurationTimer = () => {
    if (durationTimerRef.current) {
      clearInterval(durationTimerRef.current);
      durationTimerRef.current = null;
    }
  };

  // 监听会议开始时间变化
  useEffect(() => {
    if (meetingState.timeBegin) {
      console.log('会议开始时间:', meetingState.timeBegin);
      startDurationTimer(meetingState.timeBegin);
    } else {
      stopDurationTimer();
    }

    // 清理函数
    return () => {
      stopDurationTimer();
    };
  }, [meetingState.timeBegin]);

  useEffect(()=>{

    console.log('meetingState',meetingState)
  },[meetingState])

  useEffect(()=>{
    const subscription = PubSub.subscribe('会场加入', (_, data) => {
      console.log('人员会场加入:', data);
      // 判断id是否相同，如果相同更新状态
      if(meetingState.participants.some(item => item.id === data.tel)){
        setMeetingState(prev => ({
          ...prev,
          participants: prev.participants.map(p => p.id === data.tel ? { ...p, isAudioOn: data.level === 'speak' } : p)
        }));
      }else{
        setMeetingState(prev => ({
          ...prev,
          participants: [...prev.participants, {
            id: data.tel || data.id,
            name: data.name || allMemberInfo?.current[data.tel]?.memName || data.tel,
            isHost: meetingState.ctrlTel == data.tel,
            isAudioOn: true,
            isVideoOn: true,
            isVideo: data.videoInfo === 'video'
          }]  
        }));
      }


      
    })

    const videoCloseScr = PubSub.subscribe('视频关闭', (_, data) => {
      console.log('视频关闭:', data);
      setMeetingState(prev => ({
        ...prev,
        participants: prev.participants.filter(p => p.id !== data.tel)
      }));
    })

    const memberStatusScr = PubSub.subscribe('会场成员状态', (_, data) => {
      console.log('会场成员状态:', data);
      if(data.meetLevel===6){
        return
      }else{
        setMeetingState(prev => ({
          ...prev,
          participants: prev.participants.map(p => p.id === data.tel ? { ...p, isAudioOn: data.level === 'speak' } : p)
        }));
      }
    })

    const memScr = PubSub.subscribe('MEET_MEM', (_, data) => {
      console.log('会议成员变化:', data);
      if (data.type === 'leave') {
        setMeetingState(prev => ({
          ...prev,
          participants: prev.participants.filter(p => p.id !== data.tel)
        }));
      }
    })

    return () => {
      PubSub.unsubscribe(subscription);
      PubSub.unsubscribe(memScr);
      PubSub.unsubscribe(videoCloseScr);
      PubSub.unsubscribe(memberStatusScr);
    };
  },[meetingState])

  // CometD 事件监听
  // useEffect(() => {
  //   if (!isConnected) return;

  //   const subscriptions = [];
  //   subscriptions.push(
  //     subscribe(TOPICS.MEET_JOIN, (_, data) => {
  //       console.log('成员加入会议:', data);
  //       setMeetingState(prev => ({
  //         ...prev,
  //         participants: [...prev.participants, {
  //           id: data.data.tel || data.data.id,
  //           name: data.data.name || data.data.tel,
  //           isHost: false,
  //           isAudioOn: true,
  //           isVideoOn: true
  //         }]
  //       }));
  //     })
  //   );

  //   // 会议成员离开
  //   subscriptions.push(
  //     subscribe(TOPICS.MEET_LEAVE, (_, data) => {
  //       console.log('成员离开会议:', data);
  //       setMeetingState(prev => ({
  //         ...prev,
  //         participants: prev.participants.filter(p =>
  //           p.id !== (data.data.tel || data.data.id)
  //         )
  //       }));
  //     })
  //   );

  //   // 会议成员状态变化
  //   subscriptions.push(
  //     subscribe(TOPICS.MEET_MEMBER_STATUS, (_, data) => {
  //       console.log('成员状态变化:', data);
  //       setMeetingState(prev => ({
  //         ...prev,
  //         participants: prev.participants.map(p =>
  //           p.id === (data.data.tel || data.data.id)
  //             ? { ...p, isAudioOn: data.data.isAudioOn, isVideoOn: data.data.isVideoOn }
  //             : p
  //         )
  //       }));
  //     })
  //   );

  //   // 分屏设置变化
  //   subscriptions.push(
  //     subscribe(TOPICS.MEET_SPLIT_SCREEN_SET, (_, data) => {
  //       console.log('分屏设置变化:', data);
  //       // 这里可以处理分屏变化逻辑
  //     })
  //   );

  //   // 清理订阅
  //   return () => {
  //     subscriptions.forEach(sub => {
  //       if (sub) {
  //         // 注意：这里需要根据实际的 unsubscribe 方法调用
  //         console.log('清理 CometD 订阅');
  //       }
  //     });
  //   };
  // }, [isConnected, subscribe, TOPICS]);

  // 设置视口高度
  const setupViewportHeight = () => {
    const setHeight = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--vh', `${vh}px`);
    };
    window.addEventListener('resize', setHeight);
    setHeight();
    
    return () => window.removeEventListener('resize', setHeight);
  };

  // 初始化会议
  const initializeMeeting = async () => {
    try {
      console.log('初始化会议:', meetingData);
      const ownerInformation = JSON.parse(sessionStorage.getItem('ownerInformation'));
      dispatch_web_api.getMeetAdd({meetId: meetingData.meetId}).then(res => {
        console.log(res)
        if (res.code === 0 && res.data.members.length > 0) {
          sessionStorage.setItem('curParam', JSON.stringify(res.data))
          setMeetingState(prev => ({ 
            ...prev,
              isConnected: true, // 是否链接
              participants: res.data.members.filter(item => (item.status === 'callst_meet'||item.status === 'inMeet')&&(item.tel !== localStorage.getItem('scooperOwnerInformation').mainTel)).map(item => ({
                id: item.tel,
                name: item.memName, 
                isHost: res.data.ctrlTel === item.tel,
                isAudioOn: item.meetMemLevel === 'speak',
                isVideoOn: item.type === 'video',
              })),
              // res.data
              meetId: res.data.meetId,
              meetName: res.data.meetName,
              ctrlTel: res.data.ctrlTel,
              passwdSpeaker: res.data.passwdSpeaker,
              passwdAudience: res.data.passwdAudience,
              meetAccess: res.data.meetAccess,
              timeBegin:res.data.timeBegin,
          }))
        }
      })

      message.success('已加入会议');
    } catch (error) {
      console.error('初始化会议失败:', error);
      message.error('加入会议失败');
    }
  };

  // 清理会议资源
  const cleanupMeeting = () => {
    if (controlsTimerRef.current) {
      clearTimeout(controlsTimerRef.current);
    }

    // 清理会议时长计时器
    stopDurationTimer();

    // 清理会议连接
    if (connectionRef.current) {
      // 这里添加实际的会议连接清理逻辑
      console.log('清理会议连接');
    }
    // 清理视频流
    // if (videoStreamRef.current) {
    //   videoStreamRef.current.getTracks().forEach(track => track.stop());
    // }
  };

  const clearMeet = () => {
    // 关闭视频
    window.videoController.destroy();
    if(meetingState.ctrlTel === JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel){
      dispatch_web_api.destroyMeet({id: meetingData.meetId}).then(res => {
        console.log(res)
        navigate('/main/meeting');
        message.info('已关闭会议');
      })
    }else{
      navigate('/main/meeting');
      message.info('已离开会议');
    }
  }

  // 邀请参与者
  const handleInviteParticipants = (invitedUsers) => {
    console.log('邀请参与者',invitedUsers);
    // 这里可以实现邀请逻辑，比如：
    // 1. 显示联系人选择弹窗
    // 2. 生成邀请链接
    // 3. 发送邀请消息等
  };

  // 全体禁言/取消全体禁言
  const handleMuteAll = useCallback(async () => {
    // 如果正在处理中，直接返回
    if (isMuteAllProcessing) {
      console.log('全体禁言操作正在处理中，跳过重复请求');
      return;
    }

    setIsMuteAllProcessing(true);
    const newMuteState = !meetingState.isMuteAll;
    console.log('全体禁言状态切换:', newMuteState);

    try {
      debugger
      // 批量处理所有参与者的禁言状态
      const promises = meetingState.participants.map(item =>
        dispatch_web_api.changeMemberLevel({
          id: meetingData.meetId,
          tel: item.id,
          level: newMuteState ? 'audience' : 'speak'
        }).then(() => {
          console.log(`设置 ${item.name || item.tel} 发言状态成功`);
          return item;
        }).catch(error => {
          console.error(`设置 ${item.name || item.tel} 发言状态失败:`, error);
          throw error;
        })
      );

      await Promise.all(promises);

      // 更新会议状态
      setMeetingState(prev => ({
        ...prev,
        isMuteAll: newMuteState,
        participants: prev.participants.map(p => ({
          ...p,
          level: newMuteState ? 'audience' : 'speak'
        }))
      }));

      message.success(newMuteState ? '已开启全体禁言' : '已取消全体禁言');
    } catch (error) {
      console.error('全体禁言操作失败:', error);
      message.error('操作失败，请重试');
    } finally {
      setIsMuteAllProcessing(false);
    }
  }, [isMuteAllProcessing, meetingState.isMuteAll, meetingState.participants, meetingData.meetId]);

  // 创建防抖版本的处理函数
  const debouncedHandleMuteAll = useMemo(
    () => debounce(handleMuteAll, 1000, {
      leading: true,  // 立即执行第一次
      trailing: false // 不执行最后一次
    }),
    [handleMuteAll]
  );

  // 切换音频
  const toggleAudio = () => {
    
    const level = !meetingState.isAudio ? 'speak' : 'audience';
    const tel = JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel;
    // 这里添加实际的音频控制逻辑
    dispatch_web_api.changeMemberLevel({id:meetingData.meetId,tel:tel,level:level}).then(res=>{
      console.log('设置是否发言成功')
      setMeetingState(prev => ({
        ...prev,
        isAudio: !prev.isAudio
      }));
    })
    console.log('切换音频:', !meetingState.isAudio);
  };

  // 切换视频
  const toggleVideo = () => {
    setMeetingState(prev => ({
      ...prev,
      isVideo: !prev.isVideo
    }));
    
    window.shandleUtil.enableCamera(!meetingState.isVideo);
    // 这里添加实际的视频控制逻辑
    console.log('切换视频:', !meetingState.isVideo);
  };

  // 切换扬声器
  const toggleSpeaker = () => {
    setMeetingState(prev => ({
      ...prev,
      isSpeaker: !prev.isSpeaker
    }));
    
    console.log('切换扬声器:', !meetingState.isSpeaker);
  };

  // 显示参与者列表
  const showParticipants = () => {
    setUiState(prev => ({
      ...prev,
      showParticipants: true
    }));
  };


  // 切换全屏
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setUiState(prev => ({ ...prev, isFullScreen: true }));
    } else {
      document.exitFullscreen();
      setUiState(prev => ({ ...prev, isFullScreen: false }));
    }
  };

  // 离开会议
  const leaveMeeting = () => {
    confirm({
      title: '确认离开会议？',
      content: '离开后需要重新加入会议',
      okText: '确认',
      cancelText: '取消',
      onOk() {
        cleanupMeeting();
        clearMeet();

        // navigate(-1);
        
      },
    });
  };

  // 自动隐藏控制栏
  const handleUserInteraction = () => {
    setUiState(prev => ({ ...prev, showControls: true }));
    
    if (controlsTimerRef.current) {
      clearTimeout(controlsTimerRef.current);
    }
    
    controlsTimerRef.current = setTimeout(() => {
      setUiState(prev => ({ ...prev, showControls: false }));
    }, 50000); // 50秒后隐藏控制栏
  };

  // 触摸事件处理
  useEffect(() => {
    const handleTouch = () => handleUserInteraction();
    document.addEventListener('touchstart', handleTouch);
    document.addEventListener('click', handleTouch);
    
    return () => {
      document.removeEventListener('touchstart', handleTouch);
      document.removeEventListener('click', handleTouch);
    };
  }, []);

  return (
    <div className={style['meeting-room']}>
      {/* 视频容器 */}
      <VideoContainer
        participants={meetingState.participants}
        isFullScreen={uiState.isFullScreen}
        onToggleFullScreen={toggleFullScreen}
        meetingData={meetingState}
        isHost={meetingState.ctrlTel === JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel}
      />

      {/* 会议信息栏 */}
      {uiState.showControls && (
        <div className={style['meeting-header']}>
          <div className={style['meeting-info']}>
            <h3 className={style['meeting-title']}>{meetingState.meetName}</h3>
            <span className={style['meeting-status']}>
              {meetingState.isConnected ? '已连接' : '连接中...'}
            </span>
          </div>
          <div className={style['meeting-time']}>
            <span className={style['duration-label']}>会议时长：</span>
            <span className={style['duration-value']}>{meetingDuration}</span>
          </div>
        </div>
      )}

      {/* 底部控制栏 */}
      {uiState.showControls && (
        <BottomControls
          isAudio={meetingState.isAudio}
          isVideo={meetingState.isVideo}
          isSpeaker={meetingState.isSpeaker}
          onToggleAudio={toggleAudio} // 切换音频
          onToggleVideo={toggleVideo} // 切换视频
          onToggleSpeaker={toggleSpeaker} // 切换扬声器
          onShowParticipants={showParticipants} // 显示参与者列表
          onLeaveMeeting={leaveMeeting} // 离开会议
        />
      )}

      {/* 参与者列表弹窗 */}
      <ParticipantsList
        visible={uiState.showParticipants}
        participants={meetingState.participants}
        onClose={() => setUiState(prev => ({ ...prev, showParticipants: false }))}
        isHost={meetingState.ctrlTel === JSON.parse(localStorage.getItem('scooperOwnerInformation')).mainTel}
        isMuteAll={meetingState.isMuteAll || false}
        isMuteAllProcessing={isMuteAllProcessing}
        meetingDuration={meetingDuration}
        onInvite={handleInviteParticipants}
        onMuteAll={debouncedHandleMuteAll}
        meetingData={meetingState}
      />

      {/* 连接状态指示器 */}
      {!meetingState.isConnected && (
        <div className={style['connection-indicator']}>
          <div className={style['loading-spinner']}></div>
          <span>正在连接会议...</span>
          
        </div>
      )}
    </div>
  );
}

export default MeetingRoom;
