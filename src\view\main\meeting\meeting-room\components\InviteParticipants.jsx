/**
 * @Create: 邀请参会人组件
 * @Date: 2025/01/28
 * @desc: 选择和邀请参会人员
 */

import React, { useState, useEffect } from 'react';
import { 
  Modal, 
  Input, 
  List, 
  Avatar, 
  Button, 
  message,
  Divider,
  Tag
} from 'antd';
import { 
  SearchOutlined, 
  UserOutlined, 
  TeamOutlined,
  ArrowLeftOutlined,
  CheckOutlined
} from '@ant-design/icons';
import { dispatch_web_api, scooper_core_rest_api } from '../../../../../util/api';
import style from '../style/sass/invite-participants.module.scss';

const { Search } = Input;

function InviteParticipants({ 
  visible, 
  onClose, 
  meetingData = {},
  onInviteSuccess 
}) {
  const [searchText, setSearchText] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [currentDeptDataList, setCurrentDeptDataList] = useState([]);
  const [searchPersonList, setSearchPersonList] = useState([]);
  const [breadcrumbList, setBreadcrumbList] = useState([]);
  const [loading, setLoading] = useState(false);
  const [inviting, setInviting] = useState(false);

  // 获取部门颜色
  const getDepartmentColor = (index) => {
    const colors = ['#1890ff', '#52c41a', '#722ed1', '#fa8c16', '#13c2c2', '#eb2f96'];
    return colors[index % colors.length];
  };

  // 初始化面包屑和部门数据
  useEffect(() => {
    if (visible) {
      setSelectedUsers([]);
      setSearchText('');
      setCurrentDeptDataList([]);
      setSearchPersonList([]);

      // 初始化面包屑为通讯录根目录
      setBreadcrumbList([{
        name: '通讯录',
        deptId: 0,
        id: 0
      }]);
    }
  }, [visible]);

  // 根据面包屑加载部门数据
  useEffect(() => {
    if (!breadcrumbList.length) return;

    setCurrentDeptDataList([]);
    const currentDept = breadcrumbList[breadcrumbList.length - 1];
    setLoading(true);

    const params = {
      id: currentDept.deptId,
      expandMember: currentDept.deptId !== 0 // 根目录不展开成员，只显示部门
    };

    scooper_core_rest_api.listDeptByParent(params).then(data => {
      setCurrentDeptDataList(data || []);
      setLoading(false);
    }).catch(error => {
      console.log(`listDeptByParent: ${error}`);
      setLoading(false);
    });
  }, [breadcrumbList]);

  // 搜索用户
  const handleSearch = (value) => {
    setSearchText(value);
    if (value.trim()) {
      setLoading(true);
      const param = {
        keyword: value
      };

      scooper_core_rest_api.listOrgMember(param).then(data => {
        setSearchPersonList(data || []);
        setLoading(false);
      }).catch(error => {
        console.log(`listOrgMember: ${error}`);
        setLoading(false);
      });
    } else {
      setSearchPersonList([]);
    }
  };

  // 面包屑点击事件
  const handleBreadcrumbClick = (breadcrumb) => {
    const index = breadcrumbList.findIndex(item => item.deptId === breadcrumb.deptId);
    if (index !== breadcrumbList.length - 1) {
      const newBreadcrumbList = breadcrumbList.slice(0, index + 1);
      setBreadcrumbList(newBreadcrumbList);
    }
  };

  // 列表项点击事件（部门或人员）
  const handleListItemClick = (item) => {
    if (item.dataType === 'orgDept') {
      // 点击部门，添加到面包屑
      const newBreadcrumbList = [...breadcrumbList, {
        name: item.name,
        deptId: item.id,
        id: item.id
      }];
      setBreadcrumbList(newBreadcrumbList);
    } else if (item.dataType === 'orgMember') {
      // 点击人员，切换选择状态
      toggleUserSelection({
        id: item.id,
        name: item.name,
        tel: item.memTel || item.tel,
        department: getCurrentDepartmentName()
      });
    }
  };

  // 获取当前部门名称
  const getCurrentDepartmentName = () => {
    if (breadcrumbList.length === 0) return '通讯录';
    const currentDept = breadcrumbList[breadcrumbList.length - 1];
    return currentDept.name;
  };

  // 切换用户选择状态
  const toggleUserSelection = (user) => {
    setSelectedUsers(prev => {
      const isSelected = prev.some(u => u.id === user.id);
      if (isSelected) {
        return prev.filter(u => u.id !== user.id);
      } else {
        return [...prev, user];
      }
    });
  };

  // 发送邀请
  const handleInvite = async () => {
    if (selectedUsers.length === 0) {
      message.warning('请选择要邀请的参会人员');
      return;
    }

    setInviting(true);
    try {
      // 调用邀请API
      const invitePromises = selectedUsers.map(user =>
        // dispatch_web_api.joinVideoMember({
        //   meetId: meetingData.meetId,
        //   tel: user.tel,
        //   name: user.name,
        //   meetAccess: meetingData.meetAccess || meetingData.meetAccess
        // })
        dispatch_web_api.joinVideoMember({
          id: meetingData.meetId,
          tel: user.tel,
          level: 'speak',
          businessId: '',
          autoAnswer: '',
          meetAccess: meetingData.meetAccess
        })
      );

      await Promise.all(invitePromises);
      
      message.success(`已成功邀请 ${selectedUsers.length} 位参会人员`);
      onInviteSuccess && onInviteSuccess(selectedUsers);
      onClose();
    } catch (error) {
      console.error('邀请失败:', error);
      message.error('邀请失败，请重试');
    } finally {
      setInviting(false);
    }
  };

  // 渲染当前部门内容（部门和人员）
  const renderCurrentDeptContent = () => {
    const isRootDirectory = breadcrumbList.length === 1 && breadcrumbList[0].deptId === 0;

    return (
      <div className={style['department-list']}>
        <div className={style['section-title']}>
          {isRootDirectory ? '选择部门' : getCurrentDepartmentName()}
        </div>

        {currentDeptDataList.map((item, index) => (
          <div
            key={`${item.dataType}-${item.id}`}
            className={style['department-item']}
            onClick={() => handleListItemClick(item)}
          >
            <div className={style['department-info']}>
              <Avatar
                size={40}
                style={{ backgroundColor: item.dataType === 'orgDept' ? getDepartmentColor(index) : '#1890ff' }}
                icon={item.dataType === 'orgDept' ? <TeamOutlined /> : <UserOutlined />}
              />
              <div className={style['department-details']}>
                <div className={style['department-name']}>{item.name}</div>
                {item.dataType === 'orgMember' && item.memTel && (
                  <div className={style['department-count']}>({item.memTel})</div>
                )}
              </div>
            </div>
            <div className={style['arrow-icon']}>
              {item.dataType === 'orgDept' ? '›' : (
                selectedUsers.some(u => u.id === item.id) ? <CheckOutlined /> : null
              )}
            </div>
          </div>
        ))}

        {currentDeptDataList.length === 0 && !loading && (
          <div className={style['empty-state']}>
            <p>{isRootDirectory ? '暂无部门数据' : '该部门暂无成员'}</p>
          </div>
        )}
      </div>
    );
  };

  // 渲染搜索结果
  const renderSearchResults = () => (
    <div className={style['user-list']}>
      <div className={style['section-title']}>
        搜索结果 ({searchPersonList.length})
      </div>

      {searchPersonList.map(person => {
        const isSelected = selectedUsers.some(u => u.id === person.id);
        return (
          <div
            key={person.id}
            className={`${style['user-item']} ${isSelected ? style['selected'] : ''}`}
            onClick={() => toggleUserSelection({
              id: person.id,
              name: person.name,
              tel: person.memTel || person.tel,
              department: person.deptName || '未知部门'
            })}
          >
            <div className={style['user-info']}>
              <Avatar
                size={40}
                style={{ backgroundColor: '#1890ff' }}
                icon={<UserOutlined />}
              >
                {person.name?.charAt(0) || 'U'}
              </Avatar>
              <div className={style['user-details']}>
                <div className={style['user-name']}>{person.name || person.memName}</div>
                <div className={style['user-tel']}>{person.memTel || person.tel}</div>
                {person.deptName && (
                  <div className={style['user-dept']}>{person.deptName}</div>
                )}
              </div>
            </div>
            {isSelected && (
              <CheckOutlined className={style['check-icon']} />
            )}
          </div>
        );
      })}

      {searchPersonList.length === 0 && searchText && !loading && (
        <div className={style['empty-state']}>
          <p>未找到相关人员</p>
        </div>
      )}
    </div>
  );

  return (
    <Modal
      title="添加参会人"
      open={visible}
      onCancel={onClose}
      width={400}
      className={style['invite-modal']}
      footer={[
        <div key="footer" className={style['modal-footer']}>
          <div className={style['selected-info']}>
            已选择({selectedUsers.length}): {selectedUsers.map(u => u.name).join(', ')}
          </div>
          <Button 
            type="primary" 
            onClick={handleInvite}
            loading={inviting}
            disabled={selectedUsers.length === 0}
          >
            确定
          </Button>
        </div>
      ]}
    >
      <div className={style['invite-content']}>
        {/* 搜索框 */}
        <div className={style['search-section']}>
          <Search
            placeholder="搜索"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onSearch={handleSearch}
            prefix={<SearchOutlined />}
            className={style['search-input']}
          />
        </div>

        {/* 面包屑导航 */}
        <div className={style['breadcrumb']}>
          <span className={style['breadcrumb-path']}>
            {breadcrumbList.map((item, index) => (
              <span key={item.deptId}>
                <span
                  className={style['breadcrumb-item']}
                  onClick={() => handleBreadcrumbClick(item)}
                  style={{
                    cursor: index !== breadcrumbList.length - 1 ? 'pointer' : 'default',
                    color: index !== breadcrumbList.length - 1 ? '#1890ff' : '#333'
                  }}
                >
                  {item.name}
                </span>
                {index < breadcrumbList.length - 1 && ' > '}
              </span>
            ))}
          </span>
        </div>

        {/* 内容区域 */}
        <div className={style['content-area']}>
          {searchText ? renderSearchResults() : renderCurrentDeptContent()}
        </div>
      </div>
    </Modal>
  );
}

export default InviteParticipants;
