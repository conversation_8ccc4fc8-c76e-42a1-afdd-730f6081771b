/**
 * @Create: 周颖仁
 * @Date: 2023/11/7
 * @desc: 电话接通中
 * @LastEditors: 周颖仁
 * @LastEditTime: 2023/11/7
 */

import React, {useContext, useEffect, useRef, useState} from "react";
import style from '../style/sass/call-ing.module.scss';
import {MainContext} from "../context";
import {message} from "antd";

function CallIng() {

	const { nonOwnerInformation, allMemberInfo } = useContext(MainContext);

	const [isMute, setIsMute] = useState(false);
	const [isSpeaker, setIsSpeaker] = useState(true);
	const [duringTime, setDuringTime] = useState('');

	const duringTimeRef = useRef(0);

	useEffect(() => {
		window.shandleUtil.updateVoiceSpeaker(true);
	}, []);

	/**
	 * @desc 切换是否静音
	 */
	function setDeviceMute() {
		window.shandleUtil.audioClose(!isMute);
		setIsMute(!isMute);
	}

	/**
	 * @desc 挂断电话
	 */
	function hangupCall() {
		window.shandleUtil.hungUp();
	}

	/**
	 * @desc 切换扬声器
	 */
	function changeSpeaker() {
		if (!window.shandleUtil.getSHandle().deviceControl.hasMultiVoiceDevice) {
			message.error("当前设备不支持切换");
			return
		}
		window.shandleUtil.updateVoiceSpeaker(!isSpeaker)
		setIsSpeaker(!isSpeaker);
		// window.shandleUtil.getSHandle().deviceControl.toggleVoiceDevice().then(() => {
		// 	setIsSpeaker(!isSpeaker);
		// }).catch(error => {
		// 	message.error(error);
		// });
	}

	/**
	 * @desc 定时器
	 */
	useEffect(() => {
		duringTimeRef.current = 1;
		setDuringTime('00:01');
		setInterval(() => {
			duringTimeRef.current = duringTimeRef.current + 1;
			let hours = Math.floor(duringTimeRef.current / 3600);
			let minutes =  Math.floor((duringTimeRef.current - (hours * 3600)) / 60);
			let seconds = duringTimeRef.current % 60;
			if(!hours) {
				setDuringTime(`${String(minutes).padStart(2, '0')} : ${String(seconds).padStart(2, '0')}`);
			}else {
				setDuringTime(`${String(hours).padStart(2, '0')} : ${String(minutes).padStart(2, '0')} : ${String(seconds).padStart(2, '0')}`);
			}
		}, 1000)
	}, []);

	return(
		<div className={style.callIng}>
			<div className={style.callOutHeader}>
				<span className={style.memName}>{nonOwnerInformation.memName || allMemberInfo.current[nonOwnerInformation.telNumber]?.memName || nonOwnerInformation.telNumber}</span>
				<span className={style.defaultTip}>{ duringTime }</span>
			</div>
			<div className={style.callOutActionContent}>
				<span className={`${style.iconMute} ${isMute? style.isMute: ''}`} onClick={() => setDeviceMute()}></span>
				<span className={style.iconHungUp} onClick={() => hangupCall()}></span>
				<span className={`${style.iconSpeaker} ${!isSpeaker? style.isSpeaker: ''}`} onClick={() => changeSpeaker()}></span>
			</div>
		</div>
	)
}

export default CallIng;

