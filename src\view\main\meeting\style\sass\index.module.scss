.meeting {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  flex-direction: column;

  .action-buttons {
    background: #ffffff;
    padding: 20px 16px;
    border-bottom: 1px solid #f0f0f0;

    .button-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 12px;
    }

    .action-button {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 16px 8px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      min-height: 80px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .button-icon {
        font-size: 24px;
        color: #1890ff;
        margin-bottom: 8px;
        
        &:first-child {
          color: #52c41a; // 发起会议 - 绿色
        }
        
        &:nth-child(2) {
          color: #1890ff; // 加入会议 - 蓝色
        }
        
        &:nth-child(3) {
          color: #faad14; // 预约会议 - 橙色
        }
        
        &:nth-child(4) {
          color: #722ed1; // 录制文件 - 紫色
        }
      }

      .button-text {
        font-size: 12px;
        color: #333333;
        font-weight: 500;
        text-align: center;
      }

      // 为不同按钮设置不同的图标颜色
      &:nth-child(1) .button-icon {
        color: #52c41a; // 发起会议 - 绿色
      }
      
      &:nth-child(2) .button-icon {
        color: #1890ff; // 加入会议 - 蓝色
      }
      
      &:nth-child(3) .button-icon {
        color: #faad14; // 预约会议 - 橙色
      }
      
      &:nth-child(4) .button-icon {
        color: #722ed1; // 录制文件 - 紫色
      }
    }
  }

  .meeting-list-container {
    height: 42%;
    background: #ffffff;
    margin-top: 4px;
    display: flex;
    flex-direction: column;

    .list-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      background: #ffffff;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .meeting-count {
        font-size: 14px;
        color: #666666;
        font-weight: normal;
        margin-left: 8px;
      }

      .refresh-button {
        font-size: 16px;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.3s ease;
        user-select: none;

        &:hover {
          background: rgba(0, 0, 0, 0.05);
          transform: rotate(180deg);
        }

        &:active {
          transform: rotate(180deg) scale(0.9);
        }
      }

      // 正在进行中会议的头部特殊样式
      &.ongoing-header {
        background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
        border-bottom: 1px solid #b7eb8f;

        span {
          color: rgb(82, 196, 26);
          display: flex;
          align-items: center;
          gap: 8px;

          &::before {
            content: '🟢';
            font-size: 12px;
            animation: pulse 2s infinite;
          }
        }

        .meeting-count {
          color: rgb(82, 196, 26);
          background: rgba(82, 196, 26, 0.1);
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;
        }
      }
    }

    .empty-container {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;

      .adm-empty {
        .adm-empty-description {
          color: #999999;
          font-size: 14px;
        }
      }
    }

    .meeting-list {
      height: calc(100vh - 240px);
      overflow-y: auto;

      .meeting-item {
        padding: 12px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
        }

        &:last-child {
          border-bottom: none;
        }
      
              .meeting-icon {
                margin-right: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 40px;
                height: 40px;
                border-radius: 8px;
                background-color: #f8f9fa;
      
                .video-icon {
                  font-size: 20px;
                  color: #1890ff;
                }
      
                .audio-icon {
                  font-size: 20px;
                  color: #52c41a;
                }
              }
      
              .meeting-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                min-width: 0; // 防止文字溢出
      
                .meeting-title {
                  font-size: 16px;
                  font-weight: 500;
                  color: #333333;
                  margin-bottom: 4px;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
      
                .meeting-time {
                  font-size: 12px;
                  color: #666666;
                  margin-bottom: 2px;
                }
      
                .meeting-participants {
                  font-size: 12px;
                  color: #999999;
                }
              }
      
        .meeting-status {
          .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;

            &.ongoing {
              background-color: #f6ffed;
              color: #52c41a;
              border: 1px solid #b7eb8f;
            }

            &.ended {
              background-color: #f5f5f5;
              color: #999999;
              border: 1px solid #d9d9d9;
            }
          }
        }
      }
    }

    // 正在进行中的会议样式 - 按照历史会议样式，使用绿色主题
    .ongoing-meeting {
      // 移除特殊样式，使用与历史会议相同的基础样式
      .meeting-icon {
        .ongoing-indicator {
          position: absolute;
          top: -2px;
          right: -2px;
          width: 8px;
          height: 8px;
          background: rgb(82, 196, 26);
          border-radius: 50%;
          // 移除脉冲动画
        }

        .video-icon {
          font-size: 20px;
          color: rgb(82, 196, 26);
        }

        .audio-icon {
          font-size: 20px;
          color: rgb(82, 196, 26);
        }
      }

      .meeting-info {
        .meeting-title {
          color: #333333;
        }

        .meeting-time {
          color: #666666;
        }
      }

      .join-button {
        background: rgb(82, 196, 26);
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        outline: none;

        &:hover {
          background: rgb(95, 208, 40);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}
