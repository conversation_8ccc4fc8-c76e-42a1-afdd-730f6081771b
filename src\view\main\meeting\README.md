# 会议组件

## 功能说明

### 主会议页面 (`/meeting/index.jsx`)
- 四个功能按钮：发起会议、加入会议、预约会议、录制文件
- 当前会议列表显示
- 空状态显示"暂无会议"

### 加入会议页面 (`/meeting/join-meeting/index.jsx`)
- 移动端优化的UI设计
- 会议号输入框
- 三个控制按钮：麦克风、摄像头、扬声器
- 进入会议按钮

## 样式特点

### 移动端适配
- 使用 `env(safe-area-inset-*)` 适配刘海屏和底部安全区域
- 响应式设计，适配不同屏幕尺寸
- 触摸友好的按钮大小和间距

### 视觉设计
- 渐变背景：从深蓝到浅蓝的线性渐变
- 半透明毛玻璃效果
- iOS风格的按钮和交互
- 简洁的装饰元素

### 交互体验
- 按钮点击反馈
- 输入框聚焦效果
- 禁用状态的视觉反馈
- 流畅的过渡动画

## 使用方法

1. 在主会议页面点击"加入会议"按钮
2. 进入加入会议页面
3. 输入会议号
4. 调整麦克风、摄像头、扬声器设置
5. 点击"进入会议"按钮

## 技术实现

- React Hooks 状态管理
- Ant Design Mobile 组件库
- SCSS 模块化样式
- 移动端触摸事件处理
